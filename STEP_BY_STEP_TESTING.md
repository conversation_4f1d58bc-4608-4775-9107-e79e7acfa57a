# Step-by-Step Testing Guide for GoodyHut Script Issues

## 🎯 **Current Status**
I've created multiple versions of the script with progressive improvements:

1. **connection-test.js** - Basic connection and assembly loading test
2. **test-agent.js** - Minimal GoodyHutHelper functionality test  
3. **enhanced-v2-agent.js** - Improved version with robust error handling
4. **enhanced-agent.js** - Original enhanced version

## 🔧 **Testing Steps**

### **Step 1: Test Basic Connection**
```bash
npm run test-connection
```

**Expected Output:**
```
[+] Connection Test Script Starting...
[+] Il2Cpp.perform() called successfully
[+] Unity Version: 2021.3.x
[+] Application: com.nexonm.dominations.adk
[+] Listing available assemblies:
[0] mscorlib
[1] Assembly-CSharp
[2] UnityEngine
[+] Assembly-CSharp loaded: Assembly-CSharp
[+] Class count: XXXX
[+] Connection test completed successfully!
```

**If This Fails:**
- Check device connection: `frida-ps -U`
- Verify app is installed: `frida-ps -U | grep dominations`
- Check Frida server is running on device

### **Step 2: Test Minimal GoodyHut Detection**
```bash
npm run spawn-test
```

**Expected Output:**
```
[+] Minimal Test Script Starting...
[+] Assembly-CSharp loaded successfully
[+] GoodyHutHelper class found: 0x...
[+] Found X GoodyHutHelper instances
[+] IsJobComplete: true
[+] CanCollect: true
[+] GetJobTimeLeft: -3600
[+] CanBuyThrough: false
[+] Hook installed successfully
[+] Minimal test completed successfully
```

**If This Fails:**
- GoodyHutHelper class doesn't exist or has different name
- Methods have different names or signatures
- Game version incompatibility

### **Step 3: Test Enhanced V2 Script**
```bash
npm run spawn-v2
```

**Expected Output:**
```
[timestamp] INFO Enhanced GoodyHutHelper Auto-Complete Script v2 Starting...
[timestamp] INFO Waiting 3000ms for game to load...
[timestamp] INFO Starting initialization...
[timestamp] INFO Attempting to load Assembly-CSharp...
[timestamp] SUCCESS Assembly-CSharp loaded successfully
[timestamp] INFO Trying class name: GoodyHutHelper
[timestamp] SUCCESS Found class: GoodyHutHelper at 0x...
[timestamp] INFO Found methods: StartCollect, DoJobBuyThrough, IsJobComplete, CanCollect, GetJobTimeLeft
[timestamp] INFO Testing instance detection...
[timestamp] INFO Found X GoodyHutHelper instances
[timestamp] INFO Instance 0 state: IDLE_READY - JobComplete: true, CanCollect: true, TimeLeft: -3600 - Ready for new collection
[timestamp] SUCCESS Initialization completed successfully!
[timestamp] INFO Use goodyHutTest.help() for available commands
```

## 🔍 **Issue Identification Matrix**

### **Issue 1: Connection Problems**
**Symptoms:**
- Script hangs without output
- "Failed to spawn" errors
- No Frida output at all

**Diagnosis Commands:**
```bash
frida-ps -U                    # List processes on device
frida -U com.nexonm.dominations.adk  # Test direct connection
adb devices                    # Check ADB connection
```

**Solutions:**
- Restart Frida server: `adb shell su -c "killall frida-server"`
- Check USB debugging enabled
- Verify correct package name

### **Issue 2: Assembly Loading Failures**
**Symptoms:**
- "Failed to load Assembly-CSharp" error
- Assembly list doesn't include Assembly-CSharp

**Diagnosis:**
- Check available assemblies in connection test
- Game might use different assembly name

**Solutions:**
- Try alternative assembly names
- Wait longer for game to load
- Check if game is fully initialized

### **Issue 3: Class Not Found**
**Symptoms:**
- "Failed to find GoodyHutHelper class" error
- Class search returns no results

**Diagnosis Commands:**
```javascript
// In Frida console after connection
Il2Cpp.domain.assembly("Assembly-CSharp").image.classes.forEach(cls => {
    if (cls.name.toLowerCase().includes("goody") || cls.name.toLowerCase().includes("hut")) {
        console.log(cls.name);
    }
});
```

**Solutions:**
- Class might have different name (GoodyHut, TreasureHut, etc.)
- Game version might have changed class names
- Class might be in different assembly

### **Issue 4: Method Signature Changes**
**Symptoms:**
- "Missing methods" warnings
- Method calls fail with errors

**Diagnosis Commands:**
```javascript
// List all methods of found class
GoodyHutHelper.methods.forEach(method => {
    console.log(`${method.name} - ${method.signature}`);
});
```

**Solutions:**
- Update method names to match current game version
- Check parameter types and counts
- Some methods might be renamed or removed

### **Issue 5: Instance Detection Issues**
**Symptoms:**
- "Found 0 GoodyHutHelper instances"
- Il2Cpp.gc.choose returns empty array

**Diagnosis:**
- No goody huts spawned in current base
- Game state not ready
- Instances not yet created

**Solutions:**
- Switch to base with goody huts
- Wait for game to fully load
- Try alternative detection methods

## 🧪 **Interactive Testing Commands**

Once any script loads successfully, use these commands:

### **V2 Script Commands:**
```javascript
goodyHutTest.testInstance()    // Test instance detection
goodyHutTest.stats()           // Show statistics
goodyHutTest.help()            // Show available commands
```

### **Manual IL2CPP Commands:**
```javascript
// Check assemblies
Il2Cpp.domain.assemblies.forEach(a => console.log(a.name));

// Find classes
const asm = Il2Cpp.domain.assembly("Assembly-CSharp").image;
// Search for goody-related classes manually

// Test instance detection
Il2Cpp.gc.choose(GoodyHutHelper).length;
```

## 📊 **Expected Results Summary**

### **Working Script Indicators:**
- ✅ Connection established without hanging
- ✅ Assembly-CSharp loads successfully  
- ✅ GoodyHutHelper class found
- ✅ Required methods available
- ✅ Instances detected (if goody huts exist)
- ✅ Hook installs without errors

### **Problem Indicators:**
- ❌ Script hangs on spawn
- ❌ Assembly loading fails
- ❌ Class not found errors
- ❌ Method missing warnings
- ❌ Zero instances found consistently
- ❌ Hook installation fails

## 🎯 **Next Steps Based on Results**

### **If Connection Test Passes:**
→ Proceed to minimal test
→ Focus on class/method detection issues

### **If Connection Test Fails:**
→ Fix device/app connection first
→ Check Frida setup and permissions

### **If Class Detection Fails:**
→ Investigate actual class names in game
→ Check for game version changes

### **If Methods Missing:**
→ Update method names for current game version
→ Adapt to new method signatures

### **If Instances Not Found:**
→ Verify goody huts exist in game
→ Check timing of detection
→ Try alternative detection approaches

## 🚀 **Recommended Testing Order**

1. **Start Simple**: `npm run test-connection`
2. **Test Core**: `npm run spawn-test`  
3. **Full Featured**: `npm run spawn-v2`
4. **Debug Issues**: Use interactive commands
5. **Iterate**: Fix issues and retest

This systematic approach will identify exactly where the problem occurs and provide a clear path to resolution.
