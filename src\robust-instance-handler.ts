import "frida-il2cpp-bridge";

Il2Cpp.perform(function(){
    console.log("🔧 Loading Robust GoodyHutHelper Instance Manager...");

    // Robust GoodyHutHelper Instance Handler
    // Handles memory access violations and validates instances

    interface ValidatedInstance {
        instance: Il2Cpp.Object;
        entityIndex: number;
        isValid: boolean;
        canCollect: boolean;
        canBuyThrough: boolean;
        state: string;
        error?: string;
    }

class GoodyHutInstanceManager {
    private validInstances: ValidatedInstance[] = [];
    private collectibleInstances: ValidatedInstance[] = [];
    
    // Safe method invocation with null pointer protection
    safeInvoke(instance: Il2Cpp.Object, methodName: string): any {
        try {
            // First check if instance is null or invalid
            if (!instance || instance.isNull()) {
                return { error: "Null instance", value: null };
            }
            
            // Try to get the method
            const method = instance.method(methodName);
            if (!method) {
                return { error: `Method ${methodName} not found`, value: null };
            }
            
            // Try to invoke the method
            const result = method.invoke();
            return { error: null, value: result };
            
        } catch (error) {
            // Catch access violations and other errors
            const errorMsg = String(error);
            if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                return { error: "Access violation - invalid instance", value: null };
            }
            return { error: `Method error: ${errorMsg}`, value: null };
        }
    }
    
    // Validate a single GoodyHutHelper instance
    validateInstance(instance: Il2Cpp.Object, entityIndex: number): ValidatedInstance {
        console.log(`Validating instance from EntityController ${entityIndex}...`);
        
        const result: ValidatedInstance = {
            instance,
            entityIndex,
            isValid: false,
            canCollect: false,
            canBuyThrough: false,
            state: "UNKNOWN"
        };
        
        // Test basic method calls
        const isJobCompleteResult = this.safeInvoke(instance, "IsJobComplete");
        if (isJobCompleteResult.error) {
            result.error = isJobCompleteResult.error;
            console.log(`❌ EntityController ${entityIndex}: ${isJobCompleteResult.error}`);
            return result;
        }
        
        const canCollectResult = this.safeInvoke(instance, "CanCollect");
        if (canCollectResult.error) {
            result.error = canCollectResult.error;
            console.log(`❌ EntityController ${entityIndex}: ${canCollectResult.error}`);
            return result;
        }
        
        const canBuyThroughResult = this.safeInvoke(instance, "CanBuyThrough");
        if (canBuyThroughResult.error) {
            result.error = canBuyThroughResult.error;
            console.log(`❌ EntityController ${entityIndex}: ${canBuyThroughResult.error}`);
            return result;
        }
        
        // If we get here, instance is valid
        result.isValid = true;
        result.canCollect = canCollectResult.value;
        result.canBuyThrough = canBuyThroughResult.value;
        
        // Determine state
        const isJobComplete = isJobCompleteResult.value;
        if (isJobComplete === true && result.canCollect === true) {
            result.state = "IDLE_READY";
        } else if (isJobComplete === false && result.canCollect === false) {
            result.state = "COLLECTING";
        } else if (isJobComplete === true && result.canCollect === false) {
            result.state = "COMPLETED_AWAITING";
        } else {
            result.state = "INCONSISTENT";
        }
        
        console.log(`✅ EntityController ${entityIndex}: Valid - ${result.state} (CanCollect: ${result.canCollect})`);
        return result;
    }
    
    // Scan all EntityController instances and validate GoodyHutHelper components
    scanAndValidateInstances(): void {
        console.log("🔍 Starting comprehensive instance validation...");
        
        this.validInstances = [];
        this.collectibleInstances = [];
        
        try {
            const EntityController = Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController");
            const entityInstances = Il2Cpp.gc.choose(EntityController);
            
            console.log(`Found ${entityInstances.length} EntityController instances`);
            console.log("Validating GoodyHutHelper components...\n");
            
            let validCount = 0;
            let invalidCount = 0;
            let collectibleCount = 0;
            
            entityInstances.forEach((entity: any, index: number) => {
                try {
                    const goodyHut = entity.field("m_goodyHut");
                    if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                        const goodyHutInstance = goodyHut.value as Il2Cpp.Object;
                        const validation = this.validateInstance(goodyHutInstance, index);
                        
                        if (validation.isValid) {
                            this.validInstances.push(validation);
                            validCount++;
                            
                            if (validation.canCollect) {
                                this.collectibleInstances.push(validation);
                                collectibleCount++;
                            }
                        } else {
                            invalidCount++;
                        }
                    }
                } catch (error) {
                    console.log(`❌ EntityController ${index}: Field access error - ${error}`);
                    invalidCount++;
                }
            });
            
            console.log("\n=== VALIDATION SUMMARY ===");
            console.log(`✅ Valid instances: ${validCount}`);
            console.log(`❌ Invalid instances: ${invalidCount}`);
            console.log(`🎯 Collectible instances: ${collectibleCount}`);
            console.log(`📊 Success rate: ${Math.round((validCount / (validCount + invalidCount)) * 100)}%`);
            
        } catch (error) {
            console.log(`❌ Error during validation: ${error}`);
        }
    }
    
    // Get detailed information about valid instances
    showValidInstances(): void {
        console.log("\n=== VALID INSTANCES DETAILS ===");
        
        this.validInstances.forEach((validation, index) => {
            console.log(`\n[${index}] EntityController ${validation.entityIndex}:`);
            console.log(`  State: ${validation.state}`);
            console.log(`  CanCollect: ${validation.canCollect}`);
            console.log(`  CanBuyThrough: ${validation.canBuyThrough}`);
            
            // Get additional details safely
            const timeLeftResult = this.safeInvoke(validation.instance, "GetJobTimeLeft");
            const healthResult = this.safeInvoke(validation.instance, "GetHealth");
            const explorationsResult = this.safeInvoke(validation.instance, "GetExplorations");
            
            if (!timeLeftResult.error) console.log(`  TimeLeft: ${timeLeftResult.value}`);
            if (!healthResult.error) console.log(`  Health: ${healthResult.value}`);
            if (!explorationsResult.error) console.log(`  Explorations: ${explorationsResult.value}`);
        });
    }
    
    // Get the best instance for collection
    getBestCollectibleInstance(): ValidatedInstance | null {
        if (this.collectibleInstances.length === 0) {
            console.log("❌ No collectible instances found");
            return null;
        }
        
        // Prioritize IDLE_READY instances
        const idleReady = this.collectibleInstances.filter(v => v.state === "IDLE_READY");
        if (idleReady.length > 0) {
            console.log(`✅ Found ${idleReady.length} IDLE_READY collectible instances`);
            return idleReady[0];
        }
        
        // Fallback to any collectible instance
        console.log(`✅ Using first collectible instance (${this.collectibleInstances[0].state})`);
        return this.collectibleInstances[0];
    }
    
    // Re-find instance by EntityController index (for post-StartCollect monitoring)
    refindInstanceByIndex(entityIndex: number): ValidatedInstance | null {
        try {
            const EntityController = Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController");
            const entityInstances = Il2Cpp.gc.choose(EntityController);

            if (entityIndex >= entityInstances.length) {
                console.log(`❌ EntityController index ${entityIndex} out of range`);
                return null;
            }

            const entity = entityInstances[entityIndex];
            const goodyHut = entity.field("m_goodyHut");

            if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                const goodyHutInstance = goodyHut.value as Il2Cpp.Object;
                return this.validateInstance(goodyHutInstance, entityIndex);
            } else {
                console.log(`❌ EntityController ${entityIndex} no longer has GoodyHutHelper component`);
                return null;
            }
        } catch (error) {
            console.log(`❌ Error re-finding instance at index ${entityIndex}: ${error}`);
            return null;
        }
    }

    // Monitor state changes by comparing before/after snapshots
    monitorStateChanges(beforeSnapshot: ValidatedInstance[], afterDelay: number = 2000): void {
        console.log(`\n🔍 Monitoring state changes after ${afterDelay}ms delay...`);

        setTimeout(() => {
            console.log("\n--- SCANNING FOR STATE CHANGES ---");

            const afterSnapshot: ValidatedInstance[] = [];
            let changedCount = 0;
            let collectingCount = 0;

            beforeSnapshot.forEach(before => {
                const after = this.refindInstanceByIndex(before.entityIndex);
                if (after && after.isValid) {
                    afterSnapshot.push(after);

                    // Check for state changes
                    if (before.state !== after.state || before.canCollect !== after.canCollect || before.canBuyThrough !== after.canBuyThrough) {
                        console.log(`🔄 EntityController ${before.entityIndex}: ${before.state} → ${after.state}`);
                        console.log(`   CanCollect: ${before.canCollect} → ${after.canCollect}`);
                        console.log(`   CanBuyThrough: ${before.canBuyThrough} → ${after.canBuyThrough}`);
                        changedCount++;

                        // Test DoJobBuyThrough if now available
                        if (!before.canBuyThrough && after.canBuyThrough) {
                            console.log(`\n🎯 CanBuyThrough became available on EntityController ${after.entityIndex}!`);
                            this.testDoJobBuyThrough(after);
                        }
                    }

                    if (after.state === "COLLECTING") {
                        collectingCount++;
                    }
                } else {
                    console.log(`❌ EntityController ${before.entityIndex}: Instance became invalid`);
                }
            });

            console.log(`\n📊 State Change Summary:`);
            console.log(`   Changed instances: ${changedCount}`);
            console.log(`   Currently collecting: ${collectingCount}`);
            console.log(`   Valid instances after: ${afterSnapshot.length}/${beforeSnapshot.length}`);

            // Store updated snapshot
            (globalThis as any).afterSnapshot = afterSnapshot;

        }, afterDelay);
    }

    // Test DoJobBuyThrough on an instance
    testDoJobBuyThrough(validation: ValidatedInstance): boolean {
        console.log(`\n💎 Testing DoJobBuyThrough on EntityController ${validation.entityIndex}...`);

        const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");
        if (buyThroughResult.error) {
            console.log(`❌ DoJobBuyThrough failed: ${buyThroughResult.error}`);
            return false;
        } else {
            console.log(`✅ DoJobBuyThrough executed successfully! Result: ${buyThroughResult.value}`);

            // Monitor state after DoJobBuyThrough
            setTimeout(() => {
                console.log("\n--- STATE AFTER DoJobBuyThrough ---");
                const afterBuyThrough = this.refindInstanceByIndex(validation.entityIndex);
                if (afterBuyThrough && afterBuyThrough.isValid) {
                    console.log(`State: ${afterBuyThrough.state}`);
                    console.log(`CanCollect: ${afterBuyThrough.canCollect}`);
                    console.log(`CanBuyThrough: ${afterBuyThrough.canBuyThrough}`);
                } else {
                    console.log("❌ Instance became invalid after DoJobBuyThrough");
                }
            }, 1000);

            return true;
        }
    }

    // Enhanced StartCollect execution with robust monitoring
    executeStartCollect(validation: ValidatedInstance): boolean {
        console.log(`\n🚀 Executing StartCollect on EntityController ${validation.entityIndex}...`);

        // Double-check the instance is still valid and collectible
        const canCollectResult = this.safeInvoke(validation.instance, "CanCollect");
        if (canCollectResult.error) {
            console.log(`❌ Instance became invalid: ${canCollectResult.error}`);
            return false;
        }

        if (!canCollectResult.value) {
            console.log(`❌ Instance no longer collectible`);
            return false;
        }

        // Take snapshot of all valid instances before StartCollect
        console.log("📸 Taking before-snapshot of all instances...");
        const beforeSnapshot = [...this.validInstances];

        // Execute StartCollect
        const startCollectResult = this.safeInvoke(validation.instance, "StartCollect");
        if (startCollectResult.error) {
            console.log(`❌ StartCollect failed: ${startCollectResult.error}`);
            return false;
        }

        console.log(`✅ StartCollect executed successfully! Result: ${startCollectResult.value}`);

        // Start monitoring for state changes (avoids direct instance access)
        this.monitorStateChanges(beforeSnapshot, 1500); // 1.5 second delay

        // Also try immediate re-scan of the specific instance
        setTimeout(() => {
            console.log(`\n🔍 Re-scanning EntityController ${validation.entityIndex}...`);
            const refoundInstance = this.refindInstanceByIndex(validation.entityIndex);
            if (refoundInstance && refoundInstance.isValid) {
                console.log(`✅ Re-found instance: ${refoundInstance.state}`);
                console.log(`   CanCollect: ${refoundInstance.canCollect}`);
                console.log(`   CanBuyThrough: ${refoundInstance.canBuyThrough}`);

                if (refoundInstance.canBuyThrough) {
                    console.log("\n🎯 CanBuyThrough is available - testing DoJobBuyThrough...");
                    this.testDoJobBuyThrough(refoundInstance);
                }
            } else {
                console.log("❌ Could not re-find instance - may have been destroyed/recreated");
            }
        }, 500); // Quick check after 0.5 seconds

        return true;
    }
}

// Create global instance manager
const instanceManager = new GoodyHutInstanceManager();

// Make it available globally
(globalThis as any).goodyManager = {
    scan: () => instanceManager.scanAndValidateInstances(),
    showValid: () => instanceManager.showValidInstances(),
    getBest: () => instanceManager.getBestCollectibleInstance(),
    startCollection: () => {
        const best = instanceManager.getBestCollectibleInstance();
        if (best) {
            return instanceManager.executeStartCollect(best);
        } else {
            console.log("❌ No collectible instances available");
            return false;
        }
    },
    help: () => {
        console.log("=== GoodyHut Instance Manager Commands ===");
        console.log("goodyManager.scan() - Scan and validate all instances");
        console.log("goodyManager.showValid() - Show details of valid instances");
        console.log("goodyManager.getBest() - Get best collectible instance");
        console.log("goodyManager.startCollection() - Start collection on best instance");
    }
};

    console.log("🔧 Robust GoodyHutHelper Instance Manager loaded!");
    console.log("Use goodyManager.help() for available commands");
    console.log("Start with: goodyManager.scan()");
});
