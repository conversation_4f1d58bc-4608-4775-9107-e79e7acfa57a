import "frida-il2cpp-bridge";

Il2Cpp.perform(function(){
    console.log("🔧 Loading Robust GoodyHutHelper Instance Manager...");

    // Install GetCollectTime hook for instant collection completion
    let getCollectTimeHookInstalled = false;
    let getCollectTimeHookCallCount = 0;

    function installGetCollectTimeHook(): void {
        try {
            console.log("🎯 Installing GetCollectTime hook for instant collection completion...");

            const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            const GoodyHutHelper = AssemblyCSharp.class("GoodyHutHelper");

            if (!GoodyHutHelper) {
                console.log("❌ GoodyHutHelper class not found");
                return;
            }

            // Find the GetCollectTime method
            const getCollectTimeMethod = GoodyHutHelper.method("GetCollectTime");
            if (!getCollectTimeMethod) {
                console.log("❌ GetCollectTime method not found");
                return;
            }

            console.log(`🎯 Found GetCollectTime method at: ${getCollectTimeMethod.handle}`);

            // Install the hook
            getCollectTimeMethod.implementation = function() {
                getCollectTimeHookCallCount++;

                // Log occasionally to avoid spam (every 10th call)
                if (getCollectTimeHookCallCount % 10 === 1) {
                    console.log(`⚡ GetCollectTime hook triggered (call #${getCollectTimeHookCallCount}) - returning 0 for instant completion`);
                }

                // Return 0 to force instant completion
                return 0.0;
            };

            getCollectTimeHookInstalled = true;
            console.log("✅ GetCollectTime hook installed successfully!");
            console.log("💡 All GoodyHut collections will now complete instantly");

        } catch (error) {
            console.log(`❌ Failed to install GetCollectTime hook: ${error}`);
            getCollectTimeHookInstalled = false;
        }
    }

    // Install the hook immediately
    installGetCollectTimeHook();

    // Robust GoodyHutHelper Instance Handler
    // Handles memory access violations and validates instances

    interface ValidatedInstance {
        instance: Il2Cpp.Object;
        entityIndex: number;
        isValid: boolean;
        canCollect: boolean;
        canBuyThrough: boolean;
        state: string;
        rewardType: string;
        rewardAmount: any;
        error?: string;
    }

class GoodyHutInstanceManager {
    private validInstances: ValidatedInstance[] = [];
    private collectibleInstances: ValidatedInstance[] = [];
    private batchSize: number = 10;
    private batchDelay: number = 3000; // 3 seconds between batches

    // DoJobBuyThrough configuration and tracking
    private attemptInstantCompletion: boolean = true;
    private buyThroughFailureCount: number = 0;
    private buyThroughSuccessCount: number = 0;
    private buyThroughFailureThreshold: number = 20; // Stop attempting after 20 consecutive failures
    
    // Safe method invocation with null pointer protection
    safeInvoke(instance: Il2Cpp.Object, methodName: string): any {
        try {
            // First check if instance is null or invalid
            if (!instance || instance.isNull()) {
                return { error: "Null instance", value: null };
            }
            
            // Try to get the method
            const method = instance.method(methodName);
            if (!method) {
                return { error: `Method ${methodName} not found`, value: null };
            }
            
            // Try to invoke the method
            const result = method.invoke();
            return { error: null, value: result };
            
        } catch (error) {
            // Catch access violations and other errors
            const errorMsg = String(error);
            if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                return { error: "Access violation - invalid instance", value: null };
            }
            return { error: `Method error: ${errorMsg}`, value: null };
        }
    }
    
    // Validate a single GoodyHutHelper instance
    validateInstance(instance: Il2Cpp.Object, entityIndex: number): ValidatedInstance {
        console.log(`Validating instance from EntityController ${entityIndex}...`);
        
        const result: ValidatedInstance = {
            instance,
            entityIndex,
            isValid: false,
            canCollect: false,
            canBuyThrough: false,
            state: "UNKNOWN",
            rewardType: "UNKNOWN",
            rewardAmount: null
        };
        
        // Test basic method calls
        const isJobCompleteResult = this.safeInvoke(instance, "IsJobComplete");
        if (isJobCompleteResult.error) {
            result.error = isJobCompleteResult.error;
            return result;
        }

        const canCollectResult = this.safeInvoke(instance, "CanCollect");
        if (canCollectResult.error) {
            result.error = canCollectResult.error;
            return result;
        }

        const canBuyThroughResult = this.safeInvoke(instance, "CanBuyThrough");
        if (canBuyThroughResult.error) {
            result.error = canBuyThroughResult.error;
            return result;
        }

        // Get reward information
        const rewardTypeResult = this.safeInvoke(instance, "GetRewardType");
        const rewardAmountResult = this.safeInvoke(instance, "GetRewardAmount");

        // If we get here, instance is valid
        result.isValid = true;
        result.canCollect = canCollectResult.value;
        result.canBuyThrough = canBuyThroughResult.value;
        result.rewardType = rewardTypeResult.error ? "UNKNOWN" : String(rewardTypeResult.value);
        result.rewardAmount = rewardAmountResult.error ? null : rewardAmountResult.value;
        
        // Determine state
        const isJobComplete = isJobCompleteResult.value;
        if (isJobComplete === true && result.canCollect === true) {
            result.state = "IDLE_READY";
        } else if (isJobComplete === false && result.canCollect === false) {
            result.state = "COLLECTING";
        } else if (isJobComplete === true && result.canCollect === false) {
            result.state = "COMPLETED_AWAITING";
        } else {
            result.state = "INCONSISTENT";
        }
        
        console.log(`✅ EntityController ${entityIndex}: Valid - ${result.state} (CanCollect: ${result.canCollect}, Reward: ${result.rewardAmount} ${result.rewardType})`);
        return result;
    }
    
    // Scan all EntityController instances and validate GoodyHutHelper components
    scanAndValidateInstances(): void {
        console.log("🔍 Starting comprehensive instance validation...");
        
        this.validInstances = [];
        this.collectibleInstances = [];
        
        try {
            const EntityController = Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController");
            const entityInstances = Il2Cpp.gc.choose(EntityController);
            
            console.log(`Found ${entityInstances.length} EntityController instances`);
            console.log("Validating GoodyHutHelper components...\n");
            
            let validCount = 0;
            let invalidCount = 0;
            let collectibleCount = 0;
            let gemsCount = 0;

            entityInstances.forEach((entity: any, index: number) => {
                try {
                    const goodyHut = entity.field("m_goodyHut");
                    if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                        const goodyHutInstance = goodyHut.value as Il2Cpp.Object;
                        const validation = this.validateInstance(goodyHutInstance, index);

                        if (validation.isValid) {
                            this.validInstances.push(validation);
                            validCount++;

                            if (validation.canCollect) {
                                this.collectibleInstances.push(validation);
                                collectibleCount++;

                                if (validation.rewardType === "GEMS") {
                                    gemsCount++;
                                }
                            }
                        } else {
                            invalidCount++;
                        }
                    }
                } catch (error) {
                    invalidCount++;
                }
            });

            console.log("\n=== VALIDATION SUMMARY ===");
            console.log(`✅ Valid instances: ${validCount}`);
            console.log(`❌ Invalid instances: ${invalidCount}`);
            console.log(`🎯 Collectible instances: ${collectibleCount}`);
            console.log(`💎 GEMS collectible instances: ${gemsCount}`);
            console.log(`📊 Success rate: ${Math.round((validCount / (validCount + invalidCount)) * 100)}%`);
            
        } catch (error) {
            console.log(`❌ Error during validation: ${error}`);
        }
    }
    
    // Get detailed information about valid instances
    showValidInstances(): void {
        console.log("\n=== VALID INSTANCES DETAILS ===");
        
        this.validInstances.forEach((validation, index) => {
            console.log(`\n[${index}] EntityController ${validation.entityIndex}:`);
            console.log(`  State: ${validation.state}`);
            console.log(`  CanCollect: ${validation.canCollect}`);
            console.log(`  CanBuyThrough: ${validation.canBuyThrough}`);
            console.log(`  Reward: ${validation.rewardAmount} ${validation.rewardType}`);

            // Get additional details safely
            const timeLeftResult = this.safeInvoke(validation.instance, "GetJobTimeLeft");
            const healthResult = this.safeInvoke(validation.instance, "GetHealth");
            const explorationsResult = this.safeInvoke(validation.instance, "GetExplorations");

            if (!timeLeftResult.error) console.log(`  TimeLeft: ${timeLeftResult.value}`);
            if (!healthResult.error) console.log(`  Health: ${healthResult.value}`);
            if (!explorationsResult.error) console.log(`  Explorations: ${explorationsResult.value}`);
        });
    }
    
    // Get the best instance for collection (GEMS rewards only)
    getBestCollectibleInstance(): ValidatedInstance | null {
        if (this.collectibleInstances.length === 0) {
            console.log("❌ No collectible instances found");
            return null;
        }

        // Filter for GEMS rewards only
        const gemsInstances = this.collectibleInstances.filter(v => v.rewardType === "GEMS");
        if (gemsInstances.length === 0) {
            console.log("❌ No collectible instances with GEMS rewards found");
            console.log(`Available rewards: ${this.collectibleInstances.map(v => `${v.rewardAmount} ${v.rewardType}`).join(", ")}`);
            return null;
        }

        console.log(`💎 Found ${gemsInstances.length} collectible instances with GEMS rewards`);

        // Prioritize IDLE_READY instances with GEMS
        const idleReadyGems = gemsInstances.filter(v => v.state === "IDLE_READY");
        if (idleReadyGems.length > 0) {
            console.log(`✅ Using IDLE_READY GEMS instance: ${idleReadyGems[0].rewardAmount} GEMS`);
            return idleReadyGems[0];
        }

        // Fallback to any GEMS collectible instance
        console.log(`✅ Using first GEMS collectible instance (${gemsInstances[0].state}): ${gemsInstances[0].rewardAmount} GEMS`);
        return gemsInstances[0];
    }
    
    // Check if DoJobBuyThrough is available and should be attempted
    private shouldAttemptInstantCompletion(validation: ValidatedInstance): boolean {
        // If we've disabled instant completion due to failures, don't attempt
        if (!this.attemptInstantCompletion) {
            return false;
        }

        // If we've hit too many failures, disable instant completion
        if (this.buyThroughFailureCount >= this.buyThroughFailureThreshold) {
            console.log(`⚠️ Disabling instant completion after ${this.buyThroughFailureCount} consecutive failures`);
            this.attemptInstantCompletion = false;
            return false;
        }

        // Check if the instance supports DoJobBuyThrough
        if (!validation.canBuyThrough) {
            return false;
        }

        return true;
    }

    // Execute DoJobBuyThrough with proper error handling and tracking
    private executeInstantCompletion(validation: ValidatedInstance): boolean {
        const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");

        if (buyThroughResult.error) {
            this.buyThroughFailureCount++;

            // Check for specific error types
            if (buyThroughResult.error.includes("abort was called")) {
                console.log(`    ⚠️ Instant completion blocked (insufficient currency or game restriction)`);
            } else {
                console.log(`    ⚠️ Instant completion failed: ${buyThroughResult.error}`);
            }
            return false;
        } else {
            this.buyThroughSuccessCount++;
            this.buyThroughFailureCount = 0; // Reset failure count on success
            console.log(`    💎 Instant completion successful!`);
            return true;
        }
    }

    // Simplified DoJobBuyThrough execution without monitoring
    testDoJobBuyThrough(validation: ValidatedInstance): boolean {
        console.log(`\n💎 Executing DoJobBuyThrough on EntityController ${validation.entityIndex}...`);

        const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");
        if (buyThroughResult.error) {
            console.log(`⚠️ DoJobBuyThrough not available: ${buyThroughResult.error}`);
            return false;
        } else {
            console.log(`✅ DoJobBuyThrough executed successfully - instant completion!`);
            return true;
        }
    }

    // Simplified StartCollect execution without aggressive monitoring
    executeStartCollect(validation: ValidatedInstance): boolean {
        console.log(`\n🚀 Executing StartCollect on EntityController ${validation.entityIndex}...`);

        // Double-check the instance is still valid and collectible
        const canCollectResult = this.safeInvoke(validation.instance, "CanCollect");
        if (canCollectResult.error) {
            console.log(`❌ Instance became invalid: ${canCollectResult.error}`);
            return false;
        }

        if (!canCollectResult.value) {
            console.log(`❌ Instance no longer collectible`);
            return false;
        }

        // Execute StartCollect
        const startCollectResult = this.safeInvoke(validation.instance, "StartCollect");
        if (startCollectResult.error) {
            console.log(`❌ StartCollect failed: ${startCollectResult.error}`);
            return false;
        }

        console.log(`✅ StartCollect executed successfully!`);

        // Try DoJobBuyThrough for instant completion
        setTimeout(() => {
            const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");
            if (buyThroughResult.error) {
                console.log(`⚠️ DoJobBuyThrough not available (this is normal if collection is already complete)`);
            } else {
                console.log(`💎 DoJobBuyThrough executed successfully - instant completion!`);
            }

            console.log(`🎉 Collection process completed for ${validation.rewardAmount} GEMS`);
        }, 500);

        return true;
    }

    // Batch processing methods

    // Validate a batch of instances and return only GEMS collectible ones
    private async validateBatch(entityInstances: any[], startIndex: number, batchSize: number): Promise<ValidatedInstance[]> {
        const batchEnd = Math.min(startIndex + batchSize, entityInstances.length);
        const validGemsInstances: ValidatedInstance[] = [];

        console.log(`🔍 Validating batch instances ${startIndex + 1}-${batchEnd}...`);

        for (let i = startIndex; i < batchEnd; i++) {
            try {
                const entity = entityInstances[i];
                const goodyHut = entity.field("m_goodyHut");

                if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                    const goodyHutInstance = goodyHut.value as Il2Cpp.Object;
                    const validation = this.validateInstance(goodyHutInstance, i);

                    // Only include valid, collectible instances with GEMS rewards
                    if (validation.isValid && validation.canCollect && validation.rewardType === "GEMS") {
                        validGemsInstances.push(validation);
                    }
                }
            } catch (error) {
                // Silently skip invalid instances
            }
        }

        return validGemsInstances;
    }

    // Collect all instances in a batch with improved success detection and instant completion handling
    private async collectBatch(batchInstances: ValidatedInstance[], batchNumber: number): Promise<{ collectionsStarted: number, instantCompletions: number }> {
        if (batchInstances.length === 0) {
            console.log(`📦 Batch ${batchNumber}: No GEMS instances to collect`);
            return { collectionsStarted: 0, instantCompletions: 0 };
        }

        console.log(`🚀 Batch ${batchNumber}: Starting collection of ${batchInstances.length} GEMS instances...`);

        let collectionsStarted = 0;
        let instantCompletions = 0;

        // Start collection on all instances in the batch
        const collectionPromises = batchInstances.map(async (validation, index) => {
            try {
                console.log(`  💎 [${index + 1}/${batchInstances.length}] Collecting ${validation.rewardAmount} GEMS from EntityController ${validation.entityIndex}`);

                // Execute StartCollect - this is the primary success metric
                const startCollectResult = this.safeInvoke(validation.instance, "StartCollect");
                if (startCollectResult.error) {
                    console.log(`    ❌ StartCollect failed: ${startCollectResult.error}`);
                    return { started: false, instantCompleted: false };
                }

                console.log(`    ✅ Collection started successfully`);
                collectionsStarted++;

                let instantCompleted = false;

                // With GetCollectTime hook, collections should complete instantly
                if (getCollectTimeHookInstalled) {
                    console.log(`    ⚡ Collection completing instantly via GetCollectTime hook`);
                    instantCompleted = true;
                    instantCompletions++; // Count hook-based instant completion
                } else {
                    // Fallback to DoJobBuyThrough if hook is not available
                    await new Promise(resolve => setTimeout(resolve, 100));

                    if (this.shouldAttemptInstantCompletion(validation)) {
                        instantCompleted = this.executeInstantCompletion(validation);
                        if (instantCompleted) {
                            instantCompletions++;
                        }
                    } else {
                        console.log(`    ⏳ Instant completion skipped (will complete naturally)`);
                    }
                }

                console.log(`    🎉 Collection initiated for ${validation.rewardAmount} GEMS`);
                return { started: true, instantCompleted };

            } catch (error) {
                console.log(`    ❌ Collection error: ${error}`);
                return { started: false, instantCompleted: false };
            }
        });

        // Wait for all collections in this batch to complete
        const results = await Promise.all(collectionPromises);
        const finalCollectionsStarted = results.filter(r => r.started).length;
        const finalInstantCompletions = results.filter(r => r.instantCompleted).length;

        console.log(`📦 Batch ${batchNumber} completed: ${finalCollectionsStarted}/${batchInstances.length} collections started, ${finalInstantCompletions}/${batchInstances.length} instant completions`);

        return { collectionsStarted: finalCollectionsStarted, instantCompletions: finalInstantCompletions };
    }

    // Main batch processing method with enhanced statistics
    async processBatchCollection(): Promise<void> {
        console.log("🔄 Starting batch collection process...");

        // Show hook status
        if (getCollectTimeHookInstalled) {
            console.log("⚡ GetCollectTime hook is active - collections will complete instantly");
        } else {
            console.log("⚠️ GetCollectTime hook not available - falling back to DoJobBuyThrough method");
        }

        // Reset tracking counters
        this.buyThroughFailureCount = 0;
        this.buyThroughSuccessCount = 0;
        this.attemptInstantCompletion = true;

        try {
            const EntityController = Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController");
            const entityInstances = Il2Cpp.gc.choose(EntityController);

            console.log(`📊 Found ${entityInstances.length} EntityController instances`);

            const totalBatches = Math.ceil(entityInstances.length / this.batchSize);
            console.log(`📦 Processing in ${totalBatches} batches of ${this.batchSize} instances each\n`);

            let totalCollectionsStarted = 0;
            let totalInstantCompletions = 0;

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const startIndex = batchIndex * this.batchSize;
                const batchNumber = batchIndex + 1;

                console.log(`\n=== BATCH ${batchNumber}/${totalBatches} ===`);

                // Phase 1: Validate batch
                const validGemsInstances = await this.validateBatch(entityInstances, startIndex, this.batchSize);
                console.log(`✅ Found ${validGemsInstances.length} valid GEMS instances in batch ${batchNumber}`);

                // Phase 2: Collect batch
                if (validGemsInstances.length > 0) {
                    const batchResults = await this.collectBatch(validGemsInstances, batchNumber);
                    totalCollectionsStarted += batchResults.collectionsStarted;
                    totalInstantCompletions += batchResults.instantCompletions;
                }

                // Show current instant completion status
                if (this.buyThroughFailureCount > 0) {
                    console.log(`⚠️ Instant completion status: ${this.buyThroughSuccessCount} successes, ${this.buyThroughFailureCount} failures`);
                }

                // Wait before processing next batch (except for the last batch)
                if (batchIndex < totalBatches - 1) {
                    console.log(`⏳ Waiting ${this.batchDelay / 1000}s before next batch...`);
                    await new Promise(resolve => setTimeout(resolve, this.batchDelay));
                }
            }

            console.log(`\n🎉 Batch collection completed!`);
            console.log(`📊 Collections started: ${totalCollectionsStarted}`);
            console.log(`💎 Instant completions: ${totalInstantCompletions}`);

            // Show completion method used
            if (getCollectTimeHookInstalled) {
                console.log(`⚡ Completion method: GetCollectTime hook (${getCollectTimeHookCallCount} hook calls)`);
            } else if (totalInstantCompletions > 0) {
                console.log(`💎 Completion method: DoJobBuyThrough`);
            } else if (totalCollectionsStarted > 0) {
                console.log(`⏳ Completion method: Natural completion over time`);
            }

            if (totalInstantCompletions === 0 && totalCollectionsStarted > 0 && !getCollectTimeHookInstalled) {
                console.log(`ℹ️ Note: Collections were started but instant completion was not available`);
                console.log(`ℹ️ Collections will complete naturally over time`);
            }

            // Final instant completion statistics (only for DoJobBuyThrough)
            if (!getCollectTimeHookInstalled && (this.buyThroughSuccessCount > 0 || this.buyThroughFailureCount > 0)) {
                const successRate = Math.round((this.buyThroughSuccessCount / (this.buyThroughSuccessCount + this.buyThroughFailureCount)) * 100);
                console.log(`📈 DoJobBuyThrough success rate: ${successRate}% (${this.buyThroughSuccessCount}/${this.buyThroughSuccessCount + this.buyThroughFailureCount})`);
            }

        } catch (error) {
            console.log(`❌ Batch collection error: ${error}`);
        }
    }

    // Configuration methods
    setInstantCompletionEnabled(enabled: boolean): void {
        this.attemptInstantCompletion = enabled;
        console.log(`💎 Instant completion ${enabled ? 'enabled' : 'disabled'}`);
    }

    getInstantCompletionStats(): { enabled: boolean, successes: number, failures: number } {
        return {
            enabled: this.attemptInstantCompletion,
            successes: this.buyThroughSuccessCount,
            failures: this.buyThroughFailureCount
        };
    }

    // Hook status methods
    getHookStatus(): { installed: boolean, callCount: number } {
        return {
            installed: getCollectTimeHookInstalled,
            callCount: getCollectTimeHookCallCount
        };
    }

    reinstallHook(): void {
        console.log("🔄 Attempting to reinstall GetCollectTime hook...");
        installGetCollectTimeHook();
    }
}

// Create global instance manager
const instanceManager = new GoodyHutInstanceManager();

// Make it available globally
(globalThis as any).goodyManager = {
    scan: () => instanceManager.scanAndValidateInstances(),
    showValid: () => instanceManager.showValidInstances(),
    getBest: () => instanceManager.getBestCollectibleInstance(),
    startCollection: () => {
        const best = instanceManager.getBestCollectibleInstance();
        if (best) {
            return instanceManager.executeStartCollect(best);
        } else {
            console.log("❌ No collectible instances available");
            return false;
        }
    },
    batchCollection: () => instanceManager.processBatchCollection(),
    enableInstantCompletion: () => instanceManager.setInstantCompletionEnabled(true),
    disableInstantCompletion: () => instanceManager.setInstantCompletionEnabled(false),
    getStats: () => instanceManager.getInstantCompletionStats(),
    getHookStatus: () => instanceManager.getHookStatus(),
    reinstallHook: () => instanceManager.reinstallHook(),
    help: () => {
        console.log("=== GoodyHut Instance Manager Commands ===");
        console.log("goodyManager.scan() - Scan and validate all instances");
        console.log("goodyManager.showValid() - Show details of valid instances");
        console.log("goodyManager.getBest() - Get best collectible instance (GEMS only)");
        console.log("goodyManager.startCollection() - Start collection on best GEMS instance");
        console.log("goodyManager.batchCollection() - Process all GEMS instances in batches of 10");
        console.log("");
        console.log("=== Instant Completion Controls ===");
        console.log("goodyManager.enableInstantCompletion() - Enable DoJobBuyThrough attempts");
        console.log("goodyManager.disableInstantCompletion() - Disable DoJobBuyThrough attempts");
        console.log("goodyManager.getStats() - Show instant completion statistics");
        console.log("");
        console.log("=== Hook Management ===");
        console.log("goodyManager.getHookStatus() - Check GetCollectTime hook status");
        console.log("goodyManager.reinstallHook() - Reinstall GetCollectTime hook if needed");
        console.log("");
        console.log("💎 NOTE: Only instances with GEMS rewards will be collected!");
        console.log("📦 BATCH MODE: Collections will start even if instant completion fails");
        console.log("⚡ HOOK MODE: GetCollectTime hook provides instant completion without premium currency");
        console.log("🔄 FALLBACK: System falls back to DoJobBuyThrough if hook is not available");
    }
};

    console.log("🔧 Robust GoodyHutHelper Instance Manager loaded!");
    console.log("Use goodyManager.help() for available commands");
    console.log("Start with: goodyManager.scan() or goodyManager.batchCollection()");
});
