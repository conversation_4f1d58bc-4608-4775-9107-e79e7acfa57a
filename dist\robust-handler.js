📦
72628 /src/robust-instance-handler.js
✄
var u=function(e,n,a,t){var s=arguments.length,r=s<3?n:t===null?t=Object.getOwnPropertyDescriptor(n,a):t,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(e,n,a,t);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(r=(s<3?o(r):s>3?o(n,a,r):o(n,a))||r);return s>3&&r&&Object.defineProperty(n,a,r),r},h;(function(e){e.application={get dataPath(){return n("get_persistentDataPath")},get identifier(){return n("get_identifier")??n("get_bundleIdentifier")??Process.mainModule.name},get version(){return n("get_version")??U(e.module).toString(16)}},_(e,"unityVersion",()=>{try{let t=e.$config.unityVersion??n("get_unityVersion");if(t!=null)return t}catch{}let a="69 6c 32 63 70 70";for(let t of e.module.enumerateRanges("r--").concat(Process.getRangeByAddress(e.module.base)))for(let{address:s}of Memory.scanSync(t.base,t.size,a)){for(;s.readU8()!=0;)s=s.sub(1);let r=x.find(s.add(1).readCString());if(r!=null)return r}f("couldn't determine the Unity version, please specify it manually")},l),_(e,"unityVersionIsBelow201830",()=>x.lt(e.unityVersion,"2018.3.0"),l),_(e,"unityVersionIsBelow202120",()=>x.lt(e.unityVersion,"2021.2.0"),l);function n(a){let t=e.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+a)),s=new NativeFunction(t,"pointer",[]);return s.isNull()?null:new e.String(s()).asNullable()?.content??null}})(h||(h={}));var h;(function(e){function n(a,t){let s={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},r=typeof a=="boolean"?"System.Boolean":typeof a=="number"?s[t??"int32"]:a instanceof Int64?"System.Int64":a instanceof UInt64?"System.UInt64":a instanceof NativePointer?s[t??"intptr"]:f(`Cannot create boxed primitive using value of type '${typeof a}'`),o=e.corlib.class(r??f(`Unknown primitive type name '${t}'`)).alloc();return(o.tryField("m_value")??o.tryField("_pointer")??f(`Could not find primitive field in class '${r}'`)).value=a,o}e.boxed=n})(h||(h={}));var h;(function(e){e.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(h||(h={}));var h;(function(e){function n(o,i){o=o??`${e.application.identifier}_${e.application.version}.cs`,i=i??e.application.dataPath??Process.getCurrentDir(),s(i);let c=`${i}/${o}`,d=new File(c,"w");for(let m of e.domain.assemblies){E(`dumping ${m.name}...`);for(let g of m.image.classes)d.write(`${g}

`)}d.flush(),d.close(),P(`dump saved to ${c}`),r()}e.dump=n;function a(o,i=!1){o=o??`${e.application.dataPath??Process.getCurrentDir()}/${e.application.identifier}_${e.application.version}`,!i&&t(o)&&f(`directory ${o} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let c of e.domain.assemblies){E(`dumping ${c.name}...`);let d=`${o}/${c.name.replaceAll(".","/")}.cs`;s(d.substring(0,d.lastIndexOf("/")));let m=new File(d,"w");for(let g of c.image.classes)m.write(`${g}

`);m.flush(),m.close()}P(`dump saved to ${o}`),r()}e.dumpTree=a;function t(o){return e.corlib.class("System.IO.Directory").method("Exists").invoke(e.string(o))}function s(o){e.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(e.string(o))}function r(){v("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(h||(h={}));var h;(function(e){function n(a="current"){let t=e.exports.threadGetCurrent();return Interceptor.attach(e.module.getExportByName("__cxa_throw"),function(s){a=="current"&&!e.exports.threadGetCurrent().equals(t)||E(new e.Object(s[0].readPointer()))})}e.installExceptionListener=n})(h||(h={}));var h;(function(e){e.exports={get alloc(){return n("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return n("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return n("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return n("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return n("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return n("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return n("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return n("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return n("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return n("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return n("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return n("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return n("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return n("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return n("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return n("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return n("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return n("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return n("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return n("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return n("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return n("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return n("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return n("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return n("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return n("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return n("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return n("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return n("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return n("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return n("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return n("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return n("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return n("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return n("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return n("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return n("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return n("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return n("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return n("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return n("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return n("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return n("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return n("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return n("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return n("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return n("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return n("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return n("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return n("il2cpp_free","void",["pointer"])},get gcCollect(){return n("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return n("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return n("il2cpp_gc_disable","void",[])},get gcEnable(){return n("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return n("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return n("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return n("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return n("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return n("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return n("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return n("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return n("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return n("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return n("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return n("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return n("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return n("il2cpp_stop_gc_world","void",[])},get getCorlib(){return n("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return n("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return n("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return n("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return n("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return n("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return n("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return n("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return n("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return n("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return n("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return n("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return n("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return n("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return n("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return n("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return n("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return n("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return n("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return n("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return n("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return n("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return n("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return n("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return n("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return n("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return n("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return n("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return n("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return n("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return n("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return n("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return n("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return n("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return n("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return n("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return n("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return n("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return n("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return n("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return n("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return n("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return n("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return n("il2cpp_string_length","int32",["pointer"])},get stringNew(){return n("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return n("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return n("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return n("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return n("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return n("il2cpp_thread_current","pointer",[])},get threadIsVm(){return n("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return n("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return n("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return n("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return n("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return n("il2cpp_type_get_type","int",["pointer"])}},V(e.exports,l),_(e,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),l);function n(a,t,s){let r=e.$config.exports?.[a]?.()??e.module.findExportByName(a)??e.memorySnapshotExports[a],o=new NativeFunction(r??NULL,t,s);return o.isNull()?new Proxy(o,{get(i,c){let d=i[c];return typeof d=="function"?d.bind(i):d},apply(){r==null?f(`couldn't resolve export ${a}`):r.isNull()&&f(`export ${a} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):o}})(h||(h={}));var h;(function(e){function n(t){return s=>s instanceof e.Class?t.isAssignableFrom(s):t.isAssignableFrom(s.class)}e.is=n;function a(t){return s=>s instanceof e.Class?s.equals(t):s.class.equals(t)}e.isExactly=a})(h||(h={}));var h;(function(e){e.gc={get heapSize(){return e.exports.gcGetHeapSize()},get isEnabled(){return!e.exports.gcIsDisabled()},get isIncremental(){return!!e.exports.gcIsIncremental()},get maxTimeSlice(){return e.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return e.exports.gcGetUsedSize()},set isEnabled(n){n?e.exports.gcEnable():e.exports.gcDisable()},set maxTimeSlice(n){e.exports.gcSetMaxTimeSlice(n)},choose(n){let a=[],t=(r,o)=>{for(let i=0;i<o;i++)a.push(new e.Object(r.add(i*Process.pointerSize).readPointer()))},s=new NativeCallback(t,"void",["pointer","int","pointer"]);if(e.unityVersionIsBelow202120){let r=new NativeCallback(()=>{},"void",[]),o=e.exports.livenessCalculationBegin(n,0,s,NULL,r,r);e.exports.livenessCalculationFromStatics(o),e.exports.livenessCalculationEnd(o)}else{let r=(c,d)=>!c.isNull()&&d.compare(0)==0?(e.free(c),NULL):e.alloc(d),o=new NativeCallback(r,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let i=e.exports.livenessAllocateStruct(n,0,s,NULL,o);e.exports.livenessCalculationFromStatics(i),e.exports.livenessFinalize(i),this.startWorld(),e.exports.livenessFreeStruct(i)}return a},collect(n){e.exports.gcCollect(n<0?0:n>2?2:n)},collectALittle(){e.exports.gcCollectALittle()},startWorld(){return e.exports.gcStartWorld()},startIncrementalCollection(){return e.exports.gcStartIncrementalCollection()},stopWorld(){return e.exports.gcStopWorld()}}})(h||(h={}));var A;(function(e){_(e,"apiLevel",()=>{let a=n("ro.build.version.sdk");return a?parseInt(a):null},l);function n(a){let t=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(t){let s=new NativeFunction(t,"void",["pointer","pointer"]),r=Memory.alloc(92).writePointer(NULL);return s(Memory.allocUtf8String(a),r),r.readCString()??void 0}}})(A||(A={}));function f(e){let n=new Error(e);throw n.name="Il2CppError",n.stack=n.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),n}function v(e){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${e}`)}function P(e){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${e}`)}function E(e){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${e}`)}function V(e,n,a=Object.getOwnPropertyDescriptors(e)){for(let t in a)a[t]=n(e,t,a[t]);return Object.defineProperties(e,a),e}function _(e,n,a,t){globalThis.Object.defineProperty(e,n,t?.(e,n,{get:a,configurable:!0})??{get:a,configurable:!0})}function O(e){let n=3735928559,a=1103547991;for(let t=0,s;t<e.length;t++)s=e.charCodeAt(t),n=Math.imul(n^s,2654435761),a=Math.imul(a^s,1597334677);return n=Math.imul(n^n>>>16,2246822507),n^=Math.imul(a^a>>>13,3266489909),a=Math.imul(a^a>>>16,2246822507),a^=Math.imul(n^n>>>13,3266489909),4294967296*(2097151&a)+(n>>>0)}function U(e){return O(e.enumerateExports().sort((n,a)=>n.name.localeCompare(a.name)).map(n=>n.name+n.address.sub(e.base)).join(""))}function l(e,n,a){let t=a.get;if(!t)throw new Error("@lazy can only be applied to getter accessors");return a.get=function(){let s=t.call(this);return Object.defineProperty(this,n,{value:s,configurable:a.configurable,enumerable:a.enumerable,writable:!1}),s},a}var b=class{handle;constructor(n){n instanceof NativePointer?this.handle=n:this.handle=n.handle}equals(n){return this.handle.equals(n.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function k(e){return Object.keys(e).reduce((n,a)=>(n[n[a]]=a,n),e)}NativePointer.prototype.offsetOf=function(e,n){n??=512;for(let a=0;n>0?a<n:a<-n;a++)if(e(n>0?this.add(a):this.sub(a)))return a;return null};function S(e){let n=[],a=Memory.alloc(Process.pointerSize),t=e(a);for(;!t.isNull();)n.push(t),t=e(a);return n}function G(e){let n=Memory.alloc(Process.pointerSize),a=e(n);if(a.isNull())return[];let t=new Array(n.readInt());for(let s=0;s<t.length;s++)t[s]=a.add(s*Process.pointerSize).readPointer();return t}function w(e){return new Proxy(e,{cache:new Map,construct(n,a){let t=a[0].toUInt32();return this.cache.has(t)||this.cache.set(t,new n(a[0])),this.cache.get(t)}})}var x;(function(e){let n=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function a(o){return o?.match(n)?.[0]}e.find=a;function t(o,i){return r(o,i)>=0}e.gte=t;function s(o,i){return r(o,i)<0}e.lt=s;function r(o,i){let c=o.match(n),d=i.match(n);for(let m=1;m<=3;m++){let g=Number(c?.[m]??-1),y=Number(d?.[m]??-1);if(g>y)return 1;if(g<y)return-1}return 0}})(x||(x={}));var h;(function(e){function n(i=Process.pointerSize){return e.exports.alloc(i)}e.alloc=n;function a(i){return e.exports.free(i)}e.free=a;function t(i,c){switch(c.enumValue){case e.Type.Enum.BOOLEAN:return!!i.readS8();case e.Type.Enum.BYTE:return i.readS8();case e.Type.Enum.UBYTE:return i.readU8();case e.Type.Enum.SHORT:return i.readS16();case e.Type.Enum.USHORT:return i.readU16();case e.Type.Enum.INT:return i.readS32();case e.Type.Enum.UINT:return i.readU32();case e.Type.Enum.CHAR:return i.readU16();case e.Type.Enum.LONG:return i.readS64();case e.Type.Enum.ULONG:return i.readU64();case e.Type.Enum.FLOAT:return i.readFloat();case e.Type.Enum.DOUBLE:return i.readDouble();case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return i.readPointer();case e.Type.Enum.POINTER:return new e.Pointer(i.readPointer(),c.class.baseType);case e.Type.Enum.VALUE_TYPE:return new e.ValueType(i,c);case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:return new e.Object(i.readPointer());case e.Type.Enum.GENERIC_INSTANCE:return c.class.isValueType?new e.ValueType(i,c):new e.Object(i.readPointer());case e.Type.Enum.STRING:return new e.String(i.readPointer());case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i.readPointer())}f(`couldn't read the value from ${i} using an unhandled or unknown type ${c.name} (${c.enumValue}), please file an issue`)}e.read=t;function s(i,c,d){switch(d.enumValue){case e.Type.Enum.BOOLEAN:return i.writeS8(+c);case e.Type.Enum.BYTE:return i.writeS8(c);case e.Type.Enum.UBYTE:return i.writeU8(c);case e.Type.Enum.SHORT:return i.writeS16(c);case e.Type.Enum.USHORT:return i.writeU16(c);case e.Type.Enum.INT:return i.writeS32(c);case e.Type.Enum.UINT:return i.writeU32(c);case e.Type.Enum.CHAR:return i.writeU16(c);case e.Type.Enum.LONG:return i.writeS64(c);case e.Type.Enum.ULONG:return i.writeU64(c);case e.Type.Enum.FLOAT:return i.writeFloat(c);case e.Type.Enum.DOUBLE:return i.writeDouble(c);case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return i.writePointer(c);case e.Type.Enum.VALUE_TYPE:return Memory.copy(i,c,d.class.valueTypeSize),i;case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:return c instanceof e.ValueType?(Memory.copy(i,c,d.class.valueTypeSize),i):i.writePointer(c)}f(`couldn't write value ${c} to ${i} using an unhandled or unknown type ${d.name} (${d.enumValue}), please file an issue`)}e.write=s;function r(i,c){if(globalThis.Array.isArray(i)){let d=Memory.alloc(c.class.valueTypeSize),m=c.class.fields.filter(g=>!g.isStatic);for(let g=0;g<m.length;g++){let y=r(i[g],m[g].type);s(d.add(m[g].offset).sub(e.Object.headerSize),y,m[g].type)}return new e.ValueType(d,c)}else if(i instanceof NativePointer){if(c.isByReference)return new e.Reference(i,c);switch(c.enumValue){case e.Type.Enum.POINTER:return new e.Pointer(i,c.class.baseType);case e.Type.Enum.STRING:return new e.String(i);case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:case e.Type.Enum.OBJECT:return new e.Object(i);case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i);default:return i}}else return c.enumValue==e.Type.Enum.BOOLEAN?!!i:c.enumValue==e.Type.Enum.VALUE_TYPE&&c.class.isEnum?r([i],c):i}e.fromFridaValue=r;function o(i){if(typeof i=="boolean")return+i;if(i instanceof e.ValueType){if(i.type.class.isEnum)return i.field("value__").value;{let c=i.type.class.fields.filter(d=>!d.isStatic).map(d=>o(d.bind(i).value));return c.length==0?[0]:c}}else return i}e.toFridaValue=o})(h||(h={}));var h;(function(e){_(e,"module",()=>a()??f("Could not find IL2CPP module"));async function n(s=!1){let r=a()??await new Promise(o=>{let[i,c]=t(),d=setTimeout(()=>{v(`after 10 seconds, IL2CPP module '${i}' has not been loaded yet, is the app running?`)},1e4),m=Process.attachModuleObserver({onAdded(g){(g.name==i||c&&g.name==c)&&(clearTimeout(d),setImmediate(()=>{o(g),m.detach()}))}})});return Reflect.defineProperty(e,"module",{value:r}),e.exports.getCorlib().isNull()?await new Promise(o=>{let i=Interceptor.attach(e.exports.initialize,{onLeave(){i.detach(),s?o(!0):setImmediate(()=>o(!1))}})}):!1}e.initialize=n;function a(){let[s,r]=t();return Process.findModuleByName(s)??Process.findModuleByName(r??s)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function t(){if(e.$config.moduleName)return[e.$config.moduleName];switch(Process.platform){case"linux":return[A.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}f(`${Process.platform} is not supported yet`)}})(h||(h={}));var h;(function(e){async function n(a,t="bind"){let s=null;try{let r=await e.initialize(t=="main");if(t=="main"&&!r)return n(()=>e.mainThread.schedule(a),"free");e.currentThread==null&&(s=e.domain.attach()),t=="bind"&&s!=null&&Script.bindWeak(globalThis,()=>s?.detach());let o=a();return o instanceof Promise?await o:o}catch(r){return Script.nextTick(o=>{throw o},r),Promise.reject(r)}finally{t=="free"&&s!=null&&s.detach()}}e.perform=n})(h||(h={}));var h;(function(e){class n{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let r=`
${this.#e.buffer.join(`
`)}
`;if(this.#d)E(r);else{let o=O(r);this.#e.history.has(o)||(this.#e.history.add(o),E(r))}this.#e.buffer.length=0}}};#u=e.mainThread.id;#d=!1;#h;#c=[];#l;#t;#n;#s;#r;#i;#o;#a;constructor(r){this.#h=r}thread(r){return this.#u=r.id,this}verbose(r){return this.#d=r,this}domain(){return this.#l=e.domain,this}assemblies(...r){return this.#t=r,this}classes(...r){return this.#n=r,this}methods(...r){return this.#s=r,this}filterAssemblies(r){return this.#r=r,this}filterClasses(r){return this.#i=r,this}filterMethods(r){return this.#o=r,this}filterParameters(r){return this.#a=r,this}and(){let r=y=>{if(this.#a==null){this.#c.push(y);return}for(let p of y.parameters)if(this.#a(p)){this.#c.push(y);break}},o=y=>{for(let p of y)r(p)},i=y=>{if(this.#o==null){o(y.methods);return}for(let p of y.methods)this.#o(p)&&r(p)},c=y=>{for(let p of y)i(p)},d=y=>{if(this.#i==null){c(y.image.classes);return}for(let p of y.image.classes)this.#i(p)&&i(p)},m=y=>{for(let p of y)d(p)},g=y=>{if(this.#r==null){m(y.assemblies);return}for(let p of y.assemblies)this.#r(p)&&d(p)};return this.#s?o(this.#s):this.#n?c(this.#n):this.#t?m(this.#t):this.#l&&g(this.#l),this.#t=void 0,this.#n=void 0,this.#s=void 0,this.#r=void 0,this.#i=void 0,this.#o=void 0,this.#a=void 0,this}attach(){for(let r of this.#c)if(!r.virtualAddress.isNull())try{this.#h(r,this.#e,this.#u)}catch(o){switch(o.message){case/unable to intercept function at \w+; please file a bug/.exec(o.message)?.input:case"already replaced this function":break;default:throw o}}}}e.Tracer=n;function a(s=!1){let r=()=>(i,c,d)=>{let m=i.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(i.virtualAddress,{onEnter(){this.threadId==d&&c.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==d&&(c.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`),c.flush())}})},o=()=>(i,c,d)=>{let m=i.relativeVirtualAddress.toString(16).padStart(8,"0"),g=+!i.isStatic|+e.unityVersionIsBelow201830,y=function(...T){if(this.threadId==d){let M=i.isStatic?void 0:new e.Parameter("this",-1,i.class.type),j=M?[M].concat(i.parameters):i.parameters;c.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m(${j.map($=>`\x1B[32m${$.name}\x1B[0m = \x1B[31m${e.fromFridaValue(T[$.position+g],$.type)}\x1B[0m`).join(", ")})`)}let N=i.nativeFunction(...T);return this.threadId==d&&(c.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m${N==null?"":` = \x1B[36m${e.fromFridaValue(N,i.returnType)}`}\x1B[0m`),c.flush()),N};i.revert();let p=new NativeCallback(y,i.returnType.fridaAlias,i.fridaSignature);Interceptor.replace(i.virtualAddress,p)};return new e.Tracer(s?o():r())}e.trace=a;function t(s){let r=e.domain.assemblies.flatMap(c=>c.image.classes.flatMap(d=>d.methods.filter(m=>!m.virtualAddress.isNull()))).sort((c,d)=>c.virtualAddress.compare(d.virtualAddress)),o=c=>{let d=0,m=r.length-1;for(;d<=m;){let g=Math.floor((d+m)/2),y=r[g].virtualAddress.compare(c);if(y==0)return r[g];y>0?m=g-1:d=g+1}return r[m]},i=()=>(c,d,m)=>{Interceptor.attach(c.virtualAddress,function(){if(this.threadId==m){let g=globalThis.Thread.backtrace(this.context,s);g.unshift(c.virtualAddress);for(let y of g)if(y.compare(e.module.base)>0&&y.compare(e.module.base.add(e.module.size))<0){let p=o(y);if(p){let T=y.sub(p.virtualAddress);T.compare(4095)<0&&d.buffer.push(`\x1B[2m0x${p.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${T.toString(16).padStart(3,"0")}\x1B[0m ${p.class.type.name}::\x1B[1m${p.name}\x1B[0m`)}}d.flush()}})};return new e.Tracer(i())}e.backtrace=t})(h||(h={}));var h;(function(e){class n extends b{static get headerSize(){return e.corlib.class("System.Array").instanceSize}get elements(){let r=e.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(o=>o.readS16()==118)??f("couldn't find the elements offset in the native array struct");return _(e.Array.prototype,"elements",function(){return new e.Pointer(this.handle.add(r),this.elementType)},l),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return e.exports.arrayGetLength(this)}get object(){return new e.Object(this)}get(s){return(s<0||s>=this.length)&&f(`cannot get element at index ${s} as the array length is ${this.length}`),this.elements.get(s)}set(s,r){(s<0||s>=this.length)&&f(`cannot set element at index ${s} as the array length is ${this.length}`),this.elements.set(s,r)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let s=0;s<this.length;s++)yield this.elements.get(s)}}u([l],n.prototype,"elementSize",null),u([l],n.prototype,"elementType",null),u([l],n.prototype,"length",null),u([l],n.prototype,"object",null),u([l],n,"headerSize",null),e.Array=n;function a(t,s){let r=typeof s=="number"?s:s.length,o=new e.Array(e.exports.arrayNew(t,r));return globalThis.Array.isArray(s)&&o.elements.write(s),o}e.array=a})(h||(h={}));var h;(function(e){let n=class extends b{get image(){if(e.exports.assemblyGetImage.isNull()){let t=this.object.tryMethod("GetType",1)?.invoke(e.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??f(`couldn't find the runtime module object of assembly ${this.name}`);return new e.Image(t.field("_impl").value)}return new e.Image(e.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let t of e.domain.object.method("GetAssemblies",1).invoke(!1))if(t.field("_mono_assembly").value.equals(this))return t;f("couldn't find the object of the native assembly struct")}};u([l],n.prototype,"name",null),u([l],n.prototype,"object",null),n=u([w],n),e.Assembly=n})(h||(h={}));var h;(function(e){let n=class extends b{get actualInstanceSize(){let t=e.corlib.class("System.String"),s=t.handle.offsetOf(r=>r.readInt()==t.instanceSize-2)??f("couldn't find the actual instance size offset in the native class struct");return _(e.Class.prototype,"actualInstanceSize",function(){return this.handle.add(s).readS32()},l),this.actualInstanceSize}get arrayClass(){return new e.Class(e.exports.classGetArrayClass(this,1))}get arrayElementSize(){return e.exports.classGetArrayElementSize(this)}get assemblyName(){return e.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new e.Class(e.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new e.Type(e.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new e.Class(e.exports.classGetElementClass(this)).asNullable()}get fields(){return S(t=>e.exports.classGetFields(this,t)).map(t=>new e.Field(t))}get flags(){return e.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let t=this.image.tryClass(this.fullName)?.asNullable();return t?.equals(this)?null:t??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let t=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(t).map(s=>new e.Class(e.exports.classFromObject(s)))}get hasReferences(){return!!e.exports.classHasReferences(this)}get hasStaticConstructor(){let t=this.tryMethod(".cctor");return t!=null&&!t.virtualAddress.isNull()}get image(){return new e.Image(e.exports.classGetImage(this))}get instanceSize(){return e.exports.classGetInstanceSize(this)}get isAbstract(){return!!e.exports.classIsAbstract(this)}get isBlittable(){return!!e.exports.classIsBlittable(this)}get isEnum(){return!!e.exports.classIsEnum(this)}get isGeneric(){return!!e.exports.classIsGeneric(this)}get isInflated(){return!!e.exports.classIsInflated(this)}get isInterface(){return!!e.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!e.exports.classIsValueType(this)}get interfaces(){return S(t=>e.exports.classGetInterfaces(this,t)).map(t=>new e.Class(t))}get methods(){return S(t=>e.exports.classGetMethods(this,t)).map(t=>new e.Method(t))}get name(){return e.exports.classGetName(this).readUtf8String()}get namespace(){return e.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return S(t=>e.exports.classGetNestedClasses(this,t)).map(t=>new e.Class(t))}get parent(){return new e.Class(e.exports.classGetParent(this)).asNullable()}get pointerClass(){return new e.Class(e.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let t=0,s=this.name;for(let r=this.name.length-1;r>0;r--){let o=s[r];if(o=="]")t++;else{if(o=="["||t==0)break;if(o==",")t++;else break}}return t}get staticFieldsData(){return e.exports.classGetStaticFieldData(this)}get valueTypeSize(){return e.exports.classGetValueTypeSize(this,NULL)}get type(){return new e.Type(e.exports.classGetType(this))}alloc(){return new e.Object(e.exports.objectNew(this))}field(t){return this.tryField(t)??f(`couldn't find field ${t} in class ${this.type.name}`)}*hierarchy(t){let s=t?.includeCurrent??!0?this:this.parent;for(;s;)yield s,s=s.parent}inflate(...t){this.isGeneric||f(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=t.length&&f(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${t.length}`);let s=t.map(i=>i.type.object),r=e.array(e.corlib.class("System.Type"),s),o=this.type.object.method("MakeGenericType",1).invoke(r);return new e.Class(e.exports.classFromObject(o))}initialize(){return e.exports.classInitialize(this),this}isAssignableFrom(t){return!!e.exports.classIsAssignableFrom(this,t)}isSubclassOf(t,s){return!!e.exports.classIsSubclassOf(this,t,+s)}method(t,s=-1){return this.tryMethod(t,s)??f(`couldn't find method ${t} in class ${this.type.name}`)}nested(t){return this.tryNested(t)??f(`couldn't find nested class ${t} in class ${this.type.name}`)}new(){let t=this.alloc(),s=Memory.alloc(Process.pointerSize);e.exports.objectInitialize(t,s);let r=s.readPointer();return r.isNull()||f(new e.Object(r).toString()),t}tryField(t){return new e.Field(e.exports.classGetFieldFromName(this,Memory.allocUtf8String(t))).asNullable()}tryMethod(t,s=-1){return new e.Method(e.exports.classGetMethodFromName(this,Memory.allocUtf8String(t),s)).asNullable()}tryNested(t){return this.nestedClasses.find(s=>s.name==t)}toString(){let t=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${t?` : ${t.map(s=>s?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(t){let s=new NativeCallback(r=>t(new e.Class(r)),"void",["pointer","pointer"]);return e.exports.classForEach(s,NULL)}};u([l],n.prototype,"arrayClass",null),u([l],n.prototype,"arrayElementSize",null),u([l],n.prototype,"assemblyName",null),u([l],n.prototype,"declaringClass",null),u([l],n.prototype,"baseType",null),u([l],n.prototype,"elementClass",null),u([l],n.prototype,"fields",null),u([l],n.prototype,"flags",null),u([l],n.prototype,"fullName",null),u([l],n.prototype,"generics",null),u([l],n.prototype,"hasReferences",null),u([l],n.prototype,"hasStaticConstructor",null),u([l],n.prototype,"image",null),u([l],n.prototype,"instanceSize",null),u([l],n.prototype,"isAbstract",null),u([l],n.prototype,"isBlittable",null),u([l],n.prototype,"isEnum",null),u([l],n.prototype,"isGeneric",null),u([l],n.prototype,"isInflated",null),u([l],n.prototype,"isInterface",null),u([l],n.prototype,"isValueType",null),u([l],n.prototype,"interfaces",null),u([l],n.prototype,"methods",null),u([l],n.prototype,"name",null),u([l],n.prototype,"namespace",null),u([l],n.prototype,"nestedClasses",null),u([l],n.prototype,"parent",null),u([l],n.prototype,"pointerClass",null),u([l],n.prototype,"rank",null),u([l],n.prototype,"staticFieldsData",null),u([l],n.prototype,"valueTypeSize",null),u([l],n.prototype,"type",null),n=u([w],n),e.Class=n})(h||(h={}));var h;(function(e){function n(a,t){let s=e.corlib.class("System.Delegate"),r=e.corlib.class("System.MulticastDelegate");s.isAssignableFrom(a)||f(`cannot create a delegate for ${a.type.name} as it's a non-delegate class`),(a.equals(s)||a.equals(r))&&f(`cannot create a delegate for neither ${s.type.name} nor ${r.type.name}, use a subclass instead`);let o=a.alloc(),i=o.handle.toString(),c=o.tryMethod("Invoke")??f(`cannot create a delegate for ${a.type.name}, there is no Invoke method`);o.method(".ctor").invoke(o,c.handle);let d=c.wrap(t);return o.field("method_ptr").value=d,o.field("invoke_impl").value=d,e._callbacksToKeepAlive[i]=d,o}e.delegate=n,e._callbacksToKeepAlive={}})(h||(h={}));var h;(function(e){let n=class extends b{get assemblies(){let t=G(s=>e.exports.domainGetAssemblies(this,s));if(t.length==0){let s=this.object.method("GetAssemblies").overload().invoke();t=globalThis.Array.from(s).map(r=>r.field("_mono_assembly").value)}return t.map(s=>new e.Assembly(s))}get object(){return e.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(t){return this.tryAssembly(t)??f(`couldn't find assembly ${t}`)}attach(){return new e.Thread(e.exports.threadAttach(this))}tryAssembly(t){return new e.Assembly(e.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(t))).asNullable()}};u([l],n.prototype,"assemblies",null),u([l],n.prototype,"object",null),n=u([w],n),e.Domain=n,_(e,"domain",()=>new e.Domain(e.exports.domainGet()),l)})(h||(h={}));var h;(function(e){class n extends b{get class(){return new e.Class(e.exports.fieldGetClass(this))}get flags(){return e.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let t=e.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return _(e.Field.prototype,"isThreadStatic",function(){return this.offset==t},l),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.fieldGetName(this).readUtf8String()}get offset(){return e.exports.fieldGetOffset(this)}get type(){return new e.Type(e.exports.fieldGetType(this))}get value(){this.isStatic||f(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let t=Memory.alloc(Process.pointerSize);return e.exports.fieldGetStaticValue(this.handle,t),e.read(t,this.type)}set value(t){this.isStatic||f(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&f(`cannot write the value of field ${this.name} as it's thread static or literal`);let s=t instanceof e.Object&&this.type.class.isValueType?t.unbox():t instanceof b?t.handle:t instanceof NativePointer?t:e.write(Memory.alloc(this.type.class.valueTypeSize),t,this.type);e.exports.fieldSetStaticValue(this.handle,s)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?e.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(t){this.isStatic&&f(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let s=this.offset-(t instanceof e.ValueType?e.Object.headerSize:0);return new Proxy(this,{get(r,o){return o=="value"?e.read(t.handle.add(s),r.type):Reflect.get(r,o)},set(r,o,i){return o=="value"?(e.write(t.handle.add(s),i,r.type),!0):Reflect.set(r,o,i)}})}}u([l],n.prototype,"class",null),u([l],n.prototype,"flags",null),u([l],n.prototype,"isLiteral",null),u([l],n.prototype,"isStatic",null),u([l],n.prototype,"isThreadStatic",null),u([l],n.prototype,"modifier",null),u([l],n.prototype,"name",null),u([l],n.prototype,"offset",null),u([l],n.prototype,"type",null),e.Field=n})(h||(h={}));var h;(function(e){class n{handle;constructor(t){this.handle=t}get target(){return new e.Object(e.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return e.exports.gcHandleFree(this.handle)}}e.GCHandle=n})(h||(h={}));var h;(function(e){let n=class extends b{get assembly(){return new e.Assembly(e.exports.imageGetAssembly(this))}get classCount(){return e.unityVersionIsBelow201830?this.classes.length:e.exports.imageGetClassCount(this)}get classes(){if(e.unityVersionIsBelow201830){let t=this.assembly.object.method("GetTypes").invoke(!1),s=globalThis.Array.from(t,o=>new e.Class(e.exports.classFromObject(o))),r=this.tryClass("<Module>");return r&&s.unshift(r),s}else return globalThis.Array.from(globalThis.Array(this.classCount),(t,s)=>new e.Class(e.exports.imageGetClass(this,s)))}get name(){return e.exports.imageGetName(this).readUtf8String()}class(t){return this.tryClass(t)??f(`couldn't find class ${t} in assembly ${this.name}`)}tryClass(t){let s=t.lastIndexOf("."),r=Memory.allocUtf8String(s==-1?"":t.slice(0,s)),o=Memory.allocUtf8String(t.slice(s+1));return new e.Class(e.exports.classFromName(this,r,o)).asNullable()}};u([l],n.prototype,"assembly",null),u([l],n.prototype,"classCount",null),u([l],n.prototype,"classes",null),u([l],n.prototype,"name",null),n=u([w],n),e.Image=n,_(e,"corlib",()=>new e.Image(e.exports.getCorlib()),l)})(h||(h={}));var h;(function(e){class n extends b{static capture(){return new e.MemorySnapshot}constructor(s=e.exports.memorySnapshotCapture()){super(s)}get classes(){return S(s=>e.exports.memorySnapshotGetClasses(this,s)).map(s=>new e.Class(s))}get objects(){return G(s=>e.exports.memorySnapshotGetObjects(this,s)).filter(s=>!s.isNull()).map(s=>new e.Object(s))}free(){e.exports.memorySnapshotFree(this)}}u([l],n.prototype,"classes",null),u([l],n.prototype,"objects",null),e.MemorySnapshot=n;function a(t){let s=e.MemorySnapshot.capture(),r=t(s);return s.free(),r}e.memorySnapshot=a})(h||(h={}));var h;(function(e){class n extends b{get class(){return new e.Class(e.exports.methodGetClass(this))}get flags(){return e.exports.methodGetFlags(this,NULL)}get implementationFlags(){let s=Memory.alloc(Process.pointerSize);return e.exports.methodGetFlags(this,s),s.readU32()}get fridaSignature(){let s=[];for(let r of this.parameters)s.push(r.type.fridaAlias);return(!this.isStatic||e.unityVersionIsBelow201830)&&s.unshift("pointer"),this.isInflated&&s.push("pointer"),s}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let s=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(s).map(r=>new e.Class(e.exports.classFromObject(r)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!e.exports.methodIsGeneric(this)}get isInflated(){return!!e.exports.methodIsInflated(this)}get isStatic(){return!e.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new e.Object(e.exports.methodGetObject(this,NULL))}get parameterCount(){return e.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(s,r)=>{let o=e.exports.methodGetParameterName(this,r).readUtf8String(),i=e.exports.methodGetParameterType(this,r);return new e.Parameter(o,r,new e.Type(i))})}get relativeVirtualAddress(){return this.virtualAddress.sub(e.module.base)}get returnType(){return new e.Type(e.exports.methodGetReturnType(this))}get virtualAddress(){let s=e.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,r=s.field("method_ptr").value,i=s.field("method").value.offsetOf(c=>c.readPointer().equals(r))??f("couldn't find the virtual address offset in the native method struct");return _(e.Method.prototype,"virtualAddress",function(){return this.handle.add(i).readPointer()},l),e.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(s){try{Interceptor.replace(this.virtualAddress,this.wrap(s))}catch(r){switch(r.message){case"access violation accessing 0x0":f(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(r.message)?.input:v(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":v(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw r}}}inflate(...s){if(!this.isGeneric||this.generics.length!=s.length){for(let c of this.overloads())if(c.isGeneric&&c.generics.length==s.length)return c.inflate(...s);f(`could not find inflatable signature of method ${this.name} with ${s.length} generic parameter(s)`)}let r=s.map(c=>c.type.object),o=e.array(e.corlib.class("System.Type"),r),i=this.object.method("MakeGenericMethod",1).invoke(o);return new e.Method(i.field("mhandle").value)}invoke(...s){return this.isStatic||f(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...s)}invokeRaw(s,...r){let o=r.map(e.toFridaValue);(!this.isStatic||e.unityVersionIsBelow201830)&&o.unshift(s),this.isInflated&&o.push(this.handle);try{let i=this.nativeFunction(...o);return e.fromFridaValue(i,this.returnType)}catch(i){switch(i==null&&f("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),i.message){case"bad argument count":f(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${r.length}`);case"expected a pointer":case"expected number":case"expected array with fields":f(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw i}}overload(...s){return this.tryOverload(...s)??f(`couldn't find overloaded method ${this.name}(${s.map(o=>o instanceof e.Class?o.type.name:o)})`)}*overloads(){for(let s of this.class.hierarchy())for(let r of s.methods)this.name==r.name&&(yield r)}parameter(s){return this.tryParameter(s)??f(`couldn't find parameter ${s} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...s){let r=s.length*1,o=s.length*2,i;e:for(let c of this.overloads()){if(c.parameterCount!=s.length)continue;let d=0,m=0;for(let g of c.parameters){let y=s[m];if(y instanceof e.Class)if(g.type.is(y.type))d+=2;else if(g.type.class.isAssignableFrom(y))d+=1;else continue e;else if(g.type.name==y)d+=2;else continue e;m++}if(!(d<r)){if(d==o)return c;if(i==null||d>i[0])i=[d,c];else if(d==i[0]){let g=0;for(let y of i[1].parameters){if(y.type.class.isAssignableFrom(c.parameters[g].type.class)){i=[d,c];continue e}g++}}}}return i?.[1]}tryParameter(s){return this.parameters.find(r=>r.name==s)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(s=>s.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(s){return this.isStatic&&f(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(r,o,i){switch(o){case"invoke":let c=s instanceof e.ValueType?r.class.isValueType?s.handle.sub(a()?e.Object.headerSize:0):f(`cannot invoke method ${r.class.type.name}::${r.name} against a value type, you must box it first`):r.class.isValueType?s.handle.add(a()?0:e.Object.headerSize):s.handle;return r.invokeRaw.bind(r,c);case"overloads":return function*(){for(let m of r[o]())m.isStatic||(yield m)};case"inflate":case"overload":case"tryOverload":let d=Reflect.get(r,o).bind(i);return function(...m){return d(...m)?.bind(s)}}return Reflect.get(r,o)}})}wrap(s){let r=+!this.isStatic|+e.unityVersionIsBelow201830;return new NativeCallback((...o)=>{let i=this.isStatic?this.class:this.class.isValueType?new e.ValueType(o[0].add(a()?e.Object.headerSize:0),this.class.type):new e.Object(o[0]),c=this.parameters.map((m,g)=>e.fromFridaValue(o[g+r],m.type)),d=s.call(i,...c);return e.toFridaValue(d)},this.returnType.fridaAlias,this.fridaSignature)}}u([l],n.prototype,"class",null),u([l],n.prototype,"flags",null),u([l],n.prototype,"implementationFlags",null),u([l],n.prototype,"fridaSignature",null),u([l],n.prototype,"generics",null),u([l],n.prototype,"isExternal",null),u([l],n.prototype,"isGeneric",null),u([l],n.prototype,"isInflated",null),u([l],n.prototype,"isStatic",null),u([l],n.prototype,"isSynchronized",null),u([l],n.prototype,"modifier",null),u([l],n.prototype,"name",null),u([l],n.prototype,"nativeFunction",null),u([l],n.prototype,"object",null),u([l],n.prototype,"parameterCount",null),u([l],n.prototype,"parameters",null),u([l],n.prototype,"relativeVirtualAddress",null),u([l],n.prototype,"returnType",null),e.Method=n;let a=()=>{let t=e.corlib.class("System.Int64").alloc();t.field("m_value").value=3735928559;let s=t.method("Equals",1).overload(t.class).invokeRaw(t,3735928559);return(a=()=>s)()}})(h||(h={}));var h;(function(e){class n extends b{static get headerSize(){return e.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&f(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(t,s,r){return s=="class"?Reflect.get(t,s).parent:s=="base"?Reflect.getOwnPropertyDescriptor(e.Object.prototype,s).get.bind(r)():Reflect.get(t,s)}})}get class(){return new e.Class(e.exports.objectGetClass(this))}get monitor(){return new e.Object.Monitor(this)}get size(){return e.exports.objectGetSize(this)}field(t){return this.tryField(t)??f(`couldn't find non-static field ${t} in hierarchy of class ${this.class.type.name}`)}method(t,s=-1){return this.tryMethod(t,s)??f(`couldn't find non-static method ${t} in hierarchy of class ${this.class.type.name}`)}ref(t){return new e.GCHandle(e.exports.gcHandleNew(this,+t))}virtualMethod(t){return new e.Method(e.exports.objectGetVirtualMethod(this,t)).bind(this)}tryField(t){let s=this.class.tryField(t);if(s?.isStatic){for(let r of this.class.hierarchy({includeCurrent:!1}))for(let o of r.fields)if(o.name==t&&!o.isStatic)return o.bind(this);return}return s?.bind(this)}tryMethod(t,s=-1){let r=this.class.tryMethod(t,s);if(r?.isStatic){for(let o of this.class.hierarchy())for(let i of o.methods)if(i.name==t&&!i.isStatic&&(s<0||i.parameterCount==s))return i.bind(this);return}return r?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new e.ValueType(e.exports.objectUnbox(this),this.class.type):f(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(t){return new e.GCHandle(e.exports.gcHandleNewWeakRef(this,+t))}}u([l],n.prototype,"class",null),u([l],n.prototype,"size",null),u([l],n,"headerSize",null),e.Object=n,function(a){class t{handle;constructor(r){this.handle=r}enter(){return e.exports.monitorEnter(this.handle)}exit(){return e.exports.monitorExit(this.handle)}pulse(){return e.exports.monitorPulse(this.handle)}pulseAll(){return e.exports.monitorPulseAll(this.handle)}tryEnter(r){return!!e.exports.monitorTryEnter(this.handle,r)}tryWait(r){return!!e.exports.monitorTryWait(this.handle,r)}wait(){return e.exports.monitorWait(this.handle)}}a.Monitor=t}(n=e.Object||(e.Object={}))})(h||(h={}));var h;(function(e){class n{name;position;type;constructor(t,s,r){this.name=t,this.position=s,this.type=r}toString(){return`${this.type.name} ${this.name}`}}e.Parameter=n})(h||(h={}));var h;(function(e){class n extends b{type;constructor(t,s){super(t),this.type=s}get(t){return e.read(this.handle.add(t*this.type.class.arrayElementSize),this.type)}read(t,s=0){let r=new globalThis.Array(t);for(let o=0;o<t;o++)r[o]=this.get(o+s);return r}set(t,s){e.write(this.handle.add(t*this.type.class.arrayElementSize),s,this.type)}toString(){return this.handle.toString()}write(t,s=0){for(let r=0;r<t.length;r++)this.set(r+s,t[r])}}e.Pointer=n})(h||(h={}));var h;(function(e){class n extends b{type;constructor(s,r){super(s),this.type=r}get value(){return e.read(this.handle,this.type)}set value(s){e.write(this.handle,s,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}e.Reference=n;function a(t,s){let r=Memory.alloc(Process.pointerSize);switch(typeof t){case"boolean":return new e.Reference(r.writeS8(+t),e.corlib.class("System.Boolean").type);case"number":switch(s?.enumValue){case e.Type.Enum.UBYTE:return new e.Reference(r.writeU8(t),s);case e.Type.Enum.BYTE:return new e.Reference(r.writeS8(t),s);case e.Type.Enum.CHAR:case e.Type.Enum.USHORT:return new e.Reference(r.writeU16(t),s);case e.Type.Enum.SHORT:return new e.Reference(r.writeS16(t),s);case e.Type.Enum.UINT:return new e.Reference(r.writeU32(t),s);case e.Type.Enum.INT:return new e.Reference(r.writeS32(t),s);case e.Type.Enum.ULONG:return new e.Reference(r.writeU64(t),s);case e.Type.Enum.LONG:return new e.Reference(r.writeS64(t),s);case e.Type.Enum.FLOAT:return new e.Reference(r.writeFloat(t),s);case e.Type.Enum.DOUBLE:return new e.Reference(r.writeDouble(t),s)}case"object":if(t instanceof e.ValueType||t instanceof e.Pointer)return new e.Reference(t.handle,t.type);if(t instanceof e.Object)return new e.Reference(r.writePointer(t),t.class.type);if(t instanceof e.String||t instanceof e.Array)return new e.Reference(r.writePointer(t),t.object.class.type);if(t instanceof NativePointer)switch(s?.enumValue){case e.Type.Enum.NUINT:case e.Type.Enum.NINT:return new e.Reference(r.writePointer(t),s)}else{if(t instanceof Int64)return new e.Reference(r.writeS64(t),e.corlib.class("System.Int64").type);if(t instanceof UInt64)return new e.Reference(r.writeU64(t),e.corlib.class("System.UInt64").type)}default:f(`couldn't create a reference to ${t} using an unhandled type ${s?.name}`)}}e.reference=a})(h||(h={}));var h;(function(e){class n extends b{get content(){return e.exports.stringGetChars(this).readUtf16String(this.length)}set content(s){let r=e.string("vfsfitvnm").handle.offsetOf(o=>o.readInt()==9)??f("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(e.String.prototype,"content",{set(o){e.exports.stringGetChars(this).writeUtf16String(o??""),this.handle.add(r).writeS32(o?.length??0)}}),this.content=s}get length(){return e.exports.stringGetLength(this)}get object(){return new e.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}e.String=n;function a(t){return new e.String(e.exports.stringNew(Memory.allocUtf8String(t??"")))}e.string=a})(h||(h={}));var h;(function(e){class n extends b{get id(){let t=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let s=Process.getCurrentThreadId(),o=ptr(t.apply(e.currentThread)).offsetOf(c=>c.readS32()==s,1024)??f("couldn't find the offset for determining the kernel id of a posix thread"),i=t;t=function(){return ptr(i.apply(this)).add(o).readS32()}}return _(e.Thread.prototype,"id",t,l),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!e.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new e.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let s=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(s.tryField("_syncContext")?.value??s.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(e.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return e.exports.threadDetach(this)}schedule(t){let s=this.synchronizationContext?.tryMethod("Post");return s==null?Process.runOnThread(this.id,t):new Promise(r=>{let o=e.delegate(e.corlib.class("System.Threading.SendOrPostCallback"),()=>{let i=t();setImmediate(()=>r(i))});Script.bindWeak(globalThis,()=>{o.field("method_ptr").value=o.field("invoke_impl").value=e.exports.domainGet}),s.invoke(o,NULL)})}tryLocalValue(t){for(let s=0;s<16;s++){let r=this.staticData.add(s*Process.pointerSize).readPointer();if(!r.isNull()){let o=new e.Object(r.readPointer()).asNullable();if(o?.class?.isSubclassOf(t,!1))return o}}}}u([l],n.prototype,"internal",null),u([l],n.prototype,"isFinalizer",null),u([l],n.prototype,"managedId",null),u([l],n.prototype,"object",null),u([l],n.prototype,"staticData",null),u([l],n.prototype,"synchronizationContext",null),e.Thread=n,_(e,"attachedThreads",()=>{if(e.exports.threadGetAttachedThreads.isNull()){let a=e.currentThread?.handle??f("Current thread is not attached to IL2CPP"),t=a.toMatchPattern(),s=[];for(let r of Process.enumerateRanges("rw-"))if(r.file==null){let o=Memory.scanSync(r.base,r.size,t);if(o.length==1){for(;;){let i=o[0].address.sub(o[0].size*s.length).readPointer();if(i.isNull()||!i.readPointer().equals(a.readPointer()))break;s.unshift(new e.Thread(i))}break}}return s}return G(e.exports.threadGetAttachedThreads).map(a=>new e.Thread(a))}),_(e,"currentThread",()=>new e.Thread(e.exports.threadGetCurrent()).asNullable()),_(e,"mainThread",()=>e.attachedThreads[0])})(h||(h={}));var h;(function(e){let n=class extends b{static get Enum(){let t=(r,o=i=>i)=>o(e.corlib.class(r)).type.enumValue,s={VOID:t("System.Void"),BOOLEAN:t("System.Boolean"),CHAR:t("System.Char"),BYTE:t("System.SByte"),UBYTE:t("System.Byte"),SHORT:t("System.Int16"),USHORT:t("System.UInt16"),INT:t("System.Int32"),UINT:t("System.UInt32"),LONG:t("System.Int64"),ULONG:t("System.UInt64"),NINT:t("System.IntPtr"),NUINT:t("System.UIntPtr"),FLOAT:t("System.Single"),DOUBLE:t("System.Double"),POINTER:t("System.IntPtr",r=>r.field("m_value")),VALUE_TYPE:t("System.Decimal"),OBJECT:t("System.Object"),STRING:t("System.String"),CLASS:t("System.Array"),ARRAY:t("System.Void",r=>r.arrayClass),NARRAY:t("System.Void",r=>new e.Class(e.exports.classGetArrayClass(r,2))),GENERIC_INSTANCE:t("System.Int32",r=>r.interfaces.find(o=>o.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:s}),k({...s,VAR:t("System.Action`1",r=>r.generics[0]),MVAR:t("System.Array",r=>r.method("AsReadOnly",1).generics[0])})}get class(){return new e.Class(e.exports.typeGetClass(this))}get fridaAlias(){function t(s){let r=s.class.fields.filter(o=>!o.isStatic);return r.length==0?["char"]:r.map(o=>o.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case e.Type.Enum.VOID:return"void";case e.Type.Enum.BOOLEAN:return"bool";case e.Type.Enum.CHAR:return"uchar";case e.Type.Enum.BYTE:return"int8";case e.Type.Enum.UBYTE:return"uint8";case e.Type.Enum.SHORT:return"int16";case e.Type.Enum.USHORT:return"uint16";case e.Type.Enum.INT:return"int32";case e.Type.Enum.UINT:return"uint32";case e.Type.Enum.LONG:return"int64";case e.Type.Enum.ULONG:return"uint64";case e.Type.Enum.FLOAT:return"float";case e.Type.Enum.DOUBLE:return"double";case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return"pointer";case e.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:t(this);case e.Type.Enum.CLASS:case e.Type.Enum.OBJECT:case e.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?t(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case e.Type.Enum.BOOLEAN:case e.Type.Enum.CHAR:case e.Type.Enum.BYTE:case e.Type.Enum.UBYTE:case e.Type.Enum.SHORT:case e.Type.Enum.USHORT:case e.Type.Enum.INT:case e.Type.Enum.UINT:case e.Type.Enum.LONG:case e.Type.Enum.ULONG:case e.Type.Enum.FLOAT:case e.Type.Enum.DOUBLE:case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return!0;default:return!1}}get name(){let t=e.exports.typeGetName(this);try{return t.readUtf8String()}finally{e.free(t)}}get object(){return new e.Object(e.exports.typeGetObject(this))}get enumValue(){return e.exports.typeGetTypeEnum(this)}is(t){return e.exports.typeEquals.isNull()?this.object.method("Equals").invoke(t.object):!!e.exports.typeEquals(this,t)}toString(){return this.name}};u([l],n.prototype,"class",null),u([l],n.prototype,"fridaAlias",null),u([l],n.prototype,"isByReference",null),u([l],n.prototype,"isPrimitive",null),u([l],n.prototype,"name",null),u([l],n.prototype,"object",null),u([l],n.prototype,"enumValue",null),u([l],n,"Enum",null),n=u([w],n),e.Type=n})(h||(h={}));var h;(function(e){class n extends b{type;constructor(t,s){super(t),this.type=s}box(){return new e.Object(e.exports.valueTypeBox(this.type.class,this))}field(t){return this.tryField(t)??f(`couldn't find non-static field ${t} in hierarchy of class ${this.type.name}`)}method(t,s=-1){return this.tryMethod(t,s)??f(`couldn't find non-static method ${t} in hierarchy of class ${this.type.name}`)}tryField(t){let s=this.type.class.tryField(t);if(s?.isStatic){for(let r of this.type.class.hierarchy())for(let o of r.fields)if(o.name==t&&!o.isStatic)return o.bind(this);return}return s?.bind(this)}tryMethod(t,s=-1){let r=this.type.class.tryMethod(t,s);if(r?.isStatic){for(let o of this.type.class.hierarchy())for(let i of o.methods)if(i.name==t&&!i.isStatic&&(s<0||i.parameterCount==s))return i.bind(this);return}return r?.bind(this)}toString(){let t=this.method("ToString",0);return this.isNull()?"null":t.class.isValueType?t.invoke().content??"null":this.box().toString()??"null"}}e.ValueType=n})(h||(h={}));globalThis.Il2Cpp=h;Il2Cpp.perform(function(){console.log("\u{1F527} Loading Robust GoodyHutHelper Instance Manager...");class e{validInstances=[];collectibleInstances=[];safeInvoke(t,s){try{if(!t||t.isNull())return{error:"Null instance",value:null};let r=t.method(s);return r?{error:null,value:r.invoke()}:{error:`Method ${s} not found`,value:null}}catch(r){let o=String(r);return o.includes("access violation")||o.includes("0x0")?{error:"Access violation - invalid instance",value:null}:{error:`Method error: ${o}`,value:null}}}validateInstance(t,s){console.log(`Validating instance from EntityController ${s}...`);let r={instance:t,entityIndex:s,isValid:!1,canCollect:!1,canBuyThrough:!1,state:"UNKNOWN"},o=this.safeInvoke(t,"IsJobComplete");if(o.error)return r.error=o.error,console.log(`\u274C EntityController ${s}: ${o.error}`),r;let i=this.safeInvoke(t,"CanCollect");if(i.error)return r.error=i.error,console.log(`\u274C EntityController ${s}: ${i.error}`),r;let c=this.safeInvoke(t,"CanBuyThrough");if(c.error)return r.error=c.error,console.log(`\u274C EntityController ${s}: ${c.error}`),r;r.isValid=!0,r.canCollect=i.value,r.canBuyThrough=c.value;let d=o.value;return d===!0&&r.canCollect===!0?r.state="IDLE_READY":d===!1&&r.canCollect===!1?r.state="COLLECTING":d===!0&&r.canCollect===!1?r.state="COMPLETED_AWAITING":r.state="INCONSISTENT",console.log(`\u2705 EntityController ${s}: Valid - ${r.state} (CanCollect: ${r.canCollect})`),r}scanAndValidateInstances(){console.log("\u{1F50D} Starting comprehensive instance validation..."),this.validInstances=[],this.collectibleInstances=[];try{let t=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),s=Il2Cpp.gc.choose(t);console.log(`Found ${s.length} EntityController instances`),console.log(`Validating GoodyHutHelper components...
`);let r=0,o=0,i=0;s.forEach((c,d)=>{try{let m=c.field("m_goodyHut");if(m&&m.value&&m.value!==null&&m.value.toString()!=="0x0"){let g=m.value,y=this.validateInstance(g,d);y.isValid?(this.validInstances.push(y),r++,y.canCollect&&(this.collectibleInstances.push(y),i++)):o++}}catch(m){console.log(`\u274C EntityController ${d}: Field access error - ${m}`),o++}}),console.log(`
=== VALIDATION SUMMARY ===`),console.log(`\u2705 Valid instances: ${r}`),console.log(`\u274C Invalid instances: ${o}`),console.log(`\u{1F3AF} Collectible instances: ${i}`),console.log(`\u{1F4CA} Success rate: ${Math.round(r/(r+o)*100)}%`)}catch(t){console.log(`\u274C Error during validation: ${t}`)}}showValidInstances(){console.log(`
=== VALID INSTANCES DETAILS ===`),this.validInstances.forEach((t,s)=>{console.log(`
[${s}] EntityController ${t.entityIndex}:`),console.log(`  State: ${t.state}`),console.log(`  CanCollect: ${t.canCollect}`),console.log(`  CanBuyThrough: ${t.canBuyThrough}`);let r=this.safeInvoke(t.instance,"GetJobTimeLeft"),o=this.safeInvoke(t.instance,"GetHealth"),i=this.safeInvoke(t.instance,"GetExplorations");r.error||console.log(`  TimeLeft: ${r.value}`),o.error||console.log(`  Health: ${o.value}`),i.error||console.log(`  Explorations: ${i.value}`)})}getBestCollectibleInstance(){if(this.collectibleInstances.length===0)return console.log("\u274C No collectible instances found"),null;let t=this.collectibleInstances.filter(s=>s.state==="IDLE_READY");return t.length>0?(console.log(`\u2705 Found ${t.length} IDLE_READY collectible instances`),t[0]):(console.log(`\u2705 Using first collectible instance (${this.collectibleInstances[0].state})`),this.collectibleInstances[0])}refindInstanceByIndex(t){try{let s=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),r=Il2Cpp.gc.choose(s);if(t>=r.length)return console.log(`\u274C EntityController index ${t} out of range`),null;let i=r[t].field("m_goodyHut");if(i&&i.value&&i.value!==null&&i.value.toString()!=="0x0"){let c=i.value;return this.validateInstance(c,t)}else return console.log(`\u274C EntityController ${t} no longer has GoodyHutHelper component`),null}catch(s){return console.log(`\u274C Error re-finding instance at index ${t}: ${s}`),null}}monitorStateChanges(t,s=2e3){console.log(`
\u{1F50D} Monitoring state changes after ${s}ms delay...`),setTimeout(()=>{console.log(`
--- SCANNING FOR STATE CHANGES ---`);let r=[],o=0,i=0;t.forEach(c=>{let d=this.refindInstanceByIndex(c.entityIndex);d&&d.isValid?(r.push(d),(c.state!==d.state||c.canCollect!==d.canCollect||c.canBuyThrough!==d.canBuyThrough)&&(console.log(`\u{1F504} EntityController ${c.entityIndex}: ${c.state} \u2192 ${d.state}`),console.log(`   CanCollect: ${c.canCollect} \u2192 ${d.canCollect}`),console.log(`   CanBuyThrough: ${c.canBuyThrough} \u2192 ${d.canBuyThrough}`),o++,!c.canBuyThrough&&d.canBuyThrough&&(console.log(`
\u{1F3AF} CanBuyThrough became available on EntityController ${d.entityIndex}!`),this.testDoJobBuyThrough(d))),d.state==="COLLECTING"&&i++):console.log(`\u274C EntityController ${c.entityIndex}: Instance became invalid`)}),console.log(`
\u{1F4CA} State Change Summary:`),console.log(`   Changed instances: ${o}`),console.log(`   Currently collecting: ${i}`),console.log(`   Valid instances after: ${r.length}/${t.length}`),globalThis.afterSnapshot=r},s)}testDoJobBuyThrough(t){console.log(`
\u{1F48E} Testing DoJobBuyThrough on EntityController ${t.entityIndex}...`);let s=this.safeInvoke(t.instance,"DoJobBuyThrough");return s.error?(console.log(`\u274C DoJobBuyThrough failed: ${s.error}`),!1):(console.log(`\u2705 DoJobBuyThrough executed successfully! Result: ${s.value}`),setTimeout(()=>{console.log(`
--- STATE AFTER DoJobBuyThrough ---`);let r=this.refindInstanceByIndex(t.entityIndex);r&&r.isValid?(console.log(`State: ${r.state}`),console.log(`CanCollect: ${r.canCollect}`),console.log(`CanBuyThrough: ${r.canBuyThrough}`)):console.log("\u274C Instance became invalid after DoJobBuyThrough")},1e3),!0)}executeStartCollect(t){console.log(`
\u{1F680} Executing StartCollect on EntityController ${t.entityIndex}...`);let s=this.safeInvoke(t.instance,"CanCollect");if(s.error)return console.log(`\u274C Instance became invalid: ${s.error}`),!1;if(!s.value)return console.log("\u274C Instance no longer collectible"),!1;console.log("\u{1F4F8} Taking before-snapshot of all instances...");let r=[...this.validInstances],o=this.safeInvoke(t.instance,"StartCollect");return o.error?(console.log(`\u274C StartCollect failed: ${o.error}`),!1):(console.log(`\u2705 StartCollect executed successfully! Result: ${o.value}`),this.monitorStateChanges(r,1500),setTimeout(()=>{console.log(`
\u{1F50D} Re-scanning EntityController ${t.entityIndex}...`);let i=this.refindInstanceByIndex(t.entityIndex);i&&i.isValid?(console.log(`\u2705 Re-found instance: ${i.state}`),console.log(`   CanCollect: ${i.canCollect}`),console.log(`   CanBuyThrough: ${i.canBuyThrough}`),i.canBuyThrough&&(console.log(`
\u{1F3AF} CanBuyThrough is available - testing DoJobBuyThrough...`),this.testDoJobBuyThrough(i))):console.log("\u274C Could not re-find instance - may have been destroyed/recreated")},500),!0)}}let n=new e;globalThis.goodyManager={scan:()=>n.scanAndValidateInstances(),showValid:()=>n.showValidInstances(),getBest:()=>n.getBestCollectibleInstance(),startCollection:()=>{let a=n.getBestCollectibleInstance();return a?n.executeStartCollect(a):(console.log("\u274C No collectible instances available"),!1)},help:()=>{console.log("=== GoodyHut Instance Manager Commands ==="),console.log("goodyManager.scan() - Scan and validate all instances"),console.log("goodyManager.showValid() - Show details of valid instances"),console.log("goodyManager.getBest() - Get best collectible instance"),console.log("goodyManager.startCollection() - Start collection on best instance")}},console.log("\u{1F527} Robust GoodyHutHelper Instance Manager loaded!"),console.log("Use goodyManager.help() for available commands"),console.log("Start with: goodyManager.scan()")});
