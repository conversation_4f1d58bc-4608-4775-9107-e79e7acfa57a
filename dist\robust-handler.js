📦
73469 /src/robust-instance-handler.js
✄
var u=function(e,n,a,t){var r=arguments.length,s=r<3?n:t===null?t=Object.getOwnPropertyDescriptor(n,a):t,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(e,n,a,t);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(s=(r<3?o(s):r>3?o(n,a,s):o(n,a))||s);return r>3&&s&&Object.defineProperty(n,a,s),s},d;(function(e){e.application={get dataPath(){return n("get_persistentDataPath")},get identifier(){return n("get_identifier")??n("get_bundleIdentifier")??Process.mainModule.name},get version(){return n("get_version")??U(e.module).toString(16)}},_(e,"unityVersion",()=>{try{let t=e.$config.unityVersion??n("get_unityVersion");if(t!=null)return t}catch{}let a="69 6c 32 63 70 70";for(let t of e.module.enumerateRanges("r--").concat(Process.getRangeByAddress(e.module.base)))for(let{address:r}of Memory.scanSync(t.base,t.size,a)){for(;r.readU8()!=0;)r=r.sub(1);let s=x.find(r.add(1).readCString());if(s!=null)return s}m("couldn't determine the Unity version, please specify it manually")},l),_(e,"unityVersionIsBelow201830",()=>x.lt(e.unityVersion,"2018.3.0"),l),_(e,"unityVersionIsBelow202120",()=>x.lt(e.unityVersion,"2021.2.0"),l);function n(a){let t=e.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+a)),r=new NativeFunction(t,"pointer",[]);return r.isNull()?null:new e.String(r()).asNullable()?.content??null}})(d||(d={}));var d;(function(e){function n(a,t){let r={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},s=typeof a=="boolean"?"System.Boolean":typeof a=="number"?r[t??"int32"]:a instanceof Int64?"System.Int64":a instanceof UInt64?"System.UInt64":a instanceof NativePointer?r[t??"intptr"]:m(`Cannot create boxed primitive using value of type '${typeof a}'`),o=e.corlib.class(s??m(`Unknown primitive type name '${t}'`)).alloc();return(o.tryField("m_value")??o.tryField("_pointer")??m(`Could not find primitive field in class '${s}'`)).value=a,o}e.boxed=n})(d||(d={}));var d;(function(e){e.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(d||(d={}));var d;(function(e){function n(o,i){o=o??`${e.application.identifier}_${e.application.version}.cs`,i=i??e.application.dataPath??Process.getCurrentDir(),r(i);let c=`${i}/${o}`,h=new File(c,"w");for(let f of e.domain.assemblies){w(`dumping ${f.name}...`);for(let y of f.image.classes)h.write(`${y}

`)}h.flush(),h.close(),P(`dump saved to ${c}`),s()}e.dump=n;function a(o,i=!1){o=o??`${e.application.dataPath??Process.getCurrentDir()}/${e.application.identifier}_${e.application.version}`,!i&&t(o)&&m(`directory ${o} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let c of e.domain.assemblies){w(`dumping ${c.name}...`);let h=`${o}/${c.name.replaceAll(".","/")}.cs`;r(h.substring(0,h.lastIndexOf("/")));let f=new File(h,"w");for(let y of c.image.classes)f.write(`${y}

`);f.flush(),f.close()}P(`dump saved to ${o}`),s()}e.dumpTree=a;function t(o){return e.corlib.class("System.IO.Directory").method("Exists").invoke(e.string(o))}function r(o){e.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(e.string(o))}function s(){v("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(d||(d={}));var d;(function(e){function n(a="current"){let t=e.exports.threadGetCurrent();return Interceptor.attach(e.module.getExportByName("__cxa_throw"),function(r){a=="current"&&!e.exports.threadGetCurrent().equals(t)||w(new e.Object(r[0].readPointer()))})}e.installExceptionListener=n})(d||(d={}));var d;(function(e){e.exports={get alloc(){return n("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return n("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return n("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return n("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return n("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return n("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return n("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return n("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return n("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return n("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return n("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return n("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return n("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return n("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return n("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return n("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return n("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return n("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return n("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return n("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return n("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return n("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return n("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return n("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return n("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return n("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return n("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return n("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return n("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return n("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return n("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return n("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return n("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return n("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return n("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return n("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return n("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return n("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return n("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return n("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return n("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return n("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return n("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return n("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return n("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return n("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return n("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return n("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return n("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return n("il2cpp_free","void",["pointer"])},get gcCollect(){return n("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return n("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return n("il2cpp_gc_disable","void",[])},get gcEnable(){return n("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return n("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return n("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return n("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return n("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return n("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return n("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return n("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return n("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return n("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return n("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return n("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return n("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return n("il2cpp_stop_gc_world","void",[])},get getCorlib(){return n("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return n("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return n("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return n("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return n("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return n("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return n("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return n("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return n("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return n("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return n("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return n("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return n("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return n("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return n("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return n("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return n("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return n("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return n("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return n("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return n("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return n("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return n("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return n("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return n("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return n("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return n("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return n("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return n("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return n("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return n("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return n("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return n("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return n("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return n("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return n("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return n("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return n("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return n("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return n("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return n("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return n("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return n("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return n("il2cpp_string_length","int32",["pointer"])},get stringNew(){return n("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return n("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return n("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return n("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return n("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return n("il2cpp_thread_current","pointer",[])},get threadIsVm(){return n("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return n("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return n("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return n("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return n("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return n("il2cpp_type_get_type","int",["pointer"])}},V(e.exports,l),_(e,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),l);function n(a,t,r){let s=e.$config.exports?.[a]?.()??e.module.findExportByName(a)??e.memorySnapshotExports[a],o=new NativeFunction(s??NULL,t,r);return o.isNull()?new Proxy(o,{get(i,c){let h=i[c];return typeof h=="function"?h.bind(i):h},apply(){s==null?m(`couldn't resolve export ${a}`):s.isNull()&&m(`export ${a} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):o}})(d||(d={}));var d;(function(e){function n(t){return r=>r instanceof e.Class?t.isAssignableFrom(r):t.isAssignableFrom(r.class)}e.is=n;function a(t){return r=>r instanceof e.Class?r.equals(t):r.class.equals(t)}e.isExactly=a})(d||(d={}));var d;(function(e){e.gc={get heapSize(){return e.exports.gcGetHeapSize()},get isEnabled(){return!e.exports.gcIsDisabled()},get isIncremental(){return!!e.exports.gcIsIncremental()},get maxTimeSlice(){return e.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return e.exports.gcGetUsedSize()},set isEnabled(n){n?e.exports.gcEnable():e.exports.gcDisable()},set maxTimeSlice(n){e.exports.gcSetMaxTimeSlice(n)},choose(n){let a=[],t=(s,o)=>{for(let i=0;i<o;i++)a.push(new e.Object(s.add(i*Process.pointerSize).readPointer()))},r=new NativeCallback(t,"void",["pointer","int","pointer"]);if(e.unityVersionIsBelow202120){let s=new NativeCallback(()=>{},"void",[]),o=e.exports.livenessCalculationBegin(n,0,r,NULL,s,s);e.exports.livenessCalculationFromStatics(o),e.exports.livenessCalculationEnd(o)}else{let s=(c,h)=>!c.isNull()&&h.compare(0)==0?(e.free(c),NULL):e.alloc(h),o=new NativeCallback(s,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let i=e.exports.livenessAllocateStruct(n,0,r,NULL,o);e.exports.livenessCalculationFromStatics(i),e.exports.livenessFinalize(i),this.startWorld(),e.exports.livenessFreeStruct(i)}return a},collect(n){e.exports.gcCollect(n<0?0:n>2?2:n)},collectALittle(){e.exports.gcCollectALittle()},startWorld(){return e.exports.gcStartWorld()},startIncrementalCollection(){return e.exports.gcStartIncrementalCollection()},stopWorld(){return e.exports.gcStopWorld()}}})(d||(d={}));var A;(function(e){_(e,"apiLevel",()=>{let a=n("ro.build.version.sdk");return a?parseInt(a):null},l);function n(a){let t=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(t){let r=new NativeFunction(t,"void",["pointer","pointer"]),s=Memory.alloc(92).writePointer(NULL);return r(Memory.allocUtf8String(a),s),s.readCString()??void 0}}})(A||(A={}));function m(e){let n=new Error(e);throw n.name="Il2CppError",n.stack=n.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),n}function v(e){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${e}`)}function P(e){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${e}`)}function w(e){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${e}`)}function V(e,n,a=Object.getOwnPropertyDescriptors(e)){for(let t in a)a[t]=n(e,t,a[t]);return Object.defineProperties(e,a),e}function _(e,n,a,t){globalThis.Object.defineProperty(e,n,t?.(e,n,{get:a,configurable:!0})??{get:a,configurable:!0})}function O(e){let n=3735928559,a=1103547991;for(let t=0,r;t<e.length;t++)r=e.charCodeAt(t),n=Math.imul(n^r,2654435761),a=Math.imul(a^r,1597334677);return n=Math.imul(n^n>>>16,2246822507),n^=Math.imul(a^a>>>13,3266489909),a=Math.imul(a^a>>>16,2246822507),a^=Math.imul(n^n>>>13,3266489909),4294967296*(2097151&a)+(n>>>0)}function U(e){return O(e.enumerateExports().sort((n,a)=>n.name.localeCompare(a.name)).map(n=>n.name+n.address.sub(e.base)).join(""))}function l(e,n,a){let t=a.get;if(!t)throw new Error("@lazy can only be applied to getter accessors");return a.get=function(){let r=t.call(this);return Object.defineProperty(this,n,{value:r,configurable:a.configurable,enumerable:a.enumerable,writable:!1}),r},a}var b=class{handle;constructor(n){n instanceof NativePointer?this.handle=n:this.handle=n.handle}equals(n){return this.handle.equals(n.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function k(e){return Object.keys(e).reduce((n,a)=>(n[n[a]]=a,n),e)}NativePointer.prototype.offsetOf=function(e,n){n??=512;for(let a=0;n>0?a<n:a<-n;a++)if(e(n>0?this.add(a):this.sub(a)))return a;return null};function S(e){let n=[],a=Memory.alloc(Process.pointerSize),t=e(a);for(;!t.isNull();)n.push(t),t=e(a);return n}function G(e){let n=Memory.alloc(Process.pointerSize),a=e(n);if(a.isNull())return[];let t=new Array(n.readInt());for(let r=0;r<t.length;r++)t[r]=a.add(r*Process.pointerSize).readPointer();return t}function E(e){return new Proxy(e,{cache:new Map,construct(n,a){let t=a[0].toUInt32();return this.cache.has(t)||this.cache.set(t,new n(a[0])),this.cache.get(t)}})}var x;(function(e){let n=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function a(o){return o?.match(n)?.[0]}e.find=a;function t(o,i){return s(o,i)>=0}e.gte=t;function r(o,i){return s(o,i)<0}e.lt=r;function s(o,i){let c=o.match(n),h=i.match(n);for(let f=1;f<=3;f++){let y=Number(c?.[f]??-1),g=Number(h?.[f]??-1);if(y>g)return 1;if(y<g)return-1}return 0}})(x||(x={}));var d;(function(e){function n(i=Process.pointerSize){return e.exports.alloc(i)}e.alloc=n;function a(i){return e.exports.free(i)}e.free=a;function t(i,c){switch(c.enumValue){case e.Type.Enum.BOOLEAN:return!!i.readS8();case e.Type.Enum.BYTE:return i.readS8();case e.Type.Enum.UBYTE:return i.readU8();case e.Type.Enum.SHORT:return i.readS16();case e.Type.Enum.USHORT:return i.readU16();case e.Type.Enum.INT:return i.readS32();case e.Type.Enum.UINT:return i.readU32();case e.Type.Enum.CHAR:return i.readU16();case e.Type.Enum.LONG:return i.readS64();case e.Type.Enum.ULONG:return i.readU64();case e.Type.Enum.FLOAT:return i.readFloat();case e.Type.Enum.DOUBLE:return i.readDouble();case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return i.readPointer();case e.Type.Enum.POINTER:return new e.Pointer(i.readPointer(),c.class.baseType);case e.Type.Enum.VALUE_TYPE:return new e.ValueType(i,c);case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:return new e.Object(i.readPointer());case e.Type.Enum.GENERIC_INSTANCE:return c.class.isValueType?new e.ValueType(i,c):new e.Object(i.readPointer());case e.Type.Enum.STRING:return new e.String(i.readPointer());case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i.readPointer())}m(`couldn't read the value from ${i} using an unhandled or unknown type ${c.name} (${c.enumValue}), please file an issue`)}e.read=t;function r(i,c,h){switch(h.enumValue){case e.Type.Enum.BOOLEAN:return i.writeS8(+c);case e.Type.Enum.BYTE:return i.writeS8(c);case e.Type.Enum.UBYTE:return i.writeU8(c);case e.Type.Enum.SHORT:return i.writeS16(c);case e.Type.Enum.USHORT:return i.writeU16(c);case e.Type.Enum.INT:return i.writeS32(c);case e.Type.Enum.UINT:return i.writeU32(c);case e.Type.Enum.CHAR:return i.writeU16(c);case e.Type.Enum.LONG:return i.writeS64(c);case e.Type.Enum.ULONG:return i.writeU64(c);case e.Type.Enum.FLOAT:return i.writeFloat(c);case e.Type.Enum.DOUBLE:return i.writeDouble(c);case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return i.writePointer(c);case e.Type.Enum.VALUE_TYPE:return Memory.copy(i,c,h.class.valueTypeSize),i;case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:return c instanceof e.ValueType?(Memory.copy(i,c,h.class.valueTypeSize),i):i.writePointer(c)}m(`couldn't write value ${c} to ${i} using an unhandled or unknown type ${h.name} (${h.enumValue}), please file an issue`)}e.write=r;function s(i,c){if(globalThis.Array.isArray(i)){let h=Memory.alloc(c.class.valueTypeSize),f=c.class.fields.filter(y=>!y.isStatic);for(let y=0;y<f.length;y++){let g=s(i[y],f[y].type);r(h.add(f[y].offset).sub(e.Object.headerSize),g,f[y].type)}return new e.ValueType(h,c)}else if(i instanceof NativePointer){if(c.isByReference)return new e.Reference(i,c);switch(c.enumValue){case e.Type.Enum.POINTER:return new e.Pointer(i,c.class.baseType);case e.Type.Enum.STRING:return new e.String(i);case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:case e.Type.Enum.OBJECT:return new e.Object(i);case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i);default:return i}}else return c.enumValue==e.Type.Enum.BOOLEAN?!!i:c.enumValue==e.Type.Enum.VALUE_TYPE&&c.class.isEnum?s([i],c):i}e.fromFridaValue=s;function o(i){if(typeof i=="boolean")return+i;if(i instanceof e.ValueType){if(i.type.class.isEnum)return i.field("value__").value;{let c=i.type.class.fields.filter(h=>!h.isStatic).map(h=>o(h.bind(i).value));return c.length==0?[0]:c}}else return i}e.toFridaValue=o})(d||(d={}));var d;(function(e){_(e,"module",()=>a()??m("Could not find IL2CPP module"));async function n(r=!1){let s=a()??await new Promise(o=>{let[i,c]=t(),h=setTimeout(()=>{v(`after 10 seconds, IL2CPP module '${i}' has not been loaded yet, is the app running?`)},1e4),f=Process.attachModuleObserver({onAdded(y){(y.name==i||c&&y.name==c)&&(clearTimeout(h),setImmediate(()=>{o(y),f.detach()}))}})});return Reflect.defineProperty(e,"module",{value:s}),e.exports.getCorlib().isNull()?await new Promise(o=>{let i=Interceptor.attach(e.exports.initialize,{onLeave(){i.detach(),r?o(!0):setImmediate(()=>o(!1))}})}):!1}e.initialize=n;function a(){let[r,s]=t();return Process.findModuleByName(r)??Process.findModuleByName(s??r)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function t(){if(e.$config.moduleName)return[e.$config.moduleName];switch(Process.platform){case"linux":return[A.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}m(`${Process.platform} is not supported yet`)}})(d||(d={}));var d;(function(e){async function n(a,t="bind"){let r=null;try{let s=await e.initialize(t=="main");if(t=="main"&&!s)return n(()=>e.mainThread.schedule(a),"free");e.currentThread==null&&(r=e.domain.attach()),t=="bind"&&r!=null&&Script.bindWeak(globalThis,()=>r?.detach());let o=a();return o instanceof Promise?await o:o}catch(s){return Script.nextTick(o=>{throw o},s),Promise.reject(s)}finally{t=="free"&&r!=null&&r.detach()}}e.perform=n})(d||(d={}));var d;(function(e){class n{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let s=`
${this.#e.buffer.join(`
`)}
`;if(this.#d)w(s);else{let o=O(s);this.#e.history.has(o)||(this.#e.history.add(o),w(s))}this.#e.buffer.length=0}}};#u=e.mainThread.id;#d=!1;#h;#c=[];#l;#t;#n;#r;#s;#i;#o;#a;constructor(s){this.#h=s}thread(s){return this.#u=s.id,this}verbose(s){return this.#d=s,this}domain(){return this.#l=e.domain,this}assemblies(...s){return this.#t=s,this}classes(...s){return this.#n=s,this}methods(...s){return this.#r=s,this}filterAssemblies(s){return this.#s=s,this}filterClasses(s){return this.#i=s,this}filterMethods(s){return this.#o=s,this}filterParameters(s){return this.#a=s,this}and(){let s=g=>{if(this.#a==null){this.#c.push(g);return}for(let p of g.parameters)if(this.#a(p)){this.#c.push(g);break}},o=g=>{for(let p of g)s(p)},i=g=>{if(this.#o==null){o(g.methods);return}for(let p of g.methods)this.#o(p)&&s(p)},c=g=>{for(let p of g)i(p)},h=g=>{if(this.#i==null){c(g.image.classes);return}for(let p of g.image.classes)this.#i(p)&&i(p)},f=g=>{for(let p of g)h(p)},y=g=>{if(this.#s==null){f(g.assemblies);return}for(let p of g.assemblies)this.#s(p)&&h(p)};return this.#r?o(this.#r):this.#n?c(this.#n):this.#t?f(this.#t):this.#l&&y(this.#l),this.#t=void 0,this.#n=void 0,this.#r=void 0,this.#s=void 0,this.#i=void 0,this.#o=void 0,this.#a=void 0,this}attach(){for(let s of this.#c)if(!s.virtualAddress.isNull())try{this.#h(s,this.#e,this.#u)}catch(o){switch(o.message){case/unable to intercept function at \w+; please file a bug/.exec(o.message)?.input:case"already replaced this function":break;default:throw o}}}}e.Tracer=n;function a(r=!1){let s=()=>(i,c,h)=>{let f=i.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(i.virtualAddress,{onEnter(){this.threadId==h&&c.buffer.push(`\x1B[2m0x${f}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==h&&(c.buffer.push(`\x1B[2m0x${f}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`),c.flush())}})},o=()=>(i,c,h)=>{let f=i.relativeVirtualAddress.toString(16).padStart(8,"0"),y=+!i.isStatic|+e.unityVersionIsBelow201830,g=function(...T){if(this.threadId==h){let M=i.isStatic?void 0:new e.Parameter("this",-1,i.class.type),R=M?[M].concat(i.parameters):i.parameters;c.buffer.push(`\x1B[2m0x${f}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m(${R.map($=>`\x1B[32m${$.name}\x1B[0m = \x1B[31m${e.fromFridaValue(T[$.position+y],$.type)}\x1B[0m`).join(", ")})`)}let N=i.nativeFunction(...T);return this.threadId==h&&(c.buffer.push(`\x1B[2m0x${f}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m${N==null?"":` = \x1B[36m${e.fromFridaValue(N,i.returnType)}`}\x1B[0m`),c.flush()),N};i.revert();let p=new NativeCallback(g,i.returnType.fridaAlias,i.fridaSignature);Interceptor.replace(i.virtualAddress,p)};return new e.Tracer(r?o():s())}e.trace=a;function t(r){let s=e.domain.assemblies.flatMap(c=>c.image.classes.flatMap(h=>h.methods.filter(f=>!f.virtualAddress.isNull()))).sort((c,h)=>c.virtualAddress.compare(h.virtualAddress)),o=c=>{let h=0,f=s.length-1;for(;h<=f;){let y=Math.floor((h+f)/2),g=s[y].virtualAddress.compare(c);if(g==0)return s[y];g>0?f=y-1:h=y+1}return s[f]},i=()=>(c,h,f)=>{Interceptor.attach(c.virtualAddress,function(){if(this.threadId==f){let y=globalThis.Thread.backtrace(this.context,r);y.unshift(c.virtualAddress);for(let g of y)if(g.compare(e.module.base)>0&&g.compare(e.module.base.add(e.module.size))<0){let p=o(g);if(p){let T=g.sub(p.virtualAddress);T.compare(4095)<0&&h.buffer.push(`\x1B[2m0x${p.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${T.toString(16).padStart(3,"0")}\x1B[0m ${p.class.type.name}::\x1B[1m${p.name}\x1B[0m`)}}h.flush()}})};return new e.Tracer(i())}e.backtrace=t})(d||(d={}));var d;(function(e){class n extends b{static get headerSize(){return e.corlib.class("System.Array").instanceSize}get elements(){let s=e.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(o=>o.readS16()==118)??m("couldn't find the elements offset in the native array struct");return _(e.Array.prototype,"elements",function(){return new e.Pointer(this.handle.add(s),this.elementType)},l),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return e.exports.arrayGetLength(this)}get object(){return new e.Object(this)}get(r){return(r<0||r>=this.length)&&m(`cannot get element at index ${r} as the array length is ${this.length}`),this.elements.get(r)}set(r,s){(r<0||r>=this.length)&&m(`cannot set element at index ${r} as the array length is ${this.length}`),this.elements.set(r,s)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let r=0;r<this.length;r++)yield this.elements.get(r)}}u([l],n.prototype,"elementSize",null),u([l],n.prototype,"elementType",null),u([l],n.prototype,"length",null),u([l],n.prototype,"object",null),u([l],n,"headerSize",null),e.Array=n;function a(t,r){let s=typeof r=="number"?r:r.length,o=new e.Array(e.exports.arrayNew(t,s));return globalThis.Array.isArray(r)&&o.elements.write(r),o}e.array=a})(d||(d={}));var d;(function(e){let n=class extends b{get image(){if(e.exports.assemblyGetImage.isNull()){let t=this.object.tryMethod("GetType",1)?.invoke(e.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??m(`couldn't find the runtime module object of assembly ${this.name}`);return new e.Image(t.field("_impl").value)}return new e.Image(e.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let t of e.domain.object.method("GetAssemblies",1).invoke(!1))if(t.field("_mono_assembly").value.equals(this))return t;m("couldn't find the object of the native assembly struct")}};u([l],n.prototype,"name",null),u([l],n.prototype,"object",null),n=u([E],n),e.Assembly=n})(d||(d={}));var d;(function(e){let n=class extends b{get actualInstanceSize(){let t=e.corlib.class("System.String"),r=t.handle.offsetOf(s=>s.readInt()==t.instanceSize-2)??m("couldn't find the actual instance size offset in the native class struct");return _(e.Class.prototype,"actualInstanceSize",function(){return this.handle.add(r).readS32()},l),this.actualInstanceSize}get arrayClass(){return new e.Class(e.exports.classGetArrayClass(this,1))}get arrayElementSize(){return e.exports.classGetArrayElementSize(this)}get assemblyName(){return e.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new e.Class(e.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new e.Type(e.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new e.Class(e.exports.classGetElementClass(this)).asNullable()}get fields(){return S(t=>e.exports.classGetFields(this,t)).map(t=>new e.Field(t))}get flags(){return e.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let t=this.image.tryClass(this.fullName)?.asNullable();return t?.equals(this)?null:t??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let t=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(t).map(r=>new e.Class(e.exports.classFromObject(r)))}get hasReferences(){return!!e.exports.classHasReferences(this)}get hasStaticConstructor(){let t=this.tryMethod(".cctor");return t!=null&&!t.virtualAddress.isNull()}get image(){return new e.Image(e.exports.classGetImage(this))}get instanceSize(){return e.exports.classGetInstanceSize(this)}get isAbstract(){return!!e.exports.classIsAbstract(this)}get isBlittable(){return!!e.exports.classIsBlittable(this)}get isEnum(){return!!e.exports.classIsEnum(this)}get isGeneric(){return!!e.exports.classIsGeneric(this)}get isInflated(){return!!e.exports.classIsInflated(this)}get isInterface(){return!!e.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!e.exports.classIsValueType(this)}get interfaces(){return S(t=>e.exports.classGetInterfaces(this,t)).map(t=>new e.Class(t))}get methods(){return S(t=>e.exports.classGetMethods(this,t)).map(t=>new e.Method(t))}get name(){return e.exports.classGetName(this).readUtf8String()}get namespace(){return e.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return S(t=>e.exports.classGetNestedClasses(this,t)).map(t=>new e.Class(t))}get parent(){return new e.Class(e.exports.classGetParent(this)).asNullable()}get pointerClass(){return new e.Class(e.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let t=0,r=this.name;for(let s=this.name.length-1;s>0;s--){let o=r[s];if(o=="]")t++;else{if(o=="["||t==0)break;if(o==",")t++;else break}}return t}get staticFieldsData(){return e.exports.classGetStaticFieldData(this)}get valueTypeSize(){return e.exports.classGetValueTypeSize(this,NULL)}get type(){return new e.Type(e.exports.classGetType(this))}alloc(){return new e.Object(e.exports.objectNew(this))}field(t){return this.tryField(t)??m(`couldn't find field ${t} in class ${this.type.name}`)}*hierarchy(t){let r=t?.includeCurrent??!0?this:this.parent;for(;r;)yield r,r=r.parent}inflate(...t){this.isGeneric||m(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=t.length&&m(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${t.length}`);let r=t.map(i=>i.type.object),s=e.array(e.corlib.class("System.Type"),r),o=this.type.object.method("MakeGenericType",1).invoke(s);return new e.Class(e.exports.classFromObject(o))}initialize(){return e.exports.classInitialize(this),this}isAssignableFrom(t){return!!e.exports.classIsAssignableFrom(this,t)}isSubclassOf(t,r){return!!e.exports.classIsSubclassOf(this,t,+r)}method(t,r=-1){return this.tryMethod(t,r)??m(`couldn't find method ${t} in class ${this.type.name}`)}nested(t){return this.tryNested(t)??m(`couldn't find nested class ${t} in class ${this.type.name}`)}new(){let t=this.alloc(),r=Memory.alloc(Process.pointerSize);e.exports.objectInitialize(t,r);let s=r.readPointer();return s.isNull()||m(new e.Object(s).toString()),t}tryField(t){return new e.Field(e.exports.classGetFieldFromName(this,Memory.allocUtf8String(t))).asNullable()}tryMethod(t,r=-1){return new e.Method(e.exports.classGetMethodFromName(this,Memory.allocUtf8String(t),r)).asNullable()}tryNested(t){return this.nestedClasses.find(r=>r.name==t)}toString(){let t=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${t?` : ${t.map(r=>r?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(t){let r=new NativeCallback(s=>t(new e.Class(s)),"void",["pointer","pointer"]);return e.exports.classForEach(r,NULL)}};u([l],n.prototype,"arrayClass",null),u([l],n.prototype,"arrayElementSize",null),u([l],n.prototype,"assemblyName",null),u([l],n.prototype,"declaringClass",null),u([l],n.prototype,"baseType",null),u([l],n.prototype,"elementClass",null),u([l],n.prototype,"fields",null),u([l],n.prototype,"flags",null),u([l],n.prototype,"fullName",null),u([l],n.prototype,"generics",null),u([l],n.prototype,"hasReferences",null),u([l],n.prototype,"hasStaticConstructor",null),u([l],n.prototype,"image",null),u([l],n.prototype,"instanceSize",null),u([l],n.prototype,"isAbstract",null),u([l],n.prototype,"isBlittable",null),u([l],n.prototype,"isEnum",null),u([l],n.prototype,"isGeneric",null),u([l],n.prototype,"isInflated",null),u([l],n.prototype,"isInterface",null),u([l],n.prototype,"isValueType",null),u([l],n.prototype,"interfaces",null),u([l],n.prototype,"methods",null),u([l],n.prototype,"name",null),u([l],n.prototype,"namespace",null),u([l],n.prototype,"nestedClasses",null),u([l],n.prototype,"parent",null),u([l],n.prototype,"pointerClass",null),u([l],n.prototype,"rank",null),u([l],n.prototype,"staticFieldsData",null),u([l],n.prototype,"valueTypeSize",null),u([l],n.prototype,"type",null),n=u([E],n),e.Class=n})(d||(d={}));var d;(function(e){function n(a,t){let r=e.corlib.class("System.Delegate"),s=e.corlib.class("System.MulticastDelegate");r.isAssignableFrom(a)||m(`cannot create a delegate for ${a.type.name} as it's a non-delegate class`),(a.equals(r)||a.equals(s))&&m(`cannot create a delegate for neither ${r.type.name} nor ${s.type.name}, use a subclass instead`);let o=a.alloc(),i=o.handle.toString(),c=o.tryMethod("Invoke")??m(`cannot create a delegate for ${a.type.name}, there is no Invoke method`);o.method(".ctor").invoke(o,c.handle);let h=c.wrap(t);return o.field("method_ptr").value=h,o.field("invoke_impl").value=h,e._callbacksToKeepAlive[i]=h,o}e.delegate=n,e._callbacksToKeepAlive={}})(d||(d={}));var d;(function(e){let n=class extends b{get assemblies(){let t=G(r=>e.exports.domainGetAssemblies(this,r));if(t.length==0){let r=this.object.method("GetAssemblies").overload().invoke();t=globalThis.Array.from(r).map(s=>s.field("_mono_assembly").value)}return t.map(r=>new e.Assembly(r))}get object(){return e.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(t){return this.tryAssembly(t)??m(`couldn't find assembly ${t}`)}attach(){return new e.Thread(e.exports.threadAttach(this))}tryAssembly(t){return new e.Assembly(e.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(t))).asNullable()}};u([l],n.prototype,"assemblies",null),u([l],n.prototype,"object",null),n=u([E],n),e.Domain=n,_(e,"domain",()=>new e.Domain(e.exports.domainGet()),l)})(d||(d={}));var d;(function(e){class n extends b{get class(){return new e.Class(e.exports.fieldGetClass(this))}get flags(){return e.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let t=e.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return _(e.Field.prototype,"isThreadStatic",function(){return this.offset==t},l),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.fieldGetName(this).readUtf8String()}get offset(){return e.exports.fieldGetOffset(this)}get type(){return new e.Type(e.exports.fieldGetType(this))}get value(){this.isStatic||m(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let t=Memory.alloc(Process.pointerSize);return e.exports.fieldGetStaticValue(this.handle,t),e.read(t,this.type)}set value(t){this.isStatic||m(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&m(`cannot write the value of field ${this.name} as it's thread static or literal`);let r=t instanceof e.Object&&this.type.class.isValueType?t.unbox():t instanceof b?t.handle:t instanceof NativePointer?t:e.write(Memory.alloc(this.type.class.valueTypeSize),t,this.type);e.exports.fieldSetStaticValue(this.handle,r)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?e.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(t){this.isStatic&&m(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let r=this.offset-(t instanceof e.ValueType?e.Object.headerSize:0);return new Proxy(this,{get(s,o){return o=="value"?e.read(t.handle.add(r),s.type):Reflect.get(s,o)},set(s,o,i){return o=="value"?(e.write(t.handle.add(r),i,s.type),!0):Reflect.set(s,o,i)}})}}u([l],n.prototype,"class",null),u([l],n.prototype,"flags",null),u([l],n.prototype,"isLiteral",null),u([l],n.prototype,"isStatic",null),u([l],n.prototype,"isThreadStatic",null),u([l],n.prototype,"modifier",null),u([l],n.prototype,"name",null),u([l],n.prototype,"offset",null),u([l],n.prototype,"type",null),e.Field=n})(d||(d={}));var d;(function(e){class n{handle;constructor(t){this.handle=t}get target(){return new e.Object(e.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return e.exports.gcHandleFree(this.handle)}}e.GCHandle=n})(d||(d={}));var d;(function(e){let n=class extends b{get assembly(){return new e.Assembly(e.exports.imageGetAssembly(this))}get classCount(){return e.unityVersionIsBelow201830?this.classes.length:e.exports.imageGetClassCount(this)}get classes(){if(e.unityVersionIsBelow201830){let t=this.assembly.object.method("GetTypes").invoke(!1),r=globalThis.Array.from(t,o=>new e.Class(e.exports.classFromObject(o))),s=this.tryClass("<Module>");return s&&r.unshift(s),r}else return globalThis.Array.from(globalThis.Array(this.classCount),(t,r)=>new e.Class(e.exports.imageGetClass(this,r)))}get name(){return e.exports.imageGetName(this).readUtf8String()}class(t){return this.tryClass(t)??m(`couldn't find class ${t} in assembly ${this.name}`)}tryClass(t){let r=t.lastIndexOf("."),s=Memory.allocUtf8String(r==-1?"":t.slice(0,r)),o=Memory.allocUtf8String(t.slice(r+1));return new e.Class(e.exports.classFromName(this,s,o)).asNullable()}};u([l],n.prototype,"assembly",null),u([l],n.prototype,"classCount",null),u([l],n.prototype,"classes",null),u([l],n.prototype,"name",null),n=u([E],n),e.Image=n,_(e,"corlib",()=>new e.Image(e.exports.getCorlib()),l)})(d||(d={}));var d;(function(e){class n extends b{static capture(){return new e.MemorySnapshot}constructor(r=e.exports.memorySnapshotCapture()){super(r)}get classes(){return S(r=>e.exports.memorySnapshotGetClasses(this,r)).map(r=>new e.Class(r))}get objects(){return G(r=>e.exports.memorySnapshotGetObjects(this,r)).filter(r=>!r.isNull()).map(r=>new e.Object(r))}free(){e.exports.memorySnapshotFree(this)}}u([l],n.prototype,"classes",null),u([l],n.prototype,"objects",null),e.MemorySnapshot=n;function a(t){let r=e.MemorySnapshot.capture(),s=t(r);return r.free(),s}e.memorySnapshot=a})(d||(d={}));var d;(function(e){class n extends b{get class(){return new e.Class(e.exports.methodGetClass(this))}get flags(){return e.exports.methodGetFlags(this,NULL)}get implementationFlags(){let r=Memory.alloc(Process.pointerSize);return e.exports.methodGetFlags(this,r),r.readU32()}get fridaSignature(){let r=[];for(let s of this.parameters)r.push(s.type.fridaAlias);return(!this.isStatic||e.unityVersionIsBelow201830)&&r.unshift("pointer"),this.isInflated&&r.push("pointer"),r}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let r=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(r).map(s=>new e.Class(e.exports.classFromObject(s)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!e.exports.methodIsGeneric(this)}get isInflated(){return!!e.exports.methodIsInflated(this)}get isStatic(){return!e.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new e.Object(e.exports.methodGetObject(this,NULL))}get parameterCount(){return e.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(r,s)=>{let o=e.exports.methodGetParameterName(this,s).readUtf8String(),i=e.exports.methodGetParameterType(this,s);return new e.Parameter(o,s,new e.Type(i))})}get relativeVirtualAddress(){return this.virtualAddress.sub(e.module.base)}get returnType(){return new e.Type(e.exports.methodGetReturnType(this))}get virtualAddress(){let r=e.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,s=r.field("method_ptr").value,i=r.field("method").value.offsetOf(c=>c.readPointer().equals(s))??m("couldn't find the virtual address offset in the native method struct");return _(e.Method.prototype,"virtualAddress",function(){return this.handle.add(i).readPointer()},l),e.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(r){try{Interceptor.replace(this.virtualAddress,this.wrap(r))}catch(s){switch(s.message){case"access violation accessing 0x0":m(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(s.message)?.input:v(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":v(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw s}}}inflate(...r){if(!this.isGeneric||this.generics.length!=r.length){for(let c of this.overloads())if(c.isGeneric&&c.generics.length==r.length)return c.inflate(...r);m(`could not find inflatable signature of method ${this.name} with ${r.length} generic parameter(s)`)}let s=r.map(c=>c.type.object),o=e.array(e.corlib.class("System.Type"),s),i=this.object.method("MakeGenericMethod",1).invoke(o);return new e.Method(i.field("mhandle").value)}invoke(...r){return this.isStatic||m(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...r)}invokeRaw(r,...s){let o=s.map(e.toFridaValue);(!this.isStatic||e.unityVersionIsBelow201830)&&o.unshift(r),this.isInflated&&o.push(this.handle);try{let i=this.nativeFunction(...o);return e.fromFridaValue(i,this.returnType)}catch(i){switch(i==null&&m("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),i.message){case"bad argument count":m(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${s.length}`);case"expected a pointer":case"expected number":case"expected array with fields":m(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw i}}overload(...r){return this.tryOverload(...r)??m(`couldn't find overloaded method ${this.name}(${r.map(o=>o instanceof e.Class?o.type.name:o)})`)}*overloads(){for(let r of this.class.hierarchy())for(let s of r.methods)this.name==s.name&&(yield s)}parameter(r){return this.tryParameter(r)??m(`couldn't find parameter ${r} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...r){let s=r.length*1,o=r.length*2,i;e:for(let c of this.overloads()){if(c.parameterCount!=r.length)continue;let h=0,f=0;for(let y of c.parameters){let g=r[f];if(g instanceof e.Class)if(y.type.is(g.type))h+=2;else if(y.type.class.isAssignableFrom(g))h+=1;else continue e;else if(y.type.name==g)h+=2;else continue e;f++}if(!(h<s)){if(h==o)return c;if(i==null||h>i[0])i=[h,c];else if(h==i[0]){let y=0;for(let g of i[1].parameters){if(g.type.class.isAssignableFrom(c.parameters[y].type.class)){i=[h,c];continue e}y++}}}}return i?.[1]}tryParameter(r){return this.parameters.find(s=>s.name==r)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(r=>r.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(r){return this.isStatic&&m(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(s,o,i){switch(o){case"invoke":let c=r instanceof e.ValueType?s.class.isValueType?r.handle.sub(a()?e.Object.headerSize:0):m(`cannot invoke method ${s.class.type.name}::${s.name} against a value type, you must box it first`):s.class.isValueType?r.handle.add(a()?0:e.Object.headerSize):r.handle;return s.invokeRaw.bind(s,c);case"overloads":return function*(){for(let f of s[o]())f.isStatic||(yield f)};case"inflate":case"overload":case"tryOverload":let h=Reflect.get(s,o).bind(i);return function(...f){return h(...f)?.bind(r)}}return Reflect.get(s,o)}})}wrap(r){let s=+!this.isStatic|+e.unityVersionIsBelow201830;return new NativeCallback((...o)=>{let i=this.isStatic?this.class:this.class.isValueType?new e.ValueType(o[0].add(a()?e.Object.headerSize:0),this.class.type):new e.Object(o[0]),c=this.parameters.map((f,y)=>e.fromFridaValue(o[y+s],f.type)),h=r.call(i,...c);return e.toFridaValue(h)},this.returnType.fridaAlias,this.fridaSignature)}}u([l],n.prototype,"class",null),u([l],n.prototype,"flags",null),u([l],n.prototype,"implementationFlags",null),u([l],n.prototype,"fridaSignature",null),u([l],n.prototype,"generics",null),u([l],n.prototype,"isExternal",null),u([l],n.prototype,"isGeneric",null),u([l],n.prototype,"isInflated",null),u([l],n.prototype,"isStatic",null),u([l],n.prototype,"isSynchronized",null),u([l],n.prototype,"modifier",null),u([l],n.prototype,"name",null),u([l],n.prototype,"nativeFunction",null),u([l],n.prototype,"object",null),u([l],n.prototype,"parameterCount",null),u([l],n.prototype,"parameters",null),u([l],n.prototype,"relativeVirtualAddress",null),u([l],n.prototype,"returnType",null),e.Method=n;let a=()=>{let t=e.corlib.class("System.Int64").alloc();t.field("m_value").value=3735928559;let r=t.method("Equals",1).overload(t.class).invokeRaw(t,3735928559);return(a=()=>r)()}})(d||(d={}));var d;(function(e){class n extends b{static get headerSize(){return e.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&m(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(t,r,s){return r=="class"?Reflect.get(t,r).parent:r=="base"?Reflect.getOwnPropertyDescriptor(e.Object.prototype,r).get.bind(s)():Reflect.get(t,r)}})}get class(){return new e.Class(e.exports.objectGetClass(this))}get monitor(){return new e.Object.Monitor(this)}get size(){return e.exports.objectGetSize(this)}field(t){return this.tryField(t)??m(`couldn't find non-static field ${t} in hierarchy of class ${this.class.type.name}`)}method(t,r=-1){return this.tryMethod(t,r)??m(`couldn't find non-static method ${t} in hierarchy of class ${this.class.type.name}`)}ref(t){return new e.GCHandle(e.exports.gcHandleNew(this,+t))}virtualMethod(t){return new e.Method(e.exports.objectGetVirtualMethod(this,t)).bind(this)}tryField(t){let r=this.class.tryField(t);if(r?.isStatic){for(let s of this.class.hierarchy({includeCurrent:!1}))for(let o of s.fields)if(o.name==t&&!o.isStatic)return o.bind(this);return}return r?.bind(this)}tryMethod(t,r=-1){let s=this.class.tryMethod(t,r);if(s?.isStatic){for(let o of this.class.hierarchy())for(let i of o.methods)if(i.name==t&&!i.isStatic&&(r<0||i.parameterCount==r))return i.bind(this);return}return s?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new e.ValueType(e.exports.objectUnbox(this),this.class.type):m(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(t){return new e.GCHandle(e.exports.gcHandleNewWeakRef(this,+t))}}u([l],n.prototype,"class",null),u([l],n.prototype,"size",null),u([l],n,"headerSize",null),e.Object=n,function(a){class t{handle;constructor(s){this.handle=s}enter(){return e.exports.monitorEnter(this.handle)}exit(){return e.exports.monitorExit(this.handle)}pulse(){return e.exports.monitorPulse(this.handle)}pulseAll(){return e.exports.monitorPulseAll(this.handle)}tryEnter(s){return!!e.exports.monitorTryEnter(this.handle,s)}tryWait(s){return!!e.exports.monitorTryWait(this.handle,s)}wait(){return e.exports.monitorWait(this.handle)}}a.Monitor=t}(n=e.Object||(e.Object={}))})(d||(d={}));var d;(function(e){class n{name;position;type;constructor(t,r,s){this.name=t,this.position=r,this.type=s}toString(){return`${this.type.name} ${this.name}`}}e.Parameter=n})(d||(d={}));var d;(function(e){class n extends b{type;constructor(t,r){super(t),this.type=r}get(t){return e.read(this.handle.add(t*this.type.class.arrayElementSize),this.type)}read(t,r=0){let s=new globalThis.Array(t);for(let o=0;o<t;o++)s[o]=this.get(o+r);return s}set(t,r){e.write(this.handle.add(t*this.type.class.arrayElementSize),r,this.type)}toString(){return this.handle.toString()}write(t,r=0){for(let s=0;s<t.length;s++)this.set(s+r,t[s])}}e.Pointer=n})(d||(d={}));var d;(function(e){class n extends b{type;constructor(r,s){super(r),this.type=s}get value(){return e.read(this.handle,this.type)}set value(r){e.write(this.handle,r,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}e.Reference=n;function a(t,r){let s=Memory.alloc(Process.pointerSize);switch(typeof t){case"boolean":return new e.Reference(s.writeS8(+t),e.corlib.class("System.Boolean").type);case"number":switch(r?.enumValue){case e.Type.Enum.UBYTE:return new e.Reference(s.writeU8(t),r);case e.Type.Enum.BYTE:return new e.Reference(s.writeS8(t),r);case e.Type.Enum.CHAR:case e.Type.Enum.USHORT:return new e.Reference(s.writeU16(t),r);case e.Type.Enum.SHORT:return new e.Reference(s.writeS16(t),r);case e.Type.Enum.UINT:return new e.Reference(s.writeU32(t),r);case e.Type.Enum.INT:return new e.Reference(s.writeS32(t),r);case e.Type.Enum.ULONG:return new e.Reference(s.writeU64(t),r);case e.Type.Enum.LONG:return new e.Reference(s.writeS64(t),r);case e.Type.Enum.FLOAT:return new e.Reference(s.writeFloat(t),r);case e.Type.Enum.DOUBLE:return new e.Reference(s.writeDouble(t),r)}case"object":if(t instanceof e.ValueType||t instanceof e.Pointer)return new e.Reference(t.handle,t.type);if(t instanceof e.Object)return new e.Reference(s.writePointer(t),t.class.type);if(t instanceof e.String||t instanceof e.Array)return new e.Reference(s.writePointer(t),t.object.class.type);if(t instanceof NativePointer)switch(r?.enumValue){case e.Type.Enum.NUINT:case e.Type.Enum.NINT:return new e.Reference(s.writePointer(t),r)}else{if(t instanceof Int64)return new e.Reference(s.writeS64(t),e.corlib.class("System.Int64").type);if(t instanceof UInt64)return new e.Reference(s.writeU64(t),e.corlib.class("System.UInt64").type)}default:m(`couldn't create a reference to ${t} using an unhandled type ${r?.name}`)}}e.reference=a})(d||(d={}));var d;(function(e){class n extends b{get content(){return e.exports.stringGetChars(this).readUtf16String(this.length)}set content(r){let s=e.string("vfsfitvnm").handle.offsetOf(o=>o.readInt()==9)??m("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(e.String.prototype,"content",{set(o){e.exports.stringGetChars(this).writeUtf16String(o??""),this.handle.add(s).writeS32(o?.length??0)}}),this.content=r}get length(){return e.exports.stringGetLength(this)}get object(){return new e.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}e.String=n;function a(t){return new e.String(e.exports.stringNew(Memory.allocUtf8String(t??"")))}e.string=a})(d||(d={}));var d;(function(e){class n extends b{get id(){let t=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let r=Process.getCurrentThreadId(),o=ptr(t.apply(e.currentThread)).offsetOf(c=>c.readS32()==r,1024)??m("couldn't find the offset for determining the kernel id of a posix thread"),i=t;t=function(){return ptr(i.apply(this)).add(o).readS32()}}return _(e.Thread.prototype,"id",t,l),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!e.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new e.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let r=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(r.tryField("_syncContext")?.value??r.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(e.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return e.exports.threadDetach(this)}schedule(t){let r=this.synchronizationContext?.tryMethod("Post");return r==null?Process.runOnThread(this.id,t):new Promise(s=>{let o=e.delegate(e.corlib.class("System.Threading.SendOrPostCallback"),()=>{let i=t();setImmediate(()=>s(i))});Script.bindWeak(globalThis,()=>{o.field("method_ptr").value=o.field("invoke_impl").value=e.exports.domainGet}),r.invoke(o,NULL)})}tryLocalValue(t){for(let r=0;r<16;r++){let s=this.staticData.add(r*Process.pointerSize).readPointer();if(!s.isNull()){let o=new e.Object(s.readPointer()).asNullable();if(o?.class?.isSubclassOf(t,!1))return o}}}}u([l],n.prototype,"internal",null),u([l],n.prototype,"isFinalizer",null),u([l],n.prototype,"managedId",null),u([l],n.prototype,"object",null),u([l],n.prototype,"staticData",null),u([l],n.prototype,"synchronizationContext",null),e.Thread=n,_(e,"attachedThreads",()=>{if(e.exports.threadGetAttachedThreads.isNull()){let a=e.currentThread?.handle??m("Current thread is not attached to IL2CPP"),t=a.toMatchPattern(),r=[];for(let s of Process.enumerateRanges("rw-"))if(s.file==null){let o=Memory.scanSync(s.base,s.size,t);if(o.length==1){for(;;){let i=o[0].address.sub(o[0].size*r.length).readPointer();if(i.isNull()||!i.readPointer().equals(a.readPointer()))break;r.unshift(new e.Thread(i))}break}}return r}return G(e.exports.threadGetAttachedThreads).map(a=>new e.Thread(a))}),_(e,"currentThread",()=>new e.Thread(e.exports.threadGetCurrent()).asNullable()),_(e,"mainThread",()=>e.attachedThreads[0])})(d||(d={}));var d;(function(e){let n=class extends b{static get Enum(){let t=(s,o=i=>i)=>o(e.corlib.class(s)).type.enumValue,r={VOID:t("System.Void"),BOOLEAN:t("System.Boolean"),CHAR:t("System.Char"),BYTE:t("System.SByte"),UBYTE:t("System.Byte"),SHORT:t("System.Int16"),USHORT:t("System.UInt16"),INT:t("System.Int32"),UINT:t("System.UInt32"),LONG:t("System.Int64"),ULONG:t("System.UInt64"),NINT:t("System.IntPtr"),NUINT:t("System.UIntPtr"),FLOAT:t("System.Single"),DOUBLE:t("System.Double"),POINTER:t("System.IntPtr",s=>s.field("m_value")),VALUE_TYPE:t("System.Decimal"),OBJECT:t("System.Object"),STRING:t("System.String"),CLASS:t("System.Array"),ARRAY:t("System.Void",s=>s.arrayClass),NARRAY:t("System.Void",s=>new e.Class(e.exports.classGetArrayClass(s,2))),GENERIC_INSTANCE:t("System.Int32",s=>s.interfaces.find(o=>o.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:r}),k({...r,VAR:t("System.Action`1",s=>s.generics[0]),MVAR:t("System.Array",s=>s.method("AsReadOnly",1).generics[0])})}get class(){return new e.Class(e.exports.typeGetClass(this))}get fridaAlias(){function t(r){let s=r.class.fields.filter(o=>!o.isStatic);return s.length==0?["char"]:s.map(o=>o.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case e.Type.Enum.VOID:return"void";case e.Type.Enum.BOOLEAN:return"bool";case e.Type.Enum.CHAR:return"uchar";case e.Type.Enum.BYTE:return"int8";case e.Type.Enum.UBYTE:return"uint8";case e.Type.Enum.SHORT:return"int16";case e.Type.Enum.USHORT:return"uint16";case e.Type.Enum.INT:return"int32";case e.Type.Enum.UINT:return"uint32";case e.Type.Enum.LONG:return"int64";case e.Type.Enum.ULONG:return"uint64";case e.Type.Enum.FLOAT:return"float";case e.Type.Enum.DOUBLE:return"double";case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return"pointer";case e.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:t(this);case e.Type.Enum.CLASS:case e.Type.Enum.OBJECT:case e.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?t(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case e.Type.Enum.BOOLEAN:case e.Type.Enum.CHAR:case e.Type.Enum.BYTE:case e.Type.Enum.UBYTE:case e.Type.Enum.SHORT:case e.Type.Enum.USHORT:case e.Type.Enum.INT:case e.Type.Enum.UINT:case e.Type.Enum.LONG:case e.Type.Enum.ULONG:case e.Type.Enum.FLOAT:case e.Type.Enum.DOUBLE:case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return!0;default:return!1}}get name(){let t=e.exports.typeGetName(this);try{return t.readUtf8String()}finally{e.free(t)}}get object(){return new e.Object(e.exports.typeGetObject(this))}get enumValue(){return e.exports.typeGetTypeEnum(this)}is(t){return e.exports.typeEquals.isNull()?this.object.method("Equals").invoke(t.object):!!e.exports.typeEquals(this,t)}toString(){return this.name}};u([l],n.prototype,"class",null),u([l],n.prototype,"fridaAlias",null),u([l],n.prototype,"isByReference",null),u([l],n.prototype,"isPrimitive",null),u([l],n.prototype,"name",null),u([l],n.prototype,"object",null),u([l],n.prototype,"enumValue",null),u([l],n,"Enum",null),n=u([E],n),e.Type=n})(d||(d={}));var d;(function(e){class n extends b{type;constructor(t,r){super(t),this.type=r}box(){return new e.Object(e.exports.valueTypeBox(this.type.class,this))}field(t){return this.tryField(t)??m(`couldn't find non-static field ${t} in hierarchy of class ${this.type.name}`)}method(t,r=-1){return this.tryMethod(t,r)??m(`couldn't find non-static method ${t} in hierarchy of class ${this.type.name}`)}tryField(t){let r=this.type.class.tryField(t);if(r?.isStatic){for(let s of this.type.class.hierarchy())for(let o of s.fields)if(o.name==t&&!o.isStatic)return o.bind(this);return}return r?.bind(this)}tryMethod(t,r=-1){let s=this.type.class.tryMethod(t,r);if(s?.isStatic){for(let o of this.type.class.hierarchy())for(let i of o.methods)if(i.name==t&&!i.isStatic&&(r<0||i.parameterCount==r))return i.bind(this);return}return s?.bind(this)}toString(){let t=this.method("ToString",0);return this.isNull()?"null":t.class.isValueType?t.invoke().content??"null":this.box().toString()??"null"}}e.ValueType=n})(d||(d={}));globalThis.Il2Cpp=d;Il2Cpp.perform(function(){console.log("\u{1F527} Loading Robust GoodyHutHelper Instance Manager...");class e{validInstances=[];collectibleInstances=[];safeInvoke(t,r){try{if(!t||t.isNull())return{error:"Null instance",value:null};let s=t.method(r);return s?{error:null,value:s.invoke()}:{error:`Method ${r} not found`,value:null}}catch(s){let o=String(s);return o.includes("access violation")||o.includes("0x0")?{error:"Access violation - invalid instance",value:null}:{error:`Method error: ${o}`,value:null}}}validateInstance(t,r){console.log(`Validating instance from EntityController ${r}...`);let s={instance:t,entityIndex:r,isValid:!1,canCollect:!1,canBuyThrough:!1,state:"UNKNOWN",rewardType:"UNKNOWN",rewardAmount:null},o=this.safeInvoke(t,"IsJobComplete");if(o.error)return s.error=o.error,console.log(`\u274C EntityController ${r}: ${o.error}`),s;let i=this.safeInvoke(t,"CanCollect");if(i.error)return s.error=i.error,console.log(`\u274C EntityController ${r}: ${i.error}`),s;let c=this.safeInvoke(t,"CanBuyThrough");if(c.error)return s.error=c.error,console.log(`\u274C EntityController ${r}: ${c.error}`),s;let h=this.safeInvoke(t,"GetRewardType"),f=this.safeInvoke(t,"GetRewardAmount");s.isValid=!0,s.canCollect=i.value,s.canBuyThrough=c.value,s.rewardType=h.error?"UNKNOWN":String(h.value),s.rewardAmount=f.error?null:f.value;let y=o.value;return y===!0&&s.canCollect===!0?s.state="IDLE_READY":y===!1&&s.canCollect===!1?s.state="COLLECTING":y===!0&&s.canCollect===!1?s.state="COMPLETED_AWAITING":s.state="INCONSISTENT",console.log(`\u2705 EntityController ${r}: Valid - ${s.state} (CanCollect: ${s.canCollect}, Reward: ${s.rewardAmount} ${s.rewardType})`),s}scanAndValidateInstances(){console.log("\u{1F50D} Starting comprehensive instance validation..."),this.validInstances=[],this.collectibleInstances=[];try{let t=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),r=Il2Cpp.gc.choose(t);console.log(`Found ${r.length} EntityController instances`),console.log(`Validating GoodyHutHelper components...
`);let s=0,o=0,i=0,c=0;r.forEach((h,f)=>{try{let y=h.field("m_goodyHut");if(y&&y.value&&y.value!==null&&y.value.toString()!=="0x0"){let g=y.value,p=this.validateInstance(g,f);p.isValid?(this.validInstances.push(p),s++,p.canCollect&&(this.collectibleInstances.push(p),i++,p.rewardType==="GEMS"&&c++)):o++}}catch(y){console.log(`\u274C EntityController ${f}: Field access error - ${y}`),o++}}),console.log(`
=== VALIDATION SUMMARY ===`),console.log(`\u2705 Valid instances: ${s}`),console.log(`\u274C Invalid instances: ${o}`),console.log(`\u{1F3AF} Collectible instances: ${i}`),console.log(`\u{1F48E} GEMS collectible instances: ${c}`),console.log(`\u{1F4CA} Success rate: ${Math.round(s/(s+o)*100)}%`)}catch(t){console.log(`\u274C Error during validation: ${t}`)}}showValidInstances(){console.log(`
=== VALID INSTANCES DETAILS ===`),this.validInstances.forEach((t,r)=>{console.log(`
[${r}] EntityController ${t.entityIndex}:`),console.log(`  State: ${t.state}`),console.log(`  CanCollect: ${t.canCollect}`),console.log(`  CanBuyThrough: ${t.canBuyThrough}`),console.log(`  Reward: ${t.rewardAmount} ${t.rewardType}`);let s=this.safeInvoke(t.instance,"GetJobTimeLeft"),o=this.safeInvoke(t.instance,"GetHealth"),i=this.safeInvoke(t.instance,"GetExplorations");s.error||console.log(`  TimeLeft: ${s.value}`),o.error||console.log(`  Health: ${o.value}`),i.error||console.log(`  Explorations: ${i.value}`)})}getBestCollectibleInstance(){if(this.collectibleInstances.length===0)return console.log("\u274C No collectible instances found"),null;let t=this.collectibleInstances.filter(s=>s.rewardType==="GEMS");if(t.length===0)return console.log("\u274C No collectible instances with GEMS rewards found"),console.log(`Available rewards: ${this.collectibleInstances.map(s=>`${s.rewardAmount} ${s.rewardType}`).join(", ")}`),null;console.log(`\u{1F48E} Found ${t.length} collectible instances with GEMS rewards`);let r=t.filter(s=>s.state==="IDLE_READY");return r.length>0?(console.log(`\u2705 Using IDLE_READY GEMS instance: ${r[0].rewardAmount} GEMS`),r[0]):(console.log(`\u2705 Using first GEMS collectible instance (${t[0].state}): ${t[0].rewardAmount} GEMS`),t[0])}refindInstanceByIndex(t){try{let r=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),s=Il2Cpp.gc.choose(r);if(t>=s.length)return console.log(`\u274C EntityController index ${t} out of range`),null;let i=s[t].field("m_goodyHut");if(i&&i.value&&i.value!==null&&i.value.toString()!=="0x0"){let c=i.value;return this.validateInstance(c,t)}else return console.log(`\u274C EntityController ${t} no longer has GoodyHutHelper component`),null}catch(r){return console.log(`\u274C Error re-finding instance at index ${t}: ${r}`),null}}monitorStateChanges(t,r=2e3){console.log(`
\u{1F50D} Monitoring state changes after ${r}ms delay...`),setTimeout(()=>{console.log(`
--- SCANNING FOR STATE CHANGES ---`);let s=[],o=0,i=0;t.forEach(c=>{let h=this.refindInstanceByIndex(c.entityIndex);h&&h.isValid?(s.push(h),(c.state!==h.state||c.canCollect!==h.canCollect||c.canBuyThrough!==h.canBuyThrough)&&(console.log(`\u{1F504} EntityController ${c.entityIndex}: ${c.state} \u2192 ${h.state}`),console.log(`   CanCollect: ${c.canCollect} \u2192 ${h.canCollect}`),console.log(`   CanBuyThrough: ${c.canBuyThrough} \u2192 ${h.canBuyThrough}`),o++,!c.canBuyThrough&&h.canBuyThrough&&(console.log(`
\u{1F3AF} CanBuyThrough became available on EntityController ${h.entityIndex}!`),this.testDoJobBuyThrough(h))),h.state==="COLLECTING"&&i++):console.log(`\u274C EntityController ${c.entityIndex}: Instance became invalid`)}),console.log(`
\u{1F4CA} State Change Summary:`),console.log(`   Changed instances: ${o}`),console.log(`   Currently collecting: ${i}`),console.log(`   Valid instances after: ${s.length}/${t.length}`),globalThis.afterSnapshot=s},r)}testDoJobBuyThrough(t){console.log(`
\u{1F48E} Testing DoJobBuyThrough on EntityController ${t.entityIndex}...`);let r=this.safeInvoke(t.instance,"DoJobBuyThrough");return r.error?(console.log(`\u274C DoJobBuyThrough failed: ${r.error}`),!1):(console.log(`\u2705 DoJobBuyThrough executed successfully! Result: ${r.value}`),setTimeout(()=>{console.log(`
--- STATE AFTER DoJobBuyThrough ---`);let s=this.refindInstanceByIndex(t.entityIndex);s&&s.isValid?(console.log(`State: ${s.state}`),console.log(`CanCollect: ${s.canCollect}`),console.log(`CanBuyThrough: ${s.canBuyThrough}`)):console.log("\u274C Instance became invalid after DoJobBuyThrough")},1e3),!0)}executeStartCollect(t){console.log(`
\u{1F680} Executing StartCollect on EntityController ${t.entityIndex}...`);let r=this.safeInvoke(t.instance,"CanCollect");if(r.error)return console.log(`\u274C Instance became invalid: ${r.error}`),!1;if(!r.value)return console.log("\u274C Instance no longer collectible"),!1;console.log("\u{1F4F8} Taking before-snapshot of all instances...");let s=[...this.validInstances],o=this.safeInvoke(t.instance,"StartCollect");return o.error?(console.log(`\u274C StartCollect failed: ${o.error}`),!1):(console.log(`\u2705 StartCollect executed successfully! Result: ${o.value}`),this.monitorStateChanges(s,1500),setTimeout(()=>{console.log(`
\u{1F50D} Re-scanning EntityController ${t.entityIndex}...`);let i=this.refindInstanceByIndex(t.entityIndex);i&&i.isValid?(console.log(`\u2705 Re-found instance: ${i.state}`),console.log(`   CanCollect: ${i.canCollect}`),console.log(`   CanBuyThrough: ${i.canBuyThrough}`),i.canBuyThrough&&(console.log(`
\u{1F3AF} CanBuyThrough is available - testing DoJobBuyThrough...`),this.testDoJobBuyThrough(i))):console.log("\u274C Could not re-find instance - may have been destroyed/recreated")},500),!0)}}let n=new e;globalThis.goodyManager={scan:()=>n.scanAndValidateInstances(),showValid:()=>n.showValidInstances(),getBest:()=>n.getBestCollectibleInstance(),startCollection:()=>{let a=n.getBestCollectibleInstance();return a?n.executeStartCollect(a):(console.log("\u274C No collectible instances available"),!1)},help:()=>{console.log("=== GoodyHut Instance Manager Commands ==="),console.log("goodyManager.scan() - Scan and validate all instances"),console.log("goodyManager.showValid() - Show details of valid instances"),console.log("goodyManager.getBest() - Get best collectible instance (GEMS only)"),console.log("goodyManager.startCollection() - Start collection on best GEMS instance"),console.log(""),console.log("\u{1F48E} NOTE: Only instances with GEMS rewards will be collected!")}},console.log("\u{1F527} Robust GoodyHutHelper Instance Manager loaded!"),console.log("Use goodyManager.help() for available commands"),console.log("Start with: goodyManager.scan()")});
