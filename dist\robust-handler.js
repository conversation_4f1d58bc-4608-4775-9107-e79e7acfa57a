📦
75902 /src/robust-instance-handler.js
✄
var d=function(e,n,c,t){var s=arguments.length,r=s<3?n:t===null?t=Object.getOwnPropertyDescriptor(n,c):t,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(e,n,c,t);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(r=(s<3?o(r):s>3?o(n,c,r):o(n,c))||r);return s>3&&r&&Object.defineProperty(n,c,r),r},h;(function(e){e.application={get dataPath(){return n("get_persistentDataPath")},get identifier(){return n("get_identifier")??n("get_bundleIdentifier")??Process.mainModule.name},get version(){return n("get_version")??V(e.module).toString(16)}},_(e,"unityVersion",()=>{try{let t=e.$config.unityVersion??n("get_unityVersion");if(t!=null)return t}catch{}let c="69 6c 32 63 70 70";for(let t of e.module.enumerateRanges("r--").concat(Process.getRangeByAddress(e.module.base)))for(let{address:s}of Memory.scanSync(t.base,t.size,c)){for(;s.readU8()!=0;)s=s.sub(1);let r=x.find(s.add(1).readCString());if(r!=null)return r}y("couldn't determine the Unity version, please specify it manually")},l),_(e,"unityVersionIsBelow201830",()=>x.lt(e.unityVersion,"2018.3.0"),l),_(e,"unityVersionIsBelow202120",()=>x.lt(e.unityVersion,"2021.2.0"),l);function n(c){let t=e.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+c)),s=new NativeFunction(t,"pointer",[]);return s.isNull()?null:new e.String(s()).asNullable()?.content??null}})(h||(h={}));var h;(function(e){function n(c,t){let s={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},r=typeof c=="boolean"?"System.Boolean":typeof c=="number"?s[t??"int32"]:c instanceof Int64?"System.Int64":c instanceof UInt64?"System.UInt64":c instanceof NativePointer?s[t??"intptr"]:y(`Cannot create boxed primitive using value of type '${typeof c}'`),o=e.corlib.class(r??y(`Unknown primitive type name '${t}'`)).alloc();return(o.tryField("m_value")??o.tryField("_pointer")??y(`Could not find primitive field in class '${r}'`)).value=c,o}e.boxed=n})(h||(h={}));var h;(function(e){e.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(h||(h={}));var h;(function(e){function n(o,i){o=o??`${e.application.identifier}_${e.application.version}.cs`,i=i??e.application.dataPath??Process.getCurrentDir(),s(i);let a=`${i}/${o}`,u=new File(a,"w");for(let m of e.domain.assemblies){w(`dumping ${m.name}...`);for(let f of m.image.classes)u.write(`${f}

`)}u.flush(),u.close(),P(`dump saved to ${a}`),r()}e.dump=n;function c(o,i=!1){o=o??`${e.application.dataPath??Process.getCurrentDir()}/${e.application.identifier}_${e.application.version}`,!i&&t(o)&&y(`directory ${o} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let a of e.domain.assemblies){w(`dumping ${a.name}...`);let u=`${o}/${a.name.replaceAll(".","/")}.cs`;s(u.substring(0,u.lastIndexOf("/")));let m=new File(u,"w");for(let f of a.image.classes)m.write(`${f}

`);m.flush(),m.close()}P(`dump saved to ${o}`),r()}e.dumpTree=c;function t(o){return e.corlib.class("System.IO.Directory").method("Exists").invoke(e.string(o))}function s(o){e.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(e.string(o))}function r(){v("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(h||(h={}));var h;(function(e){function n(c="current"){let t=e.exports.threadGetCurrent();return Interceptor.attach(e.module.getExportByName("__cxa_throw"),function(s){c=="current"&&!e.exports.threadGetCurrent().equals(t)||w(new e.Object(s[0].readPointer()))})}e.installExceptionListener=n})(h||(h={}));var h;(function(e){e.exports={get alloc(){return n("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return n("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return n("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return n("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return n("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return n("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return n("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return n("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return n("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return n("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return n("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return n("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return n("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return n("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return n("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return n("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return n("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return n("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return n("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return n("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return n("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return n("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return n("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return n("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return n("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return n("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return n("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return n("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return n("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return n("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return n("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return n("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return n("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return n("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return n("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return n("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return n("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return n("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return n("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return n("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return n("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return n("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return n("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return n("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return n("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return n("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return n("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return n("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return n("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return n("il2cpp_free","void",["pointer"])},get gcCollect(){return n("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return n("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return n("il2cpp_gc_disable","void",[])},get gcEnable(){return n("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return n("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return n("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return n("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return n("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return n("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return n("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return n("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return n("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return n("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return n("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return n("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return n("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return n("il2cpp_stop_gc_world","void",[])},get getCorlib(){return n("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return n("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return n("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return n("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return n("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return n("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return n("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return n("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return n("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return n("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return n("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return n("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return n("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return n("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return n("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return n("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return n("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return n("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return n("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return n("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return n("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return n("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return n("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return n("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return n("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return n("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return n("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return n("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return n("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return n("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return n("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return n("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return n("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return n("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return n("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return n("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return n("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return n("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return n("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return n("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return n("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return n("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return n("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return n("il2cpp_string_length","int32",["pointer"])},get stringNew(){return n("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return n("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return n("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return n("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return n("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return n("il2cpp_thread_current","pointer",[])},get threadIsVm(){return n("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return n("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return n("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return n("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return n("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return n("il2cpp_type_get_type","int",["pointer"])}},F(e.exports,l),_(e,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),l);function n(c,t,s){let r=e.$config.exports?.[c]?.()??e.module.findExportByName(c)??e.memorySnapshotExports[c],o=new NativeFunction(r??NULL,t,s);return o.isNull()?new Proxy(o,{get(i,a){let u=i[a];return typeof u=="function"?u.bind(i):u},apply(){r==null?y(`couldn't resolve export ${c}`):r.isNull()&&y(`export ${c} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):o}})(h||(h={}));var h;(function(e){function n(t){return s=>s instanceof e.Class?t.isAssignableFrom(s):t.isAssignableFrom(s.class)}e.is=n;function c(t){return s=>s instanceof e.Class?s.equals(t):s.class.equals(t)}e.isExactly=c})(h||(h={}));var h;(function(e){e.gc={get heapSize(){return e.exports.gcGetHeapSize()},get isEnabled(){return!e.exports.gcIsDisabled()},get isIncremental(){return!!e.exports.gcIsIncremental()},get maxTimeSlice(){return e.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return e.exports.gcGetUsedSize()},set isEnabled(n){n?e.exports.gcEnable():e.exports.gcDisable()},set maxTimeSlice(n){e.exports.gcSetMaxTimeSlice(n)},choose(n){let c=[],t=(r,o)=>{for(let i=0;i<o;i++)c.push(new e.Object(r.add(i*Process.pointerSize).readPointer()))},s=new NativeCallback(t,"void",["pointer","int","pointer"]);if(e.unityVersionIsBelow202120){let r=new NativeCallback(()=>{},"void",[]),o=e.exports.livenessCalculationBegin(n,0,s,NULL,r,r);e.exports.livenessCalculationFromStatics(o),e.exports.livenessCalculationEnd(o)}else{let r=(a,u)=>!a.isNull()&&u.compare(0)==0?(e.free(a),NULL):e.alloc(u),o=new NativeCallback(r,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let i=e.exports.livenessAllocateStruct(n,0,s,NULL,o);e.exports.livenessCalculationFromStatics(i),e.exports.livenessFinalize(i),this.startWorld(),e.exports.livenessFreeStruct(i)}return c},collect(n){e.exports.gcCollect(n<0?0:n>2?2:n)},collectALittle(){e.exports.gcCollectALittle()},startWorld(){return e.exports.gcStartWorld()},startIncrementalCollection(){return e.exports.gcStartIncrementalCollection()},stopWorld(){return e.exports.gcStopWorld()}}})(h||(h={}));var G;(function(e){_(e,"apiLevel",()=>{let c=n("ro.build.version.sdk");return c?parseInt(c):null},l);function n(c){let t=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(t){let s=new NativeFunction(t,"void",["pointer","pointer"]),r=Memory.alloc(92).writePointer(NULL);return s(Memory.allocUtf8String(c),r),r.readCString()??void 0}}})(G||(G={}));function y(e){let n=new Error(e);throw n.name="Il2CppError",n.stack=n.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),n}function v(e){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${e}`)}function P(e){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${e}`)}function w(e){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${e}`)}function F(e,n,c=Object.getOwnPropertyDescriptors(e)){for(let t in c)c[t]=n(e,t,c[t]);return Object.defineProperties(e,c),e}function _(e,n,c,t){globalThis.Object.defineProperty(e,n,t?.(e,n,{get:c,configurable:!0})??{get:c,configurable:!0})}function O(e){let n=3735928559,c=1103547991;for(let t=0,s;t<e.length;t++)s=e.charCodeAt(t),n=Math.imul(n^s,2654435761),c=Math.imul(c^s,1597334677);return n=Math.imul(n^n>>>16,2246822507),n^=Math.imul(c^c>>>13,3266489909),c=Math.imul(c^c>>>16,2246822507),c^=Math.imul(n^n>>>13,3266489909),4294967296*(2097151&c)+(n>>>0)}function V(e){return O(e.enumerateExports().sort((n,c)=>n.name.localeCompare(c.name)).map(n=>n.name+n.address.sub(e.base)).join(""))}function l(e,n,c){let t=c.get;if(!t)throw new Error("@lazy can only be applied to getter accessors");return c.get=function(){let s=t.call(this);return Object.defineProperty(this,n,{value:s,configurable:c.configurable,enumerable:c.enumerable,writable:!1}),s},c}var b=class{handle;constructor(n){n instanceof NativePointer?this.handle=n:this.handle=n.handle}equals(n){return this.handle.equals(n.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function U(e){return Object.keys(e).reduce((n,c)=>(n[n[c]]=c,n),e)}NativePointer.prototype.offsetOf=function(e,n){n??=512;for(let c=0;n>0?c<n:c<-n;c++)if(e(n>0?this.add(c):this.sub(c)))return c;return null};function S(e){let n=[],c=Memory.alloc(Process.pointerSize),t=e(c);for(;!t.isNull();)n.push(t),t=e(c);return n}function A(e){let n=Memory.alloc(Process.pointerSize),c=e(n);if(c.isNull())return[];let t=new Array(n.readInt());for(let s=0;s<t.length;s++)t[s]=c.add(s*Process.pointerSize).readPointer();return t}function E(e){return new Proxy(e,{cache:new Map,construct(n,c){let t=c[0].toUInt32();return this.cache.has(t)||this.cache.set(t,new n(c[0])),this.cache.get(t)}})}var x;(function(e){let n=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function c(o){return o?.match(n)?.[0]}e.find=c;function t(o,i){return r(o,i)>=0}e.gte=t;function s(o,i){return r(o,i)<0}e.lt=s;function r(o,i){let a=o.match(n),u=i.match(n);for(let m=1;m<=3;m++){let f=Number(a?.[m]??-1),g=Number(u?.[m]??-1);if(f>g)return 1;if(f<g)return-1}return 0}})(x||(x={}));var h;(function(e){function n(i=Process.pointerSize){return e.exports.alloc(i)}e.alloc=n;function c(i){return e.exports.free(i)}e.free=c;function t(i,a){switch(a.enumValue){case e.Type.Enum.BOOLEAN:return!!i.readS8();case e.Type.Enum.BYTE:return i.readS8();case e.Type.Enum.UBYTE:return i.readU8();case e.Type.Enum.SHORT:return i.readS16();case e.Type.Enum.USHORT:return i.readU16();case e.Type.Enum.INT:return i.readS32();case e.Type.Enum.UINT:return i.readU32();case e.Type.Enum.CHAR:return i.readU16();case e.Type.Enum.LONG:return i.readS64();case e.Type.Enum.ULONG:return i.readU64();case e.Type.Enum.FLOAT:return i.readFloat();case e.Type.Enum.DOUBLE:return i.readDouble();case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return i.readPointer();case e.Type.Enum.POINTER:return new e.Pointer(i.readPointer(),a.class.baseType);case e.Type.Enum.VALUE_TYPE:return new e.ValueType(i,a);case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:return new e.Object(i.readPointer());case e.Type.Enum.GENERIC_INSTANCE:return a.class.isValueType?new e.ValueType(i,a):new e.Object(i.readPointer());case e.Type.Enum.STRING:return new e.String(i.readPointer());case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i.readPointer())}y(`couldn't read the value from ${i} using an unhandled or unknown type ${a.name} (${a.enumValue}), please file an issue`)}e.read=t;function s(i,a,u){switch(u.enumValue){case e.Type.Enum.BOOLEAN:return i.writeS8(+a);case e.Type.Enum.BYTE:return i.writeS8(a);case e.Type.Enum.UBYTE:return i.writeU8(a);case e.Type.Enum.SHORT:return i.writeS16(a);case e.Type.Enum.USHORT:return i.writeU16(a);case e.Type.Enum.INT:return i.writeS32(a);case e.Type.Enum.UINT:return i.writeU32(a);case e.Type.Enum.CHAR:return i.writeU16(a);case e.Type.Enum.LONG:return i.writeS64(a);case e.Type.Enum.ULONG:return i.writeU64(a);case e.Type.Enum.FLOAT:return i.writeFloat(a);case e.Type.Enum.DOUBLE:return i.writeDouble(a);case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return i.writePointer(a);case e.Type.Enum.VALUE_TYPE:return Memory.copy(i,a,u.class.valueTypeSize),i;case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:return a instanceof e.ValueType?(Memory.copy(i,a,u.class.valueTypeSize),i):i.writePointer(a)}y(`couldn't write value ${a} to ${i} using an unhandled or unknown type ${u.name} (${u.enumValue}), please file an issue`)}e.write=s;function r(i,a){if(globalThis.Array.isArray(i)){let u=Memory.alloc(a.class.valueTypeSize),m=a.class.fields.filter(f=>!f.isStatic);for(let f=0;f<m.length;f++){let g=r(i[f],m[f].type);s(u.add(m[f].offset).sub(e.Object.headerSize),g,m[f].type)}return new e.ValueType(u,a)}else if(i instanceof NativePointer){if(a.isByReference)return new e.Reference(i,a);switch(a.enumValue){case e.Type.Enum.POINTER:return new e.Pointer(i,a.class.baseType);case e.Type.Enum.STRING:return new e.String(i);case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:case e.Type.Enum.OBJECT:return new e.Object(i);case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i);default:return i}}else return a.enumValue==e.Type.Enum.BOOLEAN?!!i:a.enumValue==e.Type.Enum.VALUE_TYPE&&a.class.isEnum?r([i],a):i}e.fromFridaValue=r;function o(i){if(typeof i=="boolean")return+i;if(i instanceof e.ValueType){if(i.type.class.isEnum)return i.field("value__").value;{let a=i.type.class.fields.filter(u=>!u.isStatic).map(u=>o(u.bind(i).value));return a.length==0?[0]:a}}else return i}e.toFridaValue=o})(h||(h={}));var h;(function(e){_(e,"module",()=>c()??y("Could not find IL2CPP module"));async function n(s=!1){let r=c()??await new Promise(o=>{let[i,a]=t(),u=setTimeout(()=>{v(`after 10 seconds, IL2CPP module '${i}' has not been loaded yet, is the app running?`)},1e4),m=Process.attachModuleObserver({onAdded(f){(f.name==i||a&&f.name==a)&&(clearTimeout(u),setImmediate(()=>{o(f),m.detach()}))}})});return Reflect.defineProperty(e,"module",{value:r}),e.exports.getCorlib().isNull()?await new Promise(o=>{let i=Interceptor.attach(e.exports.initialize,{onLeave(){i.detach(),s?o(!0):setImmediate(()=>o(!1))}})}):!1}e.initialize=n;function c(){let[s,r]=t();return Process.findModuleByName(s)??Process.findModuleByName(r??s)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function t(){if(e.$config.moduleName)return[e.$config.moduleName];switch(Process.platform){case"linux":return[G.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}y(`${Process.platform} is not supported yet`)}})(h||(h={}));var h;(function(e){async function n(c,t="bind"){let s=null;try{let r=await e.initialize(t=="main");if(t=="main"&&!r)return n(()=>e.mainThread.schedule(c),"free");e.currentThread==null&&(s=e.domain.attach()),t=="bind"&&s!=null&&Script.bindWeak(globalThis,()=>s?.detach());let o=c();return o instanceof Promise?await o:o}catch(r){return Script.nextTick(o=>{throw o},r),Promise.reject(r)}finally{t=="free"&&s!=null&&s.detach()}}e.perform=n})(h||(h={}));var h;(function(e){class n{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let r=`
${this.#e.buffer.join(`
`)}
`;if(this.#d)w(r);else{let o=O(r);this.#e.history.has(o)||(this.#e.history.add(o),w(r))}this.#e.buffer.length=0}}};#u=e.mainThread.id;#d=!1;#h;#c=[];#l;#t;#n;#s;#r;#i;#o;#a;constructor(r){this.#h=r}thread(r){return this.#u=r.id,this}verbose(r){return this.#d=r,this}domain(){return this.#l=e.domain,this}assemblies(...r){return this.#t=r,this}classes(...r){return this.#n=r,this}methods(...r){return this.#s=r,this}filterAssemblies(r){return this.#r=r,this}filterClasses(r){return this.#i=r,this}filterMethods(r){return this.#o=r,this}filterParameters(r){return this.#a=r,this}and(){let r=g=>{if(this.#a==null){this.#c.push(g);return}for(let p of g.parameters)if(this.#a(p)){this.#c.push(g);break}},o=g=>{for(let p of g)r(p)},i=g=>{if(this.#o==null){o(g.methods);return}for(let p of g.methods)this.#o(p)&&r(p)},a=g=>{for(let p of g)i(p)},u=g=>{if(this.#i==null){a(g.image.classes);return}for(let p of g.image.classes)this.#i(p)&&i(p)},m=g=>{for(let p of g)u(p)},f=g=>{if(this.#r==null){m(g.assemblies);return}for(let p of g.assemblies)this.#r(p)&&u(p)};return this.#s?o(this.#s):this.#n?a(this.#n):this.#t?m(this.#t):this.#l&&f(this.#l),this.#t=void 0,this.#n=void 0,this.#s=void 0,this.#r=void 0,this.#i=void 0,this.#o=void 0,this.#a=void 0,this}attach(){for(let r of this.#c)if(!r.virtualAddress.isNull())try{this.#h(r,this.#e,this.#u)}catch(o){switch(o.message){case/unable to intercept function at \w+; please file a bug/.exec(o.message)?.input:case"already replaced this function":break;default:throw o}}}}e.Tracer=n;function c(s=!1){let r=()=>(i,a,u)=>{let m=i.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(i.virtualAddress,{onEnter(){this.threadId==u&&a.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(a.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==u&&(a.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(--a.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`),a.flush())}})},o=()=>(i,a,u)=>{let m=i.relativeVirtualAddress.toString(16).padStart(8,"0"),f=+!i.isStatic|+e.unityVersionIsBelow201830,g=function(...T){if(this.threadId==u){let M=i.isStatic?void 0:new e.Parameter("this",-1,i.class.type),R=M?[M].concat(i.parameters):i.parameters;a.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(a.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m(${R.map($=>`\x1B[32m${$.name}\x1B[0m = \x1B[31m${e.fromFridaValue(T[$.position+f],$.type)}\x1B[0m`).join(", ")})`)}let N=i.nativeFunction(...T);return this.threadId==u&&(a.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(--a.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m${N==null?"":` = \x1B[36m${e.fromFridaValue(N,i.returnType)}`}\x1B[0m`),a.flush()),N};i.revert();let p=new NativeCallback(g,i.returnType.fridaAlias,i.fridaSignature);Interceptor.replace(i.virtualAddress,p)};return new e.Tracer(s?o():r())}e.trace=c;function t(s){let r=e.domain.assemblies.flatMap(a=>a.image.classes.flatMap(u=>u.methods.filter(m=>!m.virtualAddress.isNull()))).sort((a,u)=>a.virtualAddress.compare(u.virtualAddress)),o=a=>{let u=0,m=r.length-1;for(;u<=m;){let f=Math.floor((u+m)/2),g=r[f].virtualAddress.compare(a);if(g==0)return r[f];g>0?m=f-1:u=f+1}return r[m]},i=()=>(a,u,m)=>{Interceptor.attach(a.virtualAddress,function(){if(this.threadId==m){let f=globalThis.Thread.backtrace(this.context,s);f.unshift(a.virtualAddress);for(let g of f)if(g.compare(e.module.base)>0&&g.compare(e.module.base.add(e.module.size))<0){let p=o(g);if(p){let T=g.sub(p.virtualAddress);T.compare(4095)<0&&u.buffer.push(`\x1B[2m0x${p.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${T.toString(16).padStart(3,"0")}\x1B[0m ${p.class.type.name}::\x1B[1m${p.name}\x1B[0m`)}}u.flush()}})};return new e.Tracer(i())}e.backtrace=t})(h||(h={}));var h;(function(e){class n extends b{static get headerSize(){return e.corlib.class("System.Array").instanceSize}get elements(){let r=e.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(o=>o.readS16()==118)??y("couldn't find the elements offset in the native array struct");return _(e.Array.prototype,"elements",function(){return new e.Pointer(this.handle.add(r),this.elementType)},l),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return e.exports.arrayGetLength(this)}get object(){return new e.Object(this)}get(s){return(s<0||s>=this.length)&&y(`cannot get element at index ${s} as the array length is ${this.length}`),this.elements.get(s)}set(s,r){(s<0||s>=this.length)&&y(`cannot set element at index ${s} as the array length is ${this.length}`),this.elements.set(s,r)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let s=0;s<this.length;s++)yield this.elements.get(s)}}d([l],n.prototype,"elementSize",null),d([l],n.prototype,"elementType",null),d([l],n.prototype,"length",null),d([l],n.prototype,"object",null),d([l],n,"headerSize",null),e.Array=n;function c(t,s){let r=typeof s=="number"?s:s.length,o=new e.Array(e.exports.arrayNew(t,r));return globalThis.Array.isArray(s)&&o.elements.write(s),o}e.array=c})(h||(h={}));var h;(function(e){let n=class extends b{get image(){if(e.exports.assemblyGetImage.isNull()){let t=this.object.tryMethod("GetType",1)?.invoke(e.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??y(`couldn't find the runtime module object of assembly ${this.name}`);return new e.Image(t.field("_impl").value)}return new e.Image(e.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let t of e.domain.object.method("GetAssemblies",1).invoke(!1))if(t.field("_mono_assembly").value.equals(this))return t;y("couldn't find the object of the native assembly struct")}};d([l],n.prototype,"name",null),d([l],n.prototype,"object",null),n=d([E],n),e.Assembly=n})(h||(h={}));var h;(function(e){let n=class extends b{get actualInstanceSize(){let t=e.corlib.class("System.String"),s=t.handle.offsetOf(r=>r.readInt()==t.instanceSize-2)??y("couldn't find the actual instance size offset in the native class struct");return _(e.Class.prototype,"actualInstanceSize",function(){return this.handle.add(s).readS32()},l),this.actualInstanceSize}get arrayClass(){return new e.Class(e.exports.classGetArrayClass(this,1))}get arrayElementSize(){return e.exports.classGetArrayElementSize(this)}get assemblyName(){return e.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new e.Class(e.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new e.Type(e.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new e.Class(e.exports.classGetElementClass(this)).asNullable()}get fields(){return S(t=>e.exports.classGetFields(this,t)).map(t=>new e.Field(t))}get flags(){return e.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let t=this.image.tryClass(this.fullName)?.asNullable();return t?.equals(this)?null:t??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let t=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(t).map(s=>new e.Class(e.exports.classFromObject(s)))}get hasReferences(){return!!e.exports.classHasReferences(this)}get hasStaticConstructor(){let t=this.tryMethod(".cctor");return t!=null&&!t.virtualAddress.isNull()}get image(){return new e.Image(e.exports.classGetImage(this))}get instanceSize(){return e.exports.classGetInstanceSize(this)}get isAbstract(){return!!e.exports.classIsAbstract(this)}get isBlittable(){return!!e.exports.classIsBlittable(this)}get isEnum(){return!!e.exports.classIsEnum(this)}get isGeneric(){return!!e.exports.classIsGeneric(this)}get isInflated(){return!!e.exports.classIsInflated(this)}get isInterface(){return!!e.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!e.exports.classIsValueType(this)}get interfaces(){return S(t=>e.exports.classGetInterfaces(this,t)).map(t=>new e.Class(t))}get methods(){return S(t=>e.exports.classGetMethods(this,t)).map(t=>new e.Method(t))}get name(){return e.exports.classGetName(this).readUtf8String()}get namespace(){return e.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return S(t=>e.exports.classGetNestedClasses(this,t)).map(t=>new e.Class(t))}get parent(){return new e.Class(e.exports.classGetParent(this)).asNullable()}get pointerClass(){return new e.Class(e.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let t=0,s=this.name;for(let r=this.name.length-1;r>0;r--){let o=s[r];if(o=="]")t++;else{if(o=="["||t==0)break;if(o==",")t++;else break}}return t}get staticFieldsData(){return e.exports.classGetStaticFieldData(this)}get valueTypeSize(){return e.exports.classGetValueTypeSize(this,NULL)}get type(){return new e.Type(e.exports.classGetType(this))}alloc(){return new e.Object(e.exports.objectNew(this))}field(t){return this.tryField(t)??y(`couldn't find field ${t} in class ${this.type.name}`)}*hierarchy(t){let s=t?.includeCurrent??!0?this:this.parent;for(;s;)yield s,s=s.parent}inflate(...t){this.isGeneric||y(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=t.length&&y(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${t.length}`);let s=t.map(i=>i.type.object),r=e.array(e.corlib.class("System.Type"),s),o=this.type.object.method("MakeGenericType",1).invoke(r);return new e.Class(e.exports.classFromObject(o))}initialize(){return e.exports.classInitialize(this),this}isAssignableFrom(t){return!!e.exports.classIsAssignableFrom(this,t)}isSubclassOf(t,s){return!!e.exports.classIsSubclassOf(this,t,+s)}method(t,s=-1){return this.tryMethod(t,s)??y(`couldn't find method ${t} in class ${this.type.name}`)}nested(t){return this.tryNested(t)??y(`couldn't find nested class ${t} in class ${this.type.name}`)}new(){let t=this.alloc(),s=Memory.alloc(Process.pointerSize);e.exports.objectInitialize(t,s);let r=s.readPointer();return r.isNull()||y(new e.Object(r).toString()),t}tryField(t){return new e.Field(e.exports.classGetFieldFromName(this,Memory.allocUtf8String(t))).asNullable()}tryMethod(t,s=-1){return new e.Method(e.exports.classGetMethodFromName(this,Memory.allocUtf8String(t),s)).asNullable()}tryNested(t){return this.nestedClasses.find(s=>s.name==t)}toString(){let t=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${t?` : ${t.map(s=>s?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(t){let s=new NativeCallback(r=>t(new e.Class(r)),"void",["pointer","pointer"]);return e.exports.classForEach(s,NULL)}};d([l],n.prototype,"arrayClass",null),d([l],n.prototype,"arrayElementSize",null),d([l],n.prototype,"assemblyName",null),d([l],n.prototype,"declaringClass",null),d([l],n.prototype,"baseType",null),d([l],n.prototype,"elementClass",null),d([l],n.prototype,"fields",null),d([l],n.prototype,"flags",null),d([l],n.prototype,"fullName",null),d([l],n.prototype,"generics",null),d([l],n.prototype,"hasReferences",null),d([l],n.prototype,"hasStaticConstructor",null),d([l],n.prototype,"image",null),d([l],n.prototype,"instanceSize",null),d([l],n.prototype,"isAbstract",null),d([l],n.prototype,"isBlittable",null),d([l],n.prototype,"isEnum",null),d([l],n.prototype,"isGeneric",null),d([l],n.prototype,"isInflated",null),d([l],n.prototype,"isInterface",null),d([l],n.prototype,"isValueType",null),d([l],n.prototype,"interfaces",null),d([l],n.prototype,"methods",null),d([l],n.prototype,"name",null),d([l],n.prototype,"namespace",null),d([l],n.prototype,"nestedClasses",null),d([l],n.prototype,"parent",null),d([l],n.prototype,"pointerClass",null),d([l],n.prototype,"rank",null),d([l],n.prototype,"staticFieldsData",null),d([l],n.prototype,"valueTypeSize",null),d([l],n.prototype,"type",null),n=d([E],n),e.Class=n})(h||(h={}));var h;(function(e){function n(c,t){let s=e.corlib.class("System.Delegate"),r=e.corlib.class("System.MulticastDelegate");s.isAssignableFrom(c)||y(`cannot create a delegate for ${c.type.name} as it's a non-delegate class`),(c.equals(s)||c.equals(r))&&y(`cannot create a delegate for neither ${s.type.name} nor ${r.type.name}, use a subclass instead`);let o=c.alloc(),i=o.handle.toString(),a=o.tryMethod("Invoke")??y(`cannot create a delegate for ${c.type.name}, there is no Invoke method`);o.method(".ctor").invoke(o,a.handle);let u=a.wrap(t);return o.field("method_ptr").value=u,o.field("invoke_impl").value=u,e._callbacksToKeepAlive[i]=u,o}e.delegate=n,e._callbacksToKeepAlive={}})(h||(h={}));var h;(function(e){let n=class extends b{get assemblies(){let t=A(s=>e.exports.domainGetAssemblies(this,s));if(t.length==0){let s=this.object.method("GetAssemblies").overload().invoke();t=globalThis.Array.from(s).map(r=>r.field("_mono_assembly").value)}return t.map(s=>new e.Assembly(s))}get object(){return e.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(t){return this.tryAssembly(t)??y(`couldn't find assembly ${t}`)}attach(){return new e.Thread(e.exports.threadAttach(this))}tryAssembly(t){return new e.Assembly(e.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(t))).asNullable()}};d([l],n.prototype,"assemblies",null),d([l],n.prototype,"object",null),n=d([E],n),e.Domain=n,_(e,"domain",()=>new e.Domain(e.exports.domainGet()),l)})(h||(h={}));var h;(function(e){class n extends b{get class(){return new e.Class(e.exports.fieldGetClass(this))}get flags(){return e.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let t=e.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return _(e.Field.prototype,"isThreadStatic",function(){return this.offset==t},l),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.fieldGetName(this).readUtf8String()}get offset(){return e.exports.fieldGetOffset(this)}get type(){return new e.Type(e.exports.fieldGetType(this))}get value(){this.isStatic||y(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let t=Memory.alloc(Process.pointerSize);return e.exports.fieldGetStaticValue(this.handle,t),e.read(t,this.type)}set value(t){this.isStatic||y(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&y(`cannot write the value of field ${this.name} as it's thread static or literal`);let s=t instanceof e.Object&&this.type.class.isValueType?t.unbox():t instanceof b?t.handle:t instanceof NativePointer?t:e.write(Memory.alloc(this.type.class.valueTypeSize),t,this.type);e.exports.fieldSetStaticValue(this.handle,s)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?e.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(t){this.isStatic&&y(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let s=this.offset-(t instanceof e.ValueType?e.Object.headerSize:0);return new Proxy(this,{get(r,o){return o=="value"?e.read(t.handle.add(s),r.type):Reflect.get(r,o)},set(r,o,i){return o=="value"?(e.write(t.handle.add(s),i,r.type),!0):Reflect.set(r,o,i)}})}}d([l],n.prototype,"class",null),d([l],n.prototype,"flags",null),d([l],n.prototype,"isLiteral",null),d([l],n.prototype,"isStatic",null),d([l],n.prototype,"isThreadStatic",null),d([l],n.prototype,"modifier",null),d([l],n.prototype,"name",null),d([l],n.prototype,"offset",null),d([l],n.prototype,"type",null),e.Field=n})(h||(h={}));var h;(function(e){class n{handle;constructor(t){this.handle=t}get target(){return new e.Object(e.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return e.exports.gcHandleFree(this.handle)}}e.GCHandle=n})(h||(h={}));var h;(function(e){let n=class extends b{get assembly(){return new e.Assembly(e.exports.imageGetAssembly(this))}get classCount(){return e.unityVersionIsBelow201830?this.classes.length:e.exports.imageGetClassCount(this)}get classes(){if(e.unityVersionIsBelow201830){let t=this.assembly.object.method("GetTypes").invoke(!1),s=globalThis.Array.from(t,o=>new e.Class(e.exports.classFromObject(o))),r=this.tryClass("<Module>");return r&&s.unshift(r),s}else return globalThis.Array.from(globalThis.Array(this.classCount),(t,s)=>new e.Class(e.exports.imageGetClass(this,s)))}get name(){return e.exports.imageGetName(this).readUtf8String()}class(t){return this.tryClass(t)??y(`couldn't find class ${t} in assembly ${this.name}`)}tryClass(t){let s=t.lastIndexOf("."),r=Memory.allocUtf8String(s==-1?"":t.slice(0,s)),o=Memory.allocUtf8String(t.slice(s+1));return new e.Class(e.exports.classFromName(this,r,o)).asNullable()}};d([l],n.prototype,"assembly",null),d([l],n.prototype,"classCount",null),d([l],n.prototype,"classes",null),d([l],n.prototype,"name",null),n=d([E],n),e.Image=n,_(e,"corlib",()=>new e.Image(e.exports.getCorlib()),l)})(h||(h={}));var h;(function(e){class n extends b{static capture(){return new e.MemorySnapshot}constructor(s=e.exports.memorySnapshotCapture()){super(s)}get classes(){return S(s=>e.exports.memorySnapshotGetClasses(this,s)).map(s=>new e.Class(s))}get objects(){return A(s=>e.exports.memorySnapshotGetObjects(this,s)).filter(s=>!s.isNull()).map(s=>new e.Object(s))}free(){e.exports.memorySnapshotFree(this)}}d([l],n.prototype,"classes",null),d([l],n.prototype,"objects",null),e.MemorySnapshot=n;function c(t){let s=e.MemorySnapshot.capture(),r=t(s);return s.free(),r}e.memorySnapshot=c})(h||(h={}));var h;(function(e){class n extends b{get class(){return new e.Class(e.exports.methodGetClass(this))}get flags(){return e.exports.methodGetFlags(this,NULL)}get implementationFlags(){let s=Memory.alloc(Process.pointerSize);return e.exports.methodGetFlags(this,s),s.readU32()}get fridaSignature(){let s=[];for(let r of this.parameters)s.push(r.type.fridaAlias);return(!this.isStatic||e.unityVersionIsBelow201830)&&s.unshift("pointer"),this.isInflated&&s.push("pointer"),s}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let s=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(s).map(r=>new e.Class(e.exports.classFromObject(r)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!e.exports.methodIsGeneric(this)}get isInflated(){return!!e.exports.methodIsInflated(this)}get isStatic(){return!e.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new e.Object(e.exports.methodGetObject(this,NULL))}get parameterCount(){return e.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(s,r)=>{let o=e.exports.methodGetParameterName(this,r).readUtf8String(),i=e.exports.methodGetParameterType(this,r);return new e.Parameter(o,r,new e.Type(i))})}get relativeVirtualAddress(){return this.virtualAddress.sub(e.module.base)}get returnType(){return new e.Type(e.exports.methodGetReturnType(this))}get virtualAddress(){let s=e.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,r=s.field("method_ptr").value,i=s.field("method").value.offsetOf(a=>a.readPointer().equals(r))??y("couldn't find the virtual address offset in the native method struct");return _(e.Method.prototype,"virtualAddress",function(){return this.handle.add(i).readPointer()},l),e.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(s){try{Interceptor.replace(this.virtualAddress,this.wrap(s))}catch(r){switch(r.message){case"access violation accessing 0x0":y(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(r.message)?.input:v(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":v(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw r}}}inflate(...s){if(!this.isGeneric||this.generics.length!=s.length){for(let a of this.overloads())if(a.isGeneric&&a.generics.length==s.length)return a.inflate(...s);y(`could not find inflatable signature of method ${this.name} with ${s.length} generic parameter(s)`)}let r=s.map(a=>a.type.object),o=e.array(e.corlib.class("System.Type"),r),i=this.object.method("MakeGenericMethod",1).invoke(o);return new e.Method(i.field("mhandle").value)}invoke(...s){return this.isStatic||y(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...s)}invokeRaw(s,...r){let o=r.map(e.toFridaValue);(!this.isStatic||e.unityVersionIsBelow201830)&&o.unshift(s),this.isInflated&&o.push(this.handle);try{let i=this.nativeFunction(...o);return e.fromFridaValue(i,this.returnType)}catch(i){switch(i==null&&y("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),i.message){case"bad argument count":y(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${r.length}`);case"expected a pointer":case"expected number":case"expected array with fields":y(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw i}}overload(...s){return this.tryOverload(...s)??y(`couldn't find overloaded method ${this.name}(${s.map(o=>o instanceof e.Class?o.type.name:o)})`)}*overloads(){for(let s of this.class.hierarchy())for(let r of s.methods)this.name==r.name&&(yield r)}parameter(s){return this.tryParameter(s)??y(`couldn't find parameter ${s} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...s){let r=s.length*1,o=s.length*2,i;e:for(let a of this.overloads()){if(a.parameterCount!=s.length)continue;let u=0,m=0;for(let f of a.parameters){let g=s[m];if(g instanceof e.Class)if(f.type.is(g.type))u+=2;else if(f.type.class.isAssignableFrom(g))u+=1;else continue e;else if(f.type.name==g)u+=2;else continue e;m++}if(!(u<r)){if(u==o)return a;if(i==null||u>i[0])i=[u,a];else if(u==i[0]){let f=0;for(let g of i[1].parameters){if(g.type.class.isAssignableFrom(a.parameters[f].type.class)){i=[u,a];continue e}f++}}}}return i?.[1]}tryParameter(s){return this.parameters.find(r=>r.name==s)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(s=>s.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(s){return this.isStatic&&y(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(r,o,i){switch(o){case"invoke":let a=s instanceof e.ValueType?r.class.isValueType?s.handle.sub(c()?e.Object.headerSize:0):y(`cannot invoke method ${r.class.type.name}::${r.name} against a value type, you must box it first`):r.class.isValueType?s.handle.add(c()?0:e.Object.headerSize):s.handle;return r.invokeRaw.bind(r,a);case"overloads":return function*(){for(let m of r[o]())m.isStatic||(yield m)};case"inflate":case"overload":case"tryOverload":let u=Reflect.get(r,o).bind(i);return function(...m){return u(...m)?.bind(s)}}return Reflect.get(r,o)}})}wrap(s){let r=+!this.isStatic|+e.unityVersionIsBelow201830;return new NativeCallback((...o)=>{let i=this.isStatic?this.class:this.class.isValueType?new e.ValueType(o[0].add(c()?e.Object.headerSize:0),this.class.type):new e.Object(o[0]),a=this.parameters.map((m,f)=>e.fromFridaValue(o[f+r],m.type)),u=s.call(i,...a);return e.toFridaValue(u)},this.returnType.fridaAlias,this.fridaSignature)}}d([l],n.prototype,"class",null),d([l],n.prototype,"flags",null),d([l],n.prototype,"implementationFlags",null),d([l],n.prototype,"fridaSignature",null),d([l],n.prototype,"generics",null),d([l],n.prototype,"isExternal",null),d([l],n.prototype,"isGeneric",null),d([l],n.prototype,"isInflated",null),d([l],n.prototype,"isStatic",null),d([l],n.prototype,"isSynchronized",null),d([l],n.prototype,"modifier",null),d([l],n.prototype,"name",null),d([l],n.prototype,"nativeFunction",null),d([l],n.prototype,"object",null),d([l],n.prototype,"parameterCount",null),d([l],n.prototype,"parameters",null),d([l],n.prototype,"relativeVirtualAddress",null),d([l],n.prototype,"returnType",null),e.Method=n;let c=()=>{let t=e.corlib.class("System.Int64").alloc();t.field("m_value").value=3735928559;let s=t.method("Equals",1).overload(t.class).invokeRaw(t,3735928559);return(c=()=>s)()}})(h||(h={}));var h;(function(e){class n extends b{static get headerSize(){return e.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&y(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(t,s,r){return s=="class"?Reflect.get(t,s).parent:s=="base"?Reflect.getOwnPropertyDescriptor(e.Object.prototype,s).get.bind(r)():Reflect.get(t,s)}})}get class(){return new e.Class(e.exports.objectGetClass(this))}get monitor(){return new e.Object.Monitor(this)}get size(){return e.exports.objectGetSize(this)}field(t){return this.tryField(t)??y(`couldn't find non-static field ${t} in hierarchy of class ${this.class.type.name}`)}method(t,s=-1){return this.tryMethod(t,s)??y(`couldn't find non-static method ${t} in hierarchy of class ${this.class.type.name}`)}ref(t){return new e.GCHandle(e.exports.gcHandleNew(this,+t))}virtualMethod(t){return new e.Method(e.exports.objectGetVirtualMethod(this,t)).bind(this)}tryField(t){let s=this.class.tryField(t);if(s?.isStatic){for(let r of this.class.hierarchy({includeCurrent:!1}))for(let o of r.fields)if(o.name==t&&!o.isStatic)return o.bind(this);return}return s?.bind(this)}tryMethod(t,s=-1){let r=this.class.tryMethod(t,s);if(r?.isStatic){for(let o of this.class.hierarchy())for(let i of o.methods)if(i.name==t&&!i.isStatic&&(s<0||i.parameterCount==s))return i.bind(this);return}return r?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new e.ValueType(e.exports.objectUnbox(this),this.class.type):y(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(t){return new e.GCHandle(e.exports.gcHandleNewWeakRef(this,+t))}}d([l],n.prototype,"class",null),d([l],n.prototype,"size",null),d([l],n,"headerSize",null),e.Object=n,function(c){class t{handle;constructor(r){this.handle=r}enter(){return e.exports.monitorEnter(this.handle)}exit(){return e.exports.monitorExit(this.handle)}pulse(){return e.exports.monitorPulse(this.handle)}pulseAll(){return e.exports.monitorPulseAll(this.handle)}tryEnter(r){return!!e.exports.monitorTryEnter(this.handle,r)}tryWait(r){return!!e.exports.monitorTryWait(this.handle,r)}wait(){return e.exports.monitorWait(this.handle)}}c.Monitor=t}(n=e.Object||(e.Object={}))})(h||(h={}));var h;(function(e){class n{name;position;type;constructor(t,s,r){this.name=t,this.position=s,this.type=r}toString(){return`${this.type.name} ${this.name}`}}e.Parameter=n})(h||(h={}));var h;(function(e){class n extends b{type;constructor(t,s){super(t),this.type=s}get(t){return e.read(this.handle.add(t*this.type.class.arrayElementSize),this.type)}read(t,s=0){let r=new globalThis.Array(t);for(let o=0;o<t;o++)r[o]=this.get(o+s);return r}set(t,s){e.write(this.handle.add(t*this.type.class.arrayElementSize),s,this.type)}toString(){return this.handle.toString()}write(t,s=0){for(let r=0;r<t.length;r++)this.set(r+s,t[r])}}e.Pointer=n})(h||(h={}));var h;(function(e){class n extends b{type;constructor(s,r){super(s),this.type=r}get value(){return e.read(this.handle,this.type)}set value(s){e.write(this.handle,s,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}e.Reference=n;function c(t,s){let r=Memory.alloc(Process.pointerSize);switch(typeof t){case"boolean":return new e.Reference(r.writeS8(+t),e.corlib.class("System.Boolean").type);case"number":switch(s?.enumValue){case e.Type.Enum.UBYTE:return new e.Reference(r.writeU8(t),s);case e.Type.Enum.BYTE:return new e.Reference(r.writeS8(t),s);case e.Type.Enum.CHAR:case e.Type.Enum.USHORT:return new e.Reference(r.writeU16(t),s);case e.Type.Enum.SHORT:return new e.Reference(r.writeS16(t),s);case e.Type.Enum.UINT:return new e.Reference(r.writeU32(t),s);case e.Type.Enum.INT:return new e.Reference(r.writeS32(t),s);case e.Type.Enum.ULONG:return new e.Reference(r.writeU64(t),s);case e.Type.Enum.LONG:return new e.Reference(r.writeS64(t),s);case e.Type.Enum.FLOAT:return new e.Reference(r.writeFloat(t),s);case e.Type.Enum.DOUBLE:return new e.Reference(r.writeDouble(t),s)}case"object":if(t instanceof e.ValueType||t instanceof e.Pointer)return new e.Reference(t.handle,t.type);if(t instanceof e.Object)return new e.Reference(r.writePointer(t),t.class.type);if(t instanceof e.String||t instanceof e.Array)return new e.Reference(r.writePointer(t),t.object.class.type);if(t instanceof NativePointer)switch(s?.enumValue){case e.Type.Enum.NUINT:case e.Type.Enum.NINT:return new e.Reference(r.writePointer(t),s)}else{if(t instanceof Int64)return new e.Reference(r.writeS64(t),e.corlib.class("System.Int64").type);if(t instanceof UInt64)return new e.Reference(r.writeU64(t),e.corlib.class("System.UInt64").type)}default:y(`couldn't create a reference to ${t} using an unhandled type ${s?.name}`)}}e.reference=c})(h||(h={}));var h;(function(e){class n extends b{get content(){return e.exports.stringGetChars(this).readUtf16String(this.length)}set content(s){let r=e.string("vfsfitvnm").handle.offsetOf(o=>o.readInt()==9)??y("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(e.String.prototype,"content",{set(o){e.exports.stringGetChars(this).writeUtf16String(o??""),this.handle.add(r).writeS32(o?.length??0)}}),this.content=s}get length(){return e.exports.stringGetLength(this)}get object(){return new e.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}e.String=n;function c(t){return new e.String(e.exports.stringNew(Memory.allocUtf8String(t??"")))}e.string=c})(h||(h={}));var h;(function(e){class n extends b{get id(){let t=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let s=Process.getCurrentThreadId(),o=ptr(t.apply(e.currentThread)).offsetOf(a=>a.readS32()==s,1024)??y("couldn't find the offset for determining the kernel id of a posix thread"),i=t;t=function(){return ptr(i.apply(this)).add(o).readS32()}}return _(e.Thread.prototype,"id",t,l),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!e.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new e.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let s=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(s.tryField("_syncContext")?.value??s.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(e.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return e.exports.threadDetach(this)}schedule(t){let s=this.synchronizationContext?.tryMethod("Post");return s==null?Process.runOnThread(this.id,t):new Promise(r=>{let o=e.delegate(e.corlib.class("System.Threading.SendOrPostCallback"),()=>{let i=t();setImmediate(()=>r(i))});Script.bindWeak(globalThis,()=>{o.field("method_ptr").value=o.field("invoke_impl").value=e.exports.domainGet}),s.invoke(o,NULL)})}tryLocalValue(t){for(let s=0;s<16;s++){let r=this.staticData.add(s*Process.pointerSize).readPointer();if(!r.isNull()){let o=new e.Object(r.readPointer()).asNullable();if(o?.class?.isSubclassOf(t,!1))return o}}}}d([l],n.prototype,"internal",null),d([l],n.prototype,"isFinalizer",null),d([l],n.prototype,"managedId",null),d([l],n.prototype,"object",null),d([l],n.prototype,"staticData",null),d([l],n.prototype,"synchronizationContext",null),e.Thread=n,_(e,"attachedThreads",()=>{if(e.exports.threadGetAttachedThreads.isNull()){let c=e.currentThread?.handle??y("Current thread is not attached to IL2CPP"),t=c.toMatchPattern(),s=[];for(let r of Process.enumerateRanges("rw-"))if(r.file==null){let o=Memory.scanSync(r.base,r.size,t);if(o.length==1){for(;;){let i=o[0].address.sub(o[0].size*s.length).readPointer();if(i.isNull()||!i.readPointer().equals(c.readPointer()))break;s.unshift(new e.Thread(i))}break}}return s}return A(e.exports.threadGetAttachedThreads).map(c=>new e.Thread(c))}),_(e,"currentThread",()=>new e.Thread(e.exports.threadGetCurrent()).asNullable()),_(e,"mainThread",()=>e.attachedThreads[0])})(h||(h={}));var h;(function(e){let n=class extends b{static get Enum(){let t=(r,o=i=>i)=>o(e.corlib.class(r)).type.enumValue,s={VOID:t("System.Void"),BOOLEAN:t("System.Boolean"),CHAR:t("System.Char"),BYTE:t("System.SByte"),UBYTE:t("System.Byte"),SHORT:t("System.Int16"),USHORT:t("System.UInt16"),INT:t("System.Int32"),UINT:t("System.UInt32"),LONG:t("System.Int64"),ULONG:t("System.UInt64"),NINT:t("System.IntPtr"),NUINT:t("System.UIntPtr"),FLOAT:t("System.Single"),DOUBLE:t("System.Double"),POINTER:t("System.IntPtr",r=>r.field("m_value")),VALUE_TYPE:t("System.Decimal"),OBJECT:t("System.Object"),STRING:t("System.String"),CLASS:t("System.Array"),ARRAY:t("System.Void",r=>r.arrayClass),NARRAY:t("System.Void",r=>new e.Class(e.exports.classGetArrayClass(r,2))),GENERIC_INSTANCE:t("System.Int32",r=>r.interfaces.find(o=>o.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:s}),U({...s,VAR:t("System.Action`1",r=>r.generics[0]),MVAR:t("System.Array",r=>r.method("AsReadOnly",1).generics[0])})}get class(){return new e.Class(e.exports.typeGetClass(this))}get fridaAlias(){function t(s){let r=s.class.fields.filter(o=>!o.isStatic);return r.length==0?["char"]:r.map(o=>o.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case e.Type.Enum.VOID:return"void";case e.Type.Enum.BOOLEAN:return"bool";case e.Type.Enum.CHAR:return"uchar";case e.Type.Enum.BYTE:return"int8";case e.Type.Enum.UBYTE:return"uint8";case e.Type.Enum.SHORT:return"int16";case e.Type.Enum.USHORT:return"uint16";case e.Type.Enum.INT:return"int32";case e.Type.Enum.UINT:return"uint32";case e.Type.Enum.LONG:return"int64";case e.Type.Enum.ULONG:return"uint64";case e.Type.Enum.FLOAT:return"float";case e.Type.Enum.DOUBLE:return"double";case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return"pointer";case e.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:t(this);case e.Type.Enum.CLASS:case e.Type.Enum.OBJECT:case e.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?t(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case e.Type.Enum.BOOLEAN:case e.Type.Enum.CHAR:case e.Type.Enum.BYTE:case e.Type.Enum.UBYTE:case e.Type.Enum.SHORT:case e.Type.Enum.USHORT:case e.Type.Enum.INT:case e.Type.Enum.UINT:case e.Type.Enum.LONG:case e.Type.Enum.ULONG:case e.Type.Enum.FLOAT:case e.Type.Enum.DOUBLE:case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return!0;default:return!1}}get name(){let t=e.exports.typeGetName(this);try{return t.readUtf8String()}finally{e.free(t)}}get object(){return new e.Object(e.exports.typeGetObject(this))}get enumValue(){return e.exports.typeGetTypeEnum(this)}is(t){return e.exports.typeEquals.isNull()?this.object.method("Equals").invoke(t.object):!!e.exports.typeEquals(this,t)}toString(){return this.name}};d([l],n.prototype,"class",null),d([l],n.prototype,"fridaAlias",null),d([l],n.prototype,"isByReference",null),d([l],n.prototype,"isPrimitive",null),d([l],n.prototype,"name",null),d([l],n.prototype,"object",null),d([l],n.prototype,"enumValue",null),d([l],n,"Enum",null),n=d([E],n),e.Type=n})(h||(h={}));var h;(function(e){class n extends b{type;constructor(t,s){super(t),this.type=s}box(){return new e.Object(e.exports.valueTypeBox(this.type.class,this))}field(t){return this.tryField(t)??y(`couldn't find non-static field ${t} in hierarchy of class ${this.type.name}`)}method(t,s=-1){return this.tryMethod(t,s)??y(`couldn't find non-static method ${t} in hierarchy of class ${this.type.name}`)}tryField(t){let s=this.type.class.tryField(t);if(s?.isStatic){for(let r of this.type.class.hierarchy())for(let o of r.fields)if(o.name==t&&!o.isStatic)return o.bind(this);return}return s?.bind(this)}tryMethod(t,s=-1){let r=this.type.class.tryMethod(t,s);if(r?.isStatic){for(let o of this.type.class.hierarchy())for(let i of o.methods)if(i.name==t&&!i.isStatic&&(s<0||i.parameterCount==s))return i.bind(this);return}return r?.bind(this)}toString(){let t=this.method("ToString",0);return this.isNull()?"null":t.class.isValueType?t.invoke().content??"null":this.box().toString()??"null"}}e.ValueType=n})(h||(h={}));globalThis.Il2Cpp=h;Il2Cpp.perform(function(){console.log("\u{1F527} Loading Robust GoodyHutHelper Instance Manager...");class e{validInstances=[];collectibleInstances=[];batchSize=10;batchDelay=3e3;safeInvoke(t,s){try{if(!t||t.isNull())return{error:"Null instance",value:null};let r=t.method(s);return r?{error:null,value:r.invoke()}:{error:`Method ${s} not found`,value:null}}catch(r){let o=String(r);return o.includes("access violation")||o.includes("0x0")?{error:"Access violation - invalid instance",value:null}:{error:`Method error: ${o}`,value:null}}}validateInstance(t,s){console.log(`Validating instance from EntityController ${s}...`);let r={instance:t,entityIndex:s,isValid:!1,canCollect:!1,canBuyThrough:!1,state:"UNKNOWN",rewardType:"UNKNOWN",rewardAmount:null},o=this.safeInvoke(t,"IsJobComplete");if(o.error)return r.error=o.error,r;let i=this.safeInvoke(t,"CanCollect");if(i.error)return r.error=i.error,r;let a=this.safeInvoke(t,"CanBuyThrough");if(a.error)return r.error=a.error,r;let u=this.safeInvoke(t,"GetRewardType"),m=this.safeInvoke(t,"GetRewardAmount");r.isValid=!0,r.canCollect=i.value,r.canBuyThrough=a.value,r.rewardType=u.error?"UNKNOWN":String(u.value),r.rewardAmount=m.error?null:m.value;let f=o.value;return f===!0&&r.canCollect===!0?r.state="IDLE_READY":f===!1&&r.canCollect===!1?r.state="COLLECTING":f===!0&&r.canCollect===!1?r.state="COMPLETED_AWAITING":r.state="INCONSISTENT",console.log(`\u2705 EntityController ${s}: Valid - ${r.state} (CanCollect: ${r.canCollect}, Reward: ${r.rewardAmount} ${r.rewardType})`),r}scanAndValidateInstances(){console.log("\u{1F50D} Starting comprehensive instance validation..."),this.validInstances=[],this.collectibleInstances=[];try{let t=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),s=Il2Cpp.gc.choose(t);console.log(`Found ${s.length} EntityController instances`),console.log(`Validating GoodyHutHelper components...
`);let r=0,o=0,i=0,a=0;s.forEach((u,m)=>{try{let f=u.field("m_goodyHut");if(f&&f.value&&f.value!==null&&f.value.toString()!=="0x0"){let g=f.value,p=this.validateInstance(g,m);p.isValid?(this.validInstances.push(p),r++,p.canCollect&&(this.collectibleInstances.push(p),i++,p.rewardType==="GEMS"&&a++)):o++}}catch{o++}}),console.log(`
=== VALIDATION SUMMARY ===`),console.log(`\u2705 Valid instances: ${r}`),console.log(`\u274C Invalid instances: ${o}`),console.log(`\u{1F3AF} Collectible instances: ${i}`),console.log(`\u{1F48E} GEMS collectible instances: ${a}`),console.log(`\u{1F4CA} Success rate: ${Math.round(r/(r+o)*100)}%`)}catch(t){console.log(`\u274C Error during validation: ${t}`)}}showValidInstances(){console.log(`
=== VALID INSTANCES DETAILS ===`),this.validInstances.forEach((t,s)=>{console.log(`
[${s}] EntityController ${t.entityIndex}:`),console.log(`  State: ${t.state}`),console.log(`  CanCollect: ${t.canCollect}`),console.log(`  CanBuyThrough: ${t.canBuyThrough}`),console.log(`  Reward: ${t.rewardAmount} ${t.rewardType}`);let r=this.safeInvoke(t.instance,"GetJobTimeLeft"),o=this.safeInvoke(t.instance,"GetHealth"),i=this.safeInvoke(t.instance,"GetExplorations");r.error||console.log(`  TimeLeft: ${r.value}`),o.error||console.log(`  Health: ${o.value}`),i.error||console.log(`  Explorations: ${i.value}`)})}getBestCollectibleInstance(){if(this.collectibleInstances.length===0)return console.log("\u274C No collectible instances found"),null;let t=this.collectibleInstances.filter(r=>r.rewardType==="GEMS");if(t.length===0)return console.log("\u274C No collectible instances with GEMS rewards found"),console.log(`Available rewards: ${this.collectibleInstances.map(r=>`${r.rewardAmount} ${r.rewardType}`).join(", ")}`),null;console.log(`\u{1F48E} Found ${t.length} collectible instances with GEMS rewards`);let s=t.filter(r=>r.state==="IDLE_READY");return s.length>0?(console.log(`\u2705 Using IDLE_READY GEMS instance: ${s[0].rewardAmount} GEMS`),s[0]):(console.log(`\u2705 Using first GEMS collectible instance (${t[0].state}): ${t[0].rewardAmount} GEMS`),t[0])}refindInstanceByIndex(t){try{let s=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),r=Il2Cpp.gc.choose(s);if(t>=r.length)return console.log(`\u274C EntityController index ${t} out of range`),null;let i=r[t].field("m_goodyHut");if(i&&i.value&&i.value!==null&&i.value.toString()!=="0x0"){let a=i.value;return this.validateInstance(a,t)}else return console.log(`\u274C EntityController ${t} no longer has GoodyHutHelper component`),null}catch(s){return console.log(`\u274C Error re-finding instance at index ${t}: ${s}`),null}}monitorStateChanges(t,s=2e3){console.log(`
\u{1F50D} Monitoring state changes after ${s}ms delay...`),setTimeout(()=>{console.log(`
--- SCANNING FOR STATE CHANGES ---`);let r=[],o=0,i=0;t.forEach(a=>{let u=this.refindInstanceByIndex(a.entityIndex);u&&u.isValid?(r.push(u),(a.state!==u.state||a.canCollect!==u.canCollect||a.canBuyThrough!==u.canBuyThrough)&&(console.log(`\u{1F504} EntityController ${a.entityIndex}: ${a.state} \u2192 ${u.state}`),console.log(`   CanCollect: ${a.canCollect} \u2192 ${u.canCollect}`),console.log(`   CanBuyThrough: ${a.canBuyThrough} \u2192 ${u.canBuyThrough}`),o++,!a.canBuyThrough&&u.canBuyThrough&&(console.log(`
\u{1F3AF} CanBuyThrough became available on EntityController ${u.entityIndex}!`),this.testDoJobBuyThrough(u))),u.state==="COLLECTING"&&i++):console.log(`\u274C EntityController ${a.entityIndex}: Instance became invalid`)}),console.log(`
\u{1F4CA} State Change Summary:`),console.log(`   Changed instances: ${o}`),console.log(`   Currently collecting: ${i}`),console.log(`   Valid instances after: ${r.length}/${t.length}`),globalThis.afterSnapshot=r},s)}testDoJobBuyThrough(t){console.log(`
\u{1F48E} Testing DoJobBuyThrough on EntityController ${t.entityIndex}...`);let s=this.safeInvoke(t.instance,"DoJobBuyThrough");return s.error?(console.log(`\u274C DoJobBuyThrough failed: ${s.error}`),!1):(console.log(`\u2705 DoJobBuyThrough executed successfully! Result: ${s.value}`),setTimeout(()=>{console.log(`
--- STATE AFTER DoJobBuyThrough ---`);let r=this.refindInstanceByIndex(t.entityIndex);r&&r.isValid?(console.log(`State: ${r.state}`),console.log(`CanCollect: ${r.canCollect}`),console.log(`CanBuyThrough: ${r.canBuyThrough}`)):console.log("\u274C Instance became invalid after DoJobBuyThrough")},1e3),!0)}executeStartCollect(t){console.log(`
\u{1F680} Executing StartCollect on EntityController ${t.entityIndex}...`);let s=this.safeInvoke(t.instance,"CanCollect");if(s.error)return console.log(`\u274C Instance became invalid: ${s.error}`),!1;if(!s.value)return console.log("\u274C Instance no longer collectible"),!1;console.log("\u{1F4F8} Taking before-snapshot of all instances...");let r=[...this.validInstances],o=this.safeInvoke(t.instance,"StartCollect");return o.error?(console.log(`\u274C StartCollect failed: ${o.error}`),!1):(console.log(`\u2705 StartCollect executed successfully! Result: ${o.value}`),this.monitorStateChanges(r,1500),setTimeout(()=>{console.log(`
\u{1F50D} Re-scanning EntityController ${t.entityIndex}...`);let i=this.refindInstanceByIndex(t.entityIndex);i&&i.isValid?(console.log(`\u2705 Re-found instance: ${i.state}`),console.log(`   CanCollect: ${i.canCollect}`),console.log(`   CanBuyThrough: ${i.canBuyThrough}`),i.canBuyThrough&&(console.log(`
\u{1F3AF} CanBuyThrough is available - testing DoJobBuyThrough...`),this.testDoJobBuyThrough(i))):console.log("\u274C Could not re-find instance - may have been destroyed/recreated")},500),!0)}createBatches(t,s){let r=[];for(let o=0;o<t.length;o+=s)r.push(t.slice(o,o+s));return r}async validateBatch(t,s,r){let o=Math.min(s+r,t.length),i=[];console.log(`\u{1F50D} Validating batch instances ${s+1}-${o}...`);for(let a=s;a<o;a++)try{let m=t[a].field("m_goodyHut");if(m&&m.value&&m.value!==null&&m.value.toString()!=="0x0"){let f=m.value,g=this.validateInstance(f,a);g.isValid&&g.canCollect&&g.rewardType==="GEMS"&&i.push(g)}}catch{}return i}async collectBatch(t,s){if(t.length===0){console.log(`\u{1F4E6} Batch ${s}: No GEMS instances to collect`);return}console.log(`\u{1F680} Batch ${s}: Starting collection of ${t.length} GEMS instances...`);let r=t.map(async(a,u)=>{try{console.log(`  \u{1F48E} [${u+1}/${t.length}] Collecting ${a.rewardAmount} GEMS from EntityController ${a.entityIndex}`);let m=this.safeInvoke(a.instance,"StartCollect");if(m.error)return console.log(`    \u274C StartCollect failed: ${m.error}`),!1;await new Promise(g=>setTimeout(g,100));let f=this.safeInvoke(a.instance,"DoJobBuyThrough");return f.error?(console.log(`    \u274C DoJobBuyThrough failed: ${f.error}`),!1):(console.log(`    \u2705 Successfully collected ${a.rewardAmount} GEMS`),!0)}catch(m){return console.log(`    \u274C Collection error: ${m}`),!1}}),i=(await Promise.all(r)).filter(a=>a).length;console.log(`\u{1F4E6} Batch ${s} completed: ${i}/${t.length} successful collections`)}async processBatchCollection(){console.log("\u{1F504} Starting batch collection process...");try{let t=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),s=Il2Cpp.gc.choose(t);console.log(`\u{1F4CA} Found ${s.length} EntityController instances`);let r=Math.ceil(s.length/this.batchSize);console.log(`\u{1F4E6} Processing in ${r} batches of ${this.batchSize} instances each
`);let o=0;for(let i=0;i<r;i++){let a=i*this.batchSize,u=i+1;console.log(`
=== BATCH ${u}/${r} ===`);let m=await this.validateBatch(s,a,this.batchSize);console.log(`\u2705 Found ${m.length} valid GEMS instances in batch ${u}`),m.length>0&&(await this.collectBatch(m,u),o+=m.length),i<r-1&&(console.log(`\u23F3 Waiting ${this.batchDelay/1e3}s before next batch...`),await new Promise(f=>setTimeout(f,this.batchDelay)))}console.log(`
\u{1F389} Batch collection completed!`),console.log(`\u{1F4CA} Total GEMS instances collected: ${o}`)}catch(t){console.log(`\u274C Batch collection error: ${t}`)}}}let n=new e;globalThis.goodyManager={scan:()=>n.scanAndValidateInstances(),showValid:()=>n.showValidInstances(),getBest:()=>n.getBestCollectibleInstance(),startCollection:()=>{let c=n.getBestCollectibleInstance();return c?n.executeStartCollect(c):(console.log("\u274C No collectible instances available"),!1)},batchCollection:()=>n.processBatchCollection(),help:()=>{console.log("=== GoodyHut Instance Manager Commands ==="),console.log("goodyManager.scan() - Scan and validate all instances"),console.log("goodyManager.showValid() - Show details of valid instances"),console.log("goodyManager.getBest() - Get best collectible instance (GEMS only)"),console.log("goodyManager.startCollection() - Start collection on best GEMS instance"),console.log("goodyManager.batchCollection() - Process all GEMS instances in batches of 10"),console.log(""),console.log("\u{1F48E} NOTE: Only instances with GEMS rewards will be collected!"),console.log("\u{1F4E6} BATCH MODE: Use batchCollection() for processing multiple instances safely")}},console.log("\u{1F527} Robust GoodyHutHelper Instance Manager loaded!"),console.log("Use goodyManager.help() for available commands"),console.log("Start with: goodyManager.scan() or goodyManager.batchCollection()")});
