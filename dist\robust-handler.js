📦
78464 /src/robust-instance-handler.js
✄
var h=function(e,t,l,n){var s=arguments.length,r=s<3?t:n===null?n=Object.getOwnPropertyDescriptor(t,l):n,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(e,t,l,n);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(r=(s<3?o(r):s>3?o(t,l,r):o(t,l))||r);return s>3&&r&&Object.defineProperty(t,l,r),r},d;(function(e){e.application={get dataPath(){return t("get_persistentDataPath")},get identifier(){return t("get_identifier")??t("get_bundleIdentifier")??Process.mainModule.name},get version(){return t("get_version")??U(e.module).toString(16)}},S(e,"unityVersion",()=>{try{let n=e.$config.unityVersion??t("get_unityVersion");if(n!=null)return n}catch{}let l="69 6c 32 63 70 70";for(let n of e.module.enumerateRanges("r--").concat(Process.getRangeByAddress(e.module.base)))for(let{address:s}of Memory.scanSync(n.base,n.size,l)){for(;s.readU8()!=0;)s=s.sub(1);let r=G.find(s.add(1).readCString());if(r!=null)return r}g("couldn't determine the Unity version, please specify it manually")},c),S(e,"unityVersionIsBelow201830",()=>G.lt(e.unityVersion,"2018.3.0"),c),S(e,"unityVersionIsBelow202120",()=>G.lt(e.unityVersion,"2021.2.0"),c);function t(l){let n=e.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+l)),s=new NativeFunction(n,"pointer",[]);return s.isNull()?null:new e.String(s()).asNullable()?.content??null}})(d||(d={}));var d;(function(e){function t(l,n){let s={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},r=typeof l=="boolean"?"System.Boolean":typeof l=="number"?s[n??"int32"]:l instanceof Int64?"System.Int64":l instanceof UInt64?"System.UInt64":l instanceof NativePointer?s[n??"intptr"]:g(`Cannot create boxed primitive using value of type '${typeof l}'`),o=e.corlib.class(r??g(`Unknown primitive type name '${n}'`)).alloc();return(o.tryField("m_value")??o.tryField("_pointer")??g(`Could not find primitive field in class '${r}'`)).value=l,o}e.boxed=t})(d||(d={}));var d;(function(e){e.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(d||(d={}));var d;(function(e){function t(o,i){o=o??`${e.application.identifier}_${e.application.version}.cs`,i=i??e.application.dataPath??Process.getCurrentDir(),s(i);let a=`${i}/${o}`,u=new File(a,"w");for(let m of e.domain.assemblies){N(`dumping ${m.name}...`);for(let f of m.image.classes)u.write(`${f}

`)}u.flush(),u.close(),O(`dump saved to ${a}`),r()}e.dump=t;function l(o,i=!1){o=o??`${e.application.dataPath??Process.getCurrentDir()}/${e.application.identifier}_${e.application.version}`,!i&&n(o)&&g(`directory ${o} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let a of e.domain.assemblies){N(`dumping ${a.name}...`);let u=`${o}/${a.name.replaceAll(".","/")}.cs`;s(u.substring(0,u.lastIndexOf("/")));let m=new File(u,"w");for(let f of a.image.classes)m.write(`${f}

`);m.flush(),m.close()}O(`dump saved to ${o}`),r()}e.dumpTree=l;function n(o){return e.corlib.class("System.IO.Directory").method("Exists").invoke(e.string(o))}function s(o){e.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(e.string(o))}function r(){A("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(d||(d={}));var d;(function(e){function t(l="current"){let n=e.exports.threadGetCurrent();return Interceptor.attach(e.module.getExportByName("__cxa_throw"),function(s){l=="current"&&!e.exports.threadGetCurrent().equals(n)||N(new e.Object(s[0].readPointer()))})}e.installExceptionListener=t})(d||(d={}));var d;(function(e){e.exports={get alloc(){return t("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return t("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return t("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return t("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return t("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return t("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return t("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return t("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return t("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return t("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return t("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return t("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return t("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return t("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return t("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return t("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return t("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return t("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return t("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return t("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return t("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return t("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return t("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return t("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return t("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return t("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return t("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return t("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return t("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return t("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return t("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return t("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return t("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return t("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return t("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return t("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return t("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return t("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return t("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return t("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return t("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return t("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return t("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return t("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return t("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return t("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return t("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return t("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return t("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return t("il2cpp_free","void",["pointer"])},get gcCollect(){return t("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return t("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return t("il2cpp_gc_disable","void",[])},get gcEnable(){return t("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return t("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return t("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return t("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return t("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return t("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return t("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return t("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return t("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return t("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return t("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return t("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return t("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return t("il2cpp_stop_gc_world","void",[])},get getCorlib(){return t("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return t("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return t("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return t("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return t("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return t("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return t("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return t("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return t("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return t("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return t("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return t("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return t("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return t("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return t("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return t("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return t("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return t("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return t("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return t("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return t("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return t("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return t("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return t("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return t("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return t("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return t("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return t("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return t("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return t("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return t("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return t("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return t("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return t("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return t("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return t("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return t("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return t("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return t("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return t("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return t("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return t("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return t("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return t("il2cpp_string_length","int32",["pointer"])},get stringNew(){return t("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return t("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return t("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return t("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return t("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return t("il2cpp_thread_current","pointer",[])},get threadIsVm(){return t("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return t("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return t("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return t("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return t("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return t("il2cpp_type_get_type","int",["pointer"])}},j(e.exports,c),S(e,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),c);function t(l,n,s){let r=e.$config.exports?.[l]?.()??e.module.findExportByName(l)??e.memorySnapshotExports[l],o=new NativeFunction(r??NULL,n,s);return o.isNull()?new Proxy(o,{get(i,a){let u=i[a];return typeof u=="function"?u.bind(i):u},apply(){r==null?g(`couldn't resolve export ${l}`):r.isNull()&&g(`export ${l} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):o}})(d||(d={}));var d;(function(e){function t(n){return s=>s instanceof e.Class?n.isAssignableFrom(s):n.isAssignableFrom(s.class)}e.is=t;function l(n){return s=>s instanceof e.Class?s.equals(n):s.class.equals(n)}e.isExactly=l})(d||(d={}));var d;(function(e){e.gc={get heapSize(){return e.exports.gcGetHeapSize()},get isEnabled(){return!e.exports.gcIsDisabled()},get isIncremental(){return!!e.exports.gcIsIncremental()},get maxTimeSlice(){return e.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return e.exports.gcGetUsedSize()},set isEnabled(t){t?e.exports.gcEnable():e.exports.gcDisable()},set maxTimeSlice(t){e.exports.gcSetMaxTimeSlice(t)},choose(t){let l=[],n=(r,o)=>{for(let i=0;i<o;i++)l.push(new e.Object(r.add(i*Process.pointerSize).readPointer()))},s=new NativeCallback(n,"void",["pointer","int","pointer"]);if(e.unityVersionIsBelow202120){let r=new NativeCallback(()=>{},"void",[]),o=e.exports.livenessCalculationBegin(t,0,s,NULL,r,r);e.exports.livenessCalculationFromStatics(o),e.exports.livenessCalculationEnd(o)}else{let r=(a,u)=>!a.isNull()&&u.compare(0)==0?(e.free(a),NULL):e.alloc(u),o=new NativeCallback(r,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let i=e.exports.livenessAllocateStruct(t,0,s,NULL,o);e.exports.livenessCalculationFromStatics(i),e.exports.livenessFinalize(i),this.startWorld(),e.exports.livenessFreeStruct(i)}return l},collect(t){e.exports.gcCollect(t<0?0:t>2?2:t)},collectALittle(){e.exports.gcCollectALittle()},startWorld(){return e.exports.gcStartWorld()},startIncrementalCollection(){return e.exports.gcStartIncrementalCollection()},stopWorld(){return e.exports.gcStopWorld()}}})(d||(d={}));var M;(function(e){S(e,"apiLevel",()=>{let l=t("ro.build.version.sdk");return l?parseInt(l):null},c);function t(l){let n=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(n){let s=new NativeFunction(n,"void",["pointer","pointer"]),r=Memory.alloc(92).writePointer(NULL);return s(Memory.allocUtf8String(l),r),r.readCString()??void 0}}})(M||(M={}));function g(e){let t=new Error(e);throw t.name="Il2CppError",t.stack=t.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),t}function A(e){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${e}`)}function O(e){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${e}`)}function N(e){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${e}`)}function j(e,t,l=Object.getOwnPropertyDescriptors(e)){for(let n in l)l[n]=t(e,n,l[n]);return Object.defineProperties(e,l),e}function S(e,t,l,n){globalThis.Object.defineProperty(e,t,n?.(e,t,{get:l,configurable:!0})??{get:l,configurable:!0})}function k(e){let t=3735928559,l=1103547991;for(let n=0,s;n<e.length;n++)s=e.charCodeAt(n),t=Math.imul(t^s,2654435761),l=Math.imul(l^s,1597334677);return t=Math.imul(t^t>>>16,2246822507),t^=Math.imul(l^l>>>13,3266489909),l=Math.imul(l^l>>>16,2246822507),l^=Math.imul(t^t>>>13,3266489909),4294967296*(2097151&l)+(t>>>0)}function U(e){return k(e.enumerateExports().sort((t,l)=>t.name.localeCompare(l.name)).map(t=>t.name+t.address.sub(e.base)).join(""))}function c(e,t,l){let n=l.get;if(!n)throw new Error("@lazy can only be applied to getter accessors");return l.get=function(){let s=n.call(this);return Object.defineProperty(this,t,{value:s,configurable:l.configurable,enumerable:l.enumerable,writable:!1}),s},l}var _=class{handle;constructor(t){t instanceof NativePointer?this.handle=t:this.handle=t.handle}equals(t){return this.handle.equals(t.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function V(e){return Object.keys(e).reduce((t,l)=>(t[t[l]]=l,t),e)}NativePointer.prototype.offsetOf=function(e,t){t??=512;for(let l=0;t>0?l<t:l<-t;l++)if(e(t>0?this.add(l):this.sub(l)))return l;return null};function v(e){let t=[],l=Memory.alloc(Process.pointerSize),n=e(l);for(;!n.isNull();)t.push(n),n=e(l);return t}function P(e){let t=Memory.alloc(Process.pointerSize),l=e(t);if(l.isNull())return[];let n=new Array(t.readInt());for(let s=0;s<n.length;s++)n[s]=l.add(s*Process.pointerSize).readPointer();return n}function $(e){return new Proxy(e,{cache:new Map,construct(t,l){let n=l[0].toUInt32();return this.cache.has(n)||this.cache.set(n,new t(l[0])),this.cache.get(n)}})}var G;(function(e){let t=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function l(o){return o?.match(t)?.[0]}e.find=l;function n(o,i){return r(o,i)>=0}e.gte=n;function s(o,i){return r(o,i)<0}e.lt=s;function r(o,i){let a=o.match(t),u=i.match(t);for(let m=1;m<=3;m++){let f=Number(a?.[m]??-1),y=Number(u?.[m]??-1);if(f>y)return 1;if(f<y)return-1}return 0}})(G||(G={}));var d;(function(e){function t(i=Process.pointerSize){return e.exports.alloc(i)}e.alloc=t;function l(i){return e.exports.free(i)}e.free=l;function n(i,a){switch(a.enumValue){case e.Type.Enum.BOOLEAN:return!!i.readS8();case e.Type.Enum.BYTE:return i.readS8();case e.Type.Enum.UBYTE:return i.readU8();case e.Type.Enum.SHORT:return i.readS16();case e.Type.Enum.USHORT:return i.readU16();case e.Type.Enum.INT:return i.readS32();case e.Type.Enum.UINT:return i.readU32();case e.Type.Enum.CHAR:return i.readU16();case e.Type.Enum.LONG:return i.readS64();case e.Type.Enum.ULONG:return i.readU64();case e.Type.Enum.FLOAT:return i.readFloat();case e.Type.Enum.DOUBLE:return i.readDouble();case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return i.readPointer();case e.Type.Enum.POINTER:return new e.Pointer(i.readPointer(),a.class.baseType);case e.Type.Enum.VALUE_TYPE:return new e.ValueType(i,a);case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:return new e.Object(i.readPointer());case e.Type.Enum.GENERIC_INSTANCE:return a.class.isValueType?new e.ValueType(i,a):new e.Object(i.readPointer());case e.Type.Enum.STRING:return new e.String(i.readPointer());case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i.readPointer())}g(`couldn't read the value from ${i} using an unhandled or unknown type ${a.name} (${a.enumValue}), please file an issue`)}e.read=n;function s(i,a,u){switch(u.enumValue){case e.Type.Enum.BOOLEAN:return i.writeS8(+a);case e.Type.Enum.BYTE:return i.writeS8(a);case e.Type.Enum.UBYTE:return i.writeU8(a);case e.Type.Enum.SHORT:return i.writeS16(a);case e.Type.Enum.USHORT:return i.writeU16(a);case e.Type.Enum.INT:return i.writeS32(a);case e.Type.Enum.UINT:return i.writeU32(a);case e.Type.Enum.CHAR:return i.writeU16(a);case e.Type.Enum.LONG:return i.writeS64(a);case e.Type.Enum.ULONG:return i.writeU64(a);case e.Type.Enum.FLOAT:return i.writeFloat(a);case e.Type.Enum.DOUBLE:return i.writeDouble(a);case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return i.writePointer(a);case e.Type.Enum.VALUE_TYPE:return Memory.copy(i,a,u.class.valueTypeSize),i;case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:return a instanceof e.ValueType?(Memory.copy(i,a,u.class.valueTypeSize),i):i.writePointer(a)}g(`couldn't write value ${a} to ${i} using an unhandled or unknown type ${u.name} (${u.enumValue}), please file an issue`)}e.write=s;function r(i,a){if(globalThis.Array.isArray(i)){let u=Memory.alloc(a.class.valueTypeSize),m=a.class.fields.filter(f=>!f.isStatic);for(let f=0;f<m.length;f++){let y=r(i[f],m[f].type);s(u.add(m[f].offset).sub(e.Object.headerSize),y,m[f].type)}return new e.ValueType(u,a)}else if(i instanceof NativePointer){if(a.isByReference)return new e.Reference(i,a);switch(a.enumValue){case e.Type.Enum.POINTER:return new e.Pointer(i,a.class.baseType);case e.Type.Enum.STRING:return new e.String(i);case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:case e.Type.Enum.OBJECT:return new e.Object(i);case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i);default:return i}}else return a.enumValue==e.Type.Enum.BOOLEAN?!!i:a.enumValue==e.Type.Enum.VALUE_TYPE&&a.class.isEnum?r([i],a):i}e.fromFridaValue=r;function o(i){if(typeof i=="boolean")return+i;if(i instanceof e.ValueType){if(i.type.class.isEnum)return i.field("value__").value;{let a=i.type.class.fields.filter(u=>!u.isStatic).map(u=>o(u.bind(i).value));return a.length==0?[0]:a}}else return i}e.toFridaValue=o})(d||(d={}));var d;(function(e){S(e,"module",()=>l()??g("Could not find IL2CPP module"));async function t(s=!1){let r=l()??await new Promise(o=>{let[i,a]=n(),u=setTimeout(()=>{A(`after 10 seconds, IL2CPP module '${i}' has not been loaded yet, is the app running?`)},1e4),m=Process.attachModuleObserver({onAdded(f){(f.name==i||a&&f.name==a)&&(clearTimeout(u),setImmediate(()=>{o(f),m.detach()}))}})});return Reflect.defineProperty(e,"module",{value:r}),e.exports.getCorlib().isNull()?await new Promise(o=>{let i=Interceptor.attach(e.exports.initialize,{onLeave(){i.detach(),s?o(!0):setImmediate(()=>o(!1))}})}):!1}e.initialize=t;function l(){let[s,r]=n();return Process.findModuleByName(s)??Process.findModuleByName(r??s)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function n(){if(e.$config.moduleName)return[e.$config.moduleName];switch(Process.platform){case"linux":return[M.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}g(`${Process.platform} is not supported yet`)}})(d||(d={}));var d;(function(e){async function t(l,n="bind"){let s=null;try{let r=await e.initialize(n=="main");if(n=="main"&&!r)return t(()=>e.mainThread.schedule(l),"free");e.currentThread==null&&(s=e.domain.attach()),n=="bind"&&s!=null&&Script.bindWeak(globalThis,()=>s?.detach());let o=l();return o instanceof Promise?await o:o}catch(r){return Script.nextTick(o=>{throw o},r),Promise.reject(r)}finally{n=="free"&&s!=null&&s.detach()}}e.perform=t})(d||(d={}));var d;(function(e){class t{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let r=`
${this.#e.buffer.join(`
`)}
`;if(this.#h)N(r);else{let o=k(r);this.#e.history.has(o)||(this.#e.history.add(o),N(r))}this.#e.buffer.length=0}}};#u=e.mainThread.id;#h=!1;#d;#l=[];#c;#t;#n;#s;#r;#o;#i;#a;constructor(r){this.#d=r}thread(r){return this.#u=r.id,this}verbose(r){return this.#h=r,this}domain(){return this.#c=e.domain,this}assemblies(...r){return this.#t=r,this}classes(...r){return this.#n=r,this}methods(...r){return this.#s=r,this}filterAssemblies(r){return this.#r=r,this}filterClasses(r){return this.#o=r,this}filterMethods(r){return this.#i=r,this}filterParameters(r){return this.#a=r,this}and(){let r=y=>{if(this.#a==null){this.#l.push(y);return}for(let p of y.parameters)if(this.#a(p)){this.#l.push(y);break}},o=y=>{for(let p of y)r(p)},i=y=>{if(this.#i==null){o(y.methods);return}for(let p of y.methods)this.#i(p)&&r(p)},a=y=>{for(let p of y)i(p)},u=y=>{if(this.#o==null){a(y.image.classes);return}for(let p of y.image.classes)this.#o(p)&&i(p)},m=y=>{for(let p of y)u(p)},f=y=>{if(this.#r==null){m(y.assemblies);return}for(let p of y.assemblies)this.#r(p)&&u(p)};return this.#s?o(this.#s):this.#n?a(this.#n):this.#t?m(this.#t):this.#c&&f(this.#c),this.#t=void 0,this.#n=void 0,this.#s=void 0,this.#r=void 0,this.#o=void 0,this.#i=void 0,this.#a=void 0,this}attach(){for(let r of this.#l)if(!r.virtualAddress.isNull())try{this.#d(r,this.#e,this.#u)}catch(o){switch(o.message){case/unable to intercept function at \w+; please file a bug/.exec(o.message)?.input:case"already replaced this function":break;default:throw o}}}}e.Tracer=t;function l(s=!1){let r=()=>(i,a,u)=>{let m=i.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(i.virtualAddress,{onEnter(){this.threadId==u&&a.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(a.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==u&&(a.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(--a.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`),a.flush())}})},o=()=>(i,a,u)=>{let m=i.relativeVirtualAddress.toString(16).padStart(8,"0"),f=+!i.isStatic|+e.unityVersionIsBelow201830,y=function(...b){if(this.threadId==u){let w=i.isStatic?void 0:new e.Parameter("this",-1,i.class.type),x=w?[w].concat(i.parameters):i.parameters;a.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(a.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m(${x.map(E=>`\x1B[32m${E.name}\x1B[0m = \x1B[31m${e.fromFridaValue(b[E.position+f],E.type)}\x1B[0m`).join(", ")})`)}let T=i.nativeFunction(...b);return this.threadId==u&&(a.buffer.push(`\x1B[2m0x${m}\x1B[0m ${"\u2502 ".repeat(--a.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m${T==null?"":` = \x1B[36m${e.fromFridaValue(T,i.returnType)}`}\x1B[0m`),a.flush()),T};i.revert();let p=new NativeCallback(y,i.returnType.fridaAlias,i.fridaSignature);Interceptor.replace(i.virtualAddress,p)};return new e.Tracer(s?o():r())}e.trace=l;function n(s){let r=e.domain.assemblies.flatMap(a=>a.image.classes.flatMap(u=>u.methods.filter(m=>!m.virtualAddress.isNull()))).sort((a,u)=>a.virtualAddress.compare(u.virtualAddress)),o=a=>{let u=0,m=r.length-1;for(;u<=m;){let f=Math.floor((u+m)/2),y=r[f].virtualAddress.compare(a);if(y==0)return r[f];y>0?m=f-1:u=f+1}return r[m]},i=()=>(a,u,m)=>{Interceptor.attach(a.virtualAddress,function(){if(this.threadId==m){let f=globalThis.Thread.backtrace(this.context,s);f.unshift(a.virtualAddress);for(let y of f)if(y.compare(e.module.base)>0&&y.compare(e.module.base.add(e.module.size))<0){let p=o(y);if(p){let b=y.sub(p.virtualAddress);b.compare(4095)<0&&u.buffer.push(`\x1B[2m0x${p.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${b.toString(16).padStart(3,"0")}\x1B[0m ${p.class.type.name}::\x1B[1m${p.name}\x1B[0m`)}}u.flush()}})};return new e.Tracer(i())}e.backtrace=n})(d||(d={}));var d;(function(e){class t extends _{static get headerSize(){return e.corlib.class("System.Array").instanceSize}get elements(){let r=e.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(o=>o.readS16()==118)??g("couldn't find the elements offset in the native array struct");return S(e.Array.prototype,"elements",function(){return new e.Pointer(this.handle.add(r),this.elementType)},c),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return e.exports.arrayGetLength(this)}get object(){return new e.Object(this)}get(s){return(s<0||s>=this.length)&&g(`cannot get element at index ${s} as the array length is ${this.length}`),this.elements.get(s)}set(s,r){(s<0||s>=this.length)&&g(`cannot set element at index ${s} as the array length is ${this.length}`),this.elements.set(s,r)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let s=0;s<this.length;s++)yield this.elements.get(s)}}h([c],t.prototype,"elementSize",null),h([c],t.prototype,"elementType",null),h([c],t.prototype,"length",null),h([c],t.prototype,"object",null),h([c],t,"headerSize",null),e.Array=t;function l(n,s){let r=typeof s=="number"?s:s.length,o=new e.Array(e.exports.arrayNew(n,r));return globalThis.Array.isArray(s)&&o.elements.write(s),o}e.array=l})(d||(d={}));var d;(function(e){let t=class extends _{get image(){if(e.exports.assemblyGetImage.isNull()){let n=this.object.tryMethod("GetType",1)?.invoke(e.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??g(`couldn't find the runtime module object of assembly ${this.name}`);return new e.Image(n.field("_impl").value)}return new e.Image(e.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let n of e.domain.object.method("GetAssemblies",1).invoke(!1))if(n.field("_mono_assembly").value.equals(this))return n;g("couldn't find the object of the native assembly struct")}};h([c],t.prototype,"name",null),h([c],t.prototype,"object",null),t=h([$],t),e.Assembly=t})(d||(d={}));var d;(function(e){let t=class extends _{get actualInstanceSize(){let n=e.corlib.class("System.String"),s=n.handle.offsetOf(r=>r.readInt()==n.instanceSize-2)??g("couldn't find the actual instance size offset in the native class struct");return S(e.Class.prototype,"actualInstanceSize",function(){return this.handle.add(s).readS32()},c),this.actualInstanceSize}get arrayClass(){return new e.Class(e.exports.classGetArrayClass(this,1))}get arrayElementSize(){return e.exports.classGetArrayElementSize(this)}get assemblyName(){return e.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new e.Class(e.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new e.Type(e.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new e.Class(e.exports.classGetElementClass(this)).asNullable()}get fields(){return v(n=>e.exports.classGetFields(this,n)).map(n=>new e.Field(n))}get flags(){return e.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let n=this.image.tryClass(this.fullName)?.asNullable();return n?.equals(this)?null:n??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let n=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(n).map(s=>new e.Class(e.exports.classFromObject(s)))}get hasReferences(){return!!e.exports.classHasReferences(this)}get hasStaticConstructor(){let n=this.tryMethod(".cctor");return n!=null&&!n.virtualAddress.isNull()}get image(){return new e.Image(e.exports.classGetImage(this))}get instanceSize(){return e.exports.classGetInstanceSize(this)}get isAbstract(){return!!e.exports.classIsAbstract(this)}get isBlittable(){return!!e.exports.classIsBlittable(this)}get isEnum(){return!!e.exports.classIsEnum(this)}get isGeneric(){return!!e.exports.classIsGeneric(this)}get isInflated(){return!!e.exports.classIsInflated(this)}get isInterface(){return!!e.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!e.exports.classIsValueType(this)}get interfaces(){return v(n=>e.exports.classGetInterfaces(this,n)).map(n=>new e.Class(n))}get methods(){return v(n=>e.exports.classGetMethods(this,n)).map(n=>new e.Method(n))}get name(){return e.exports.classGetName(this).readUtf8String()}get namespace(){return e.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return v(n=>e.exports.classGetNestedClasses(this,n)).map(n=>new e.Class(n))}get parent(){return new e.Class(e.exports.classGetParent(this)).asNullable()}get pointerClass(){return new e.Class(e.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let n=0,s=this.name;for(let r=this.name.length-1;r>0;r--){let o=s[r];if(o=="]")n++;else{if(o=="["||n==0)break;if(o==",")n++;else break}}return n}get staticFieldsData(){return e.exports.classGetStaticFieldData(this)}get valueTypeSize(){return e.exports.classGetValueTypeSize(this,NULL)}get type(){return new e.Type(e.exports.classGetType(this))}alloc(){return new e.Object(e.exports.objectNew(this))}field(n){return this.tryField(n)??g(`couldn't find field ${n} in class ${this.type.name}`)}*hierarchy(n){let s=n?.includeCurrent??!0?this:this.parent;for(;s;)yield s,s=s.parent}inflate(...n){this.isGeneric||g(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=n.length&&g(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${n.length}`);let s=n.map(i=>i.type.object),r=e.array(e.corlib.class("System.Type"),s),o=this.type.object.method("MakeGenericType",1).invoke(r);return new e.Class(e.exports.classFromObject(o))}initialize(){return e.exports.classInitialize(this),this}isAssignableFrom(n){return!!e.exports.classIsAssignableFrom(this,n)}isSubclassOf(n,s){return!!e.exports.classIsSubclassOf(this,n,+s)}method(n,s=-1){return this.tryMethod(n,s)??g(`couldn't find method ${n} in class ${this.type.name}`)}nested(n){return this.tryNested(n)??g(`couldn't find nested class ${n} in class ${this.type.name}`)}new(){let n=this.alloc(),s=Memory.alloc(Process.pointerSize);e.exports.objectInitialize(n,s);let r=s.readPointer();return r.isNull()||g(new e.Object(r).toString()),n}tryField(n){return new e.Field(e.exports.classGetFieldFromName(this,Memory.allocUtf8String(n))).asNullable()}tryMethod(n,s=-1){return new e.Method(e.exports.classGetMethodFromName(this,Memory.allocUtf8String(n),s)).asNullable()}tryNested(n){return this.nestedClasses.find(s=>s.name==n)}toString(){let n=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${n?` : ${n.map(s=>s?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(n){let s=new NativeCallback(r=>n(new e.Class(r)),"void",["pointer","pointer"]);return e.exports.classForEach(s,NULL)}};h([c],t.prototype,"arrayClass",null),h([c],t.prototype,"arrayElementSize",null),h([c],t.prototype,"assemblyName",null),h([c],t.prototype,"declaringClass",null),h([c],t.prototype,"baseType",null),h([c],t.prototype,"elementClass",null),h([c],t.prototype,"fields",null),h([c],t.prototype,"flags",null),h([c],t.prototype,"fullName",null),h([c],t.prototype,"generics",null),h([c],t.prototype,"hasReferences",null),h([c],t.prototype,"hasStaticConstructor",null),h([c],t.prototype,"image",null),h([c],t.prototype,"instanceSize",null),h([c],t.prototype,"isAbstract",null),h([c],t.prototype,"isBlittable",null),h([c],t.prototype,"isEnum",null),h([c],t.prototype,"isGeneric",null),h([c],t.prototype,"isInflated",null),h([c],t.prototype,"isInterface",null),h([c],t.prototype,"isValueType",null),h([c],t.prototype,"interfaces",null),h([c],t.prototype,"methods",null),h([c],t.prototype,"name",null),h([c],t.prototype,"namespace",null),h([c],t.prototype,"nestedClasses",null),h([c],t.prototype,"parent",null),h([c],t.prototype,"pointerClass",null),h([c],t.prototype,"rank",null),h([c],t.prototype,"staticFieldsData",null),h([c],t.prototype,"valueTypeSize",null),h([c],t.prototype,"type",null),t=h([$],t),e.Class=t})(d||(d={}));var d;(function(e){function t(l,n){let s=e.corlib.class("System.Delegate"),r=e.corlib.class("System.MulticastDelegate");s.isAssignableFrom(l)||g(`cannot create a delegate for ${l.type.name} as it's a non-delegate class`),(l.equals(s)||l.equals(r))&&g(`cannot create a delegate for neither ${s.type.name} nor ${r.type.name}, use a subclass instead`);let o=l.alloc(),i=o.handle.toString(),a=o.tryMethod("Invoke")??g(`cannot create a delegate for ${l.type.name}, there is no Invoke method`);o.method(".ctor").invoke(o,a.handle);let u=a.wrap(n);return o.field("method_ptr").value=u,o.field("invoke_impl").value=u,e._callbacksToKeepAlive[i]=u,o}e.delegate=t,e._callbacksToKeepAlive={}})(d||(d={}));var d;(function(e){let t=class extends _{get assemblies(){let n=P(s=>e.exports.domainGetAssemblies(this,s));if(n.length==0){let s=this.object.method("GetAssemblies").overload().invoke();n=globalThis.Array.from(s).map(r=>r.field("_mono_assembly").value)}return n.map(s=>new e.Assembly(s))}get object(){return e.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(n){return this.tryAssembly(n)??g(`couldn't find assembly ${n}`)}attach(){return new e.Thread(e.exports.threadAttach(this))}tryAssembly(n){return new e.Assembly(e.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(n))).asNullable()}};h([c],t.prototype,"assemblies",null),h([c],t.prototype,"object",null),t=h([$],t),e.Domain=t,S(e,"domain",()=>new e.Domain(e.exports.domainGet()),c)})(d||(d={}));var d;(function(e){class t extends _{get class(){return new e.Class(e.exports.fieldGetClass(this))}get flags(){return e.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let n=e.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return S(e.Field.prototype,"isThreadStatic",function(){return this.offset==n},c),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.fieldGetName(this).readUtf8String()}get offset(){return e.exports.fieldGetOffset(this)}get type(){return new e.Type(e.exports.fieldGetType(this))}get value(){this.isStatic||g(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let n=Memory.alloc(Process.pointerSize);return e.exports.fieldGetStaticValue(this.handle,n),e.read(n,this.type)}set value(n){this.isStatic||g(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&g(`cannot write the value of field ${this.name} as it's thread static or literal`);let s=n instanceof e.Object&&this.type.class.isValueType?n.unbox():n instanceof _?n.handle:n instanceof NativePointer?n:e.write(Memory.alloc(this.type.class.valueTypeSize),n,this.type);e.exports.fieldSetStaticValue(this.handle,s)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?e.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(n){this.isStatic&&g(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let s=this.offset-(n instanceof e.ValueType?e.Object.headerSize:0);return new Proxy(this,{get(r,o){return o=="value"?e.read(n.handle.add(s),r.type):Reflect.get(r,o)},set(r,o,i){return o=="value"?(e.write(n.handle.add(s),i,r.type),!0):Reflect.set(r,o,i)}})}}h([c],t.prototype,"class",null),h([c],t.prototype,"flags",null),h([c],t.prototype,"isLiteral",null),h([c],t.prototype,"isStatic",null),h([c],t.prototype,"isThreadStatic",null),h([c],t.prototype,"modifier",null),h([c],t.prototype,"name",null),h([c],t.prototype,"offset",null),h([c],t.prototype,"type",null),e.Field=t})(d||(d={}));var d;(function(e){class t{handle;constructor(n){this.handle=n}get target(){return new e.Object(e.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return e.exports.gcHandleFree(this.handle)}}e.GCHandle=t})(d||(d={}));var d;(function(e){let t=class extends _{get assembly(){return new e.Assembly(e.exports.imageGetAssembly(this))}get classCount(){return e.unityVersionIsBelow201830?this.classes.length:e.exports.imageGetClassCount(this)}get classes(){if(e.unityVersionIsBelow201830){let n=this.assembly.object.method("GetTypes").invoke(!1),s=globalThis.Array.from(n,o=>new e.Class(e.exports.classFromObject(o))),r=this.tryClass("<Module>");return r&&s.unshift(r),s}else return globalThis.Array.from(globalThis.Array(this.classCount),(n,s)=>new e.Class(e.exports.imageGetClass(this,s)))}get name(){return e.exports.imageGetName(this).readUtf8String()}class(n){return this.tryClass(n)??g(`couldn't find class ${n} in assembly ${this.name}`)}tryClass(n){let s=n.lastIndexOf("."),r=Memory.allocUtf8String(s==-1?"":n.slice(0,s)),o=Memory.allocUtf8String(n.slice(s+1));return new e.Class(e.exports.classFromName(this,r,o)).asNullable()}};h([c],t.prototype,"assembly",null),h([c],t.prototype,"classCount",null),h([c],t.prototype,"classes",null),h([c],t.prototype,"name",null),t=h([$],t),e.Image=t,S(e,"corlib",()=>new e.Image(e.exports.getCorlib()),c)})(d||(d={}));var d;(function(e){class t extends _{static capture(){return new e.MemorySnapshot}constructor(s=e.exports.memorySnapshotCapture()){super(s)}get classes(){return v(s=>e.exports.memorySnapshotGetClasses(this,s)).map(s=>new e.Class(s))}get objects(){return P(s=>e.exports.memorySnapshotGetObjects(this,s)).filter(s=>!s.isNull()).map(s=>new e.Object(s))}free(){e.exports.memorySnapshotFree(this)}}h([c],t.prototype,"classes",null),h([c],t.prototype,"objects",null),e.MemorySnapshot=t;function l(n){let s=e.MemorySnapshot.capture(),r=n(s);return s.free(),r}e.memorySnapshot=l})(d||(d={}));var d;(function(e){class t extends _{get class(){return new e.Class(e.exports.methodGetClass(this))}get flags(){return e.exports.methodGetFlags(this,NULL)}get implementationFlags(){let s=Memory.alloc(Process.pointerSize);return e.exports.methodGetFlags(this,s),s.readU32()}get fridaSignature(){let s=[];for(let r of this.parameters)s.push(r.type.fridaAlias);return(!this.isStatic||e.unityVersionIsBelow201830)&&s.unshift("pointer"),this.isInflated&&s.push("pointer"),s}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let s=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(s).map(r=>new e.Class(e.exports.classFromObject(r)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!e.exports.methodIsGeneric(this)}get isInflated(){return!!e.exports.methodIsInflated(this)}get isStatic(){return!e.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new e.Object(e.exports.methodGetObject(this,NULL))}get parameterCount(){return e.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(s,r)=>{let o=e.exports.methodGetParameterName(this,r).readUtf8String(),i=e.exports.methodGetParameterType(this,r);return new e.Parameter(o,r,new e.Type(i))})}get relativeVirtualAddress(){return this.virtualAddress.sub(e.module.base)}get returnType(){return new e.Type(e.exports.methodGetReturnType(this))}get virtualAddress(){let s=e.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,r=s.field("method_ptr").value,i=s.field("method").value.offsetOf(a=>a.readPointer().equals(r))??g("couldn't find the virtual address offset in the native method struct");return S(e.Method.prototype,"virtualAddress",function(){return this.handle.add(i).readPointer()},c),e.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(s){try{Interceptor.replace(this.virtualAddress,this.wrap(s))}catch(r){switch(r.message){case"access violation accessing 0x0":g(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(r.message)?.input:A(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":A(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw r}}}inflate(...s){if(!this.isGeneric||this.generics.length!=s.length){for(let a of this.overloads())if(a.isGeneric&&a.generics.length==s.length)return a.inflate(...s);g(`could not find inflatable signature of method ${this.name} with ${s.length} generic parameter(s)`)}let r=s.map(a=>a.type.object),o=e.array(e.corlib.class("System.Type"),r),i=this.object.method("MakeGenericMethod",1).invoke(o);return new e.Method(i.field("mhandle").value)}invoke(...s){return this.isStatic||g(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...s)}invokeRaw(s,...r){let o=r.map(e.toFridaValue);(!this.isStatic||e.unityVersionIsBelow201830)&&o.unshift(s),this.isInflated&&o.push(this.handle);try{let i=this.nativeFunction(...o);return e.fromFridaValue(i,this.returnType)}catch(i){switch(i==null&&g("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),i.message){case"bad argument count":g(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${r.length}`);case"expected a pointer":case"expected number":case"expected array with fields":g(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw i}}overload(...s){return this.tryOverload(...s)??g(`couldn't find overloaded method ${this.name}(${s.map(o=>o instanceof e.Class?o.type.name:o)})`)}*overloads(){for(let s of this.class.hierarchy())for(let r of s.methods)this.name==r.name&&(yield r)}parameter(s){return this.tryParameter(s)??g(`couldn't find parameter ${s} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...s){let r=s.length*1,o=s.length*2,i;e:for(let a of this.overloads()){if(a.parameterCount!=s.length)continue;let u=0,m=0;for(let f of a.parameters){let y=s[m];if(y instanceof e.Class)if(f.type.is(y.type))u+=2;else if(f.type.class.isAssignableFrom(y))u+=1;else continue e;else if(f.type.name==y)u+=2;else continue e;m++}if(!(u<r)){if(u==o)return a;if(i==null||u>i[0])i=[u,a];else if(u==i[0]){let f=0;for(let y of i[1].parameters){if(y.type.class.isAssignableFrom(a.parameters[f].type.class)){i=[u,a];continue e}f++}}}}return i?.[1]}tryParameter(s){return this.parameters.find(r=>r.name==s)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(s=>s.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(s){return this.isStatic&&g(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(r,o,i){switch(o){case"invoke":let a=s instanceof e.ValueType?r.class.isValueType?s.handle.sub(l()?e.Object.headerSize:0):g(`cannot invoke method ${r.class.type.name}::${r.name} against a value type, you must box it first`):r.class.isValueType?s.handle.add(l()?0:e.Object.headerSize):s.handle;return r.invokeRaw.bind(r,a);case"overloads":return function*(){for(let m of r[o]())m.isStatic||(yield m)};case"inflate":case"overload":case"tryOverload":let u=Reflect.get(r,o).bind(i);return function(...m){return u(...m)?.bind(s)}}return Reflect.get(r,o)}})}wrap(s){let r=+!this.isStatic|+e.unityVersionIsBelow201830;return new NativeCallback((...o)=>{let i=this.isStatic?this.class:this.class.isValueType?new e.ValueType(o[0].add(l()?e.Object.headerSize:0),this.class.type):new e.Object(o[0]),a=this.parameters.map((m,f)=>e.fromFridaValue(o[f+r],m.type)),u=s.call(i,...a);return e.toFridaValue(u)},this.returnType.fridaAlias,this.fridaSignature)}}h([c],t.prototype,"class",null),h([c],t.prototype,"flags",null),h([c],t.prototype,"implementationFlags",null),h([c],t.prototype,"fridaSignature",null),h([c],t.prototype,"generics",null),h([c],t.prototype,"isExternal",null),h([c],t.prototype,"isGeneric",null),h([c],t.prototype,"isInflated",null),h([c],t.prototype,"isStatic",null),h([c],t.prototype,"isSynchronized",null),h([c],t.prototype,"modifier",null),h([c],t.prototype,"name",null),h([c],t.prototype,"nativeFunction",null),h([c],t.prototype,"object",null),h([c],t.prototype,"parameterCount",null),h([c],t.prototype,"parameters",null),h([c],t.prototype,"relativeVirtualAddress",null),h([c],t.prototype,"returnType",null),e.Method=t;let l=()=>{let n=e.corlib.class("System.Int64").alloc();n.field("m_value").value=3735928559;let s=n.method("Equals",1).overload(n.class).invokeRaw(n,3735928559);return(l=()=>s)()}})(d||(d={}));var d;(function(e){class t extends _{static get headerSize(){return e.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&g(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(n,s,r){return s=="class"?Reflect.get(n,s).parent:s=="base"?Reflect.getOwnPropertyDescriptor(e.Object.prototype,s).get.bind(r)():Reflect.get(n,s)}})}get class(){return new e.Class(e.exports.objectGetClass(this))}get monitor(){return new e.Object.Monitor(this)}get size(){return e.exports.objectGetSize(this)}field(n){return this.tryField(n)??g(`couldn't find non-static field ${n} in hierarchy of class ${this.class.type.name}`)}method(n,s=-1){return this.tryMethod(n,s)??g(`couldn't find non-static method ${n} in hierarchy of class ${this.class.type.name}`)}ref(n){return new e.GCHandle(e.exports.gcHandleNew(this,+n))}virtualMethod(n){return new e.Method(e.exports.objectGetVirtualMethod(this,n)).bind(this)}tryField(n){let s=this.class.tryField(n);if(s?.isStatic){for(let r of this.class.hierarchy({includeCurrent:!1}))for(let o of r.fields)if(o.name==n&&!o.isStatic)return o.bind(this);return}return s?.bind(this)}tryMethod(n,s=-1){let r=this.class.tryMethod(n,s);if(r?.isStatic){for(let o of this.class.hierarchy())for(let i of o.methods)if(i.name==n&&!i.isStatic&&(s<0||i.parameterCount==s))return i.bind(this);return}return r?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new e.ValueType(e.exports.objectUnbox(this),this.class.type):g(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(n){return new e.GCHandle(e.exports.gcHandleNewWeakRef(this,+n))}}h([c],t.prototype,"class",null),h([c],t.prototype,"size",null),h([c],t,"headerSize",null),e.Object=t,function(l){class n{handle;constructor(r){this.handle=r}enter(){return e.exports.monitorEnter(this.handle)}exit(){return e.exports.monitorExit(this.handle)}pulse(){return e.exports.monitorPulse(this.handle)}pulseAll(){return e.exports.monitorPulseAll(this.handle)}tryEnter(r){return!!e.exports.monitorTryEnter(this.handle,r)}tryWait(r){return!!e.exports.monitorTryWait(this.handle,r)}wait(){return e.exports.monitorWait(this.handle)}}l.Monitor=n}(t=e.Object||(e.Object={}))})(d||(d={}));var d;(function(e){class t{name;position;type;constructor(n,s,r){this.name=n,this.position=s,this.type=r}toString(){return`${this.type.name} ${this.name}`}}e.Parameter=t})(d||(d={}));var d;(function(e){class t extends _{type;constructor(n,s){super(n),this.type=s}get(n){return e.read(this.handle.add(n*this.type.class.arrayElementSize),this.type)}read(n,s=0){let r=new globalThis.Array(n);for(let o=0;o<n;o++)r[o]=this.get(o+s);return r}set(n,s){e.write(this.handle.add(n*this.type.class.arrayElementSize),s,this.type)}toString(){return this.handle.toString()}write(n,s=0){for(let r=0;r<n.length;r++)this.set(r+s,n[r])}}e.Pointer=t})(d||(d={}));var d;(function(e){class t extends _{type;constructor(s,r){super(s),this.type=r}get value(){return e.read(this.handle,this.type)}set value(s){e.write(this.handle,s,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}e.Reference=t;function l(n,s){let r=Memory.alloc(Process.pointerSize);switch(typeof n){case"boolean":return new e.Reference(r.writeS8(+n),e.corlib.class("System.Boolean").type);case"number":switch(s?.enumValue){case e.Type.Enum.UBYTE:return new e.Reference(r.writeU8(n),s);case e.Type.Enum.BYTE:return new e.Reference(r.writeS8(n),s);case e.Type.Enum.CHAR:case e.Type.Enum.USHORT:return new e.Reference(r.writeU16(n),s);case e.Type.Enum.SHORT:return new e.Reference(r.writeS16(n),s);case e.Type.Enum.UINT:return new e.Reference(r.writeU32(n),s);case e.Type.Enum.INT:return new e.Reference(r.writeS32(n),s);case e.Type.Enum.ULONG:return new e.Reference(r.writeU64(n),s);case e.Type.Enum.LONG:return new e.Reference(r.writeS64(n),s);case e.Type.Enum.FLOAT:return new e.Reference(r.writeFloat(n),s);case e.Type.Enum.DOUBLE:return new e.Reference(r.writeDouble(n),s)}case"object":if(n instanceof e.ValueType||n instanceof e.Pointer)return new e.Reference(n.handle,n.type);if(n instanceof e.Object)return new e.Reference(r.writePointer(n),n.class.type);if(n instanceof e.String||n instanceof e.Array)return new e.Reference(r.writePointer(n),n.object.class.type);if(n instanceof NativePointer)switch(s?.enumValue){case e.Type.Enum.NUINT:case e.Type.Enum.NINT:return new e.Reference(r.writePointer(n),s)}else{if(n instanceof Int64)return new e.Reference(r.writeS64(n),e.corlib.class("System.Int64").type);if(n instanceof UInt64)return new e.Reference(r.writeU64(n),e.corlib.class("System.UInt64").type)}default:g(`couldn't create a reference to ${n} using an unhandled type ${s?.name}`)}}e.reference=l})(d||(d={}));var d;(function(e){class t extends _{get content(){return e.exports.stringGetChars(this).readUtf16String(this.length)}set content(s){let r=e.string("vfsfitvnm").handle.offsetOf(o=>o.readInt()==9)??g("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(e.String.prototype,"content",{set(o){e.exports.stringGetChars(this).writeUtf16String(o??""),this.handle.add(r).writeS32(o?.length??0)}}),this.content=s}get length(){return e.exports.stringGetLength(this)}get object(){return new e.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}e.String=t;function l(n){return new e.String(e.exports.stringNew(Memory.allocUtf8String(n??"")))}e.string=l})(d||(d={}));var d;(function(e){class t extends _{get id(){let n=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let s=Process.getCurrentThreadId(),o=ptr(n.apply(e.currentThread)).offsetOf(a=>a.readS32()==s,1024)??g("couldn't find the offset for determining the kernel id of a posix thread"),i=n;n=function(){return ptr(i.apply(this)).add(o).readS32()}}return S(e.Thread.prototype,"id",n,c),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!e.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new e.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let s=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(s.tryField("_syncContext")?.value??s.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(e.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return e.exports.threadDetach(this)}schedule(n){let s=this.synchronizationContext?.tryMethod("Post");return s==null?Process.runOnThread(this.id,n):new Promise(r=>{let o=e.delegate(e.corlib.class("System.Threading.SendOrPostCallback"),()=>{let i=n();setImmediate(()=>r(i))});Script.bindWeak(globalThis,()=>{o.field("method_ptr").value=o.field("invoke_impl").value=e.exports.domainGet}),s.invoke(o,NULL)})}tryLocalValue(n){for(let s=0;s<16;s++){let r=this.staticData.add(s*Process.pointerSize).readPointer();if(!r.isNull()){let o=new e.Object(r.readPointer()).asNullable();if(o?.class?.isSubclassOf(n,!1))return o}}}}h([c],t.prototype,"internal",null),h([c],t.prototype,"isFinalizer",null),h([c],t.prototype,"managedId",null),h([c],t.prototype,"object",null),h([c],t.prototype,"staticData",null),h([c],t.prototype,"synchronizationContext",null),e.Thread=t,S(e,"attachedThreads",()=>{if(e.exports.threadGetAttachedThreads.isNull()){let l=e.currentThread?.handle??g("Current thread is not attached to IL2CPP"),n=l.toMatchPattern(),s=[];for(let r of Process.enumerateRanges("rw-"))if(r.file==null){let o=Memory.scanSync(r.base,r.size,n);if(o.length==1){for(;;){let i=o[0].address.sub(o[0].size*s.length).readPointer();if(i.isNull()||!i.readPointer().equals(l.readPointer()))break;s.unshift(new e.Thread(i))}break}}return s}return P(e.exports.threadGetAttachedThreads).map(l=>new e.Thread(l))}),S(e,"currentThread",()=>new e.Thread(e.exports.threadGetCurrent()).asNullable()),S(e,"mainThread",()=>e.attachedThreads[0])})(d||(d={}));var d;(function(e){let t=class extends _{static get Enum(){let n=(r,o=i=>i)=>o(e.corlib.class(r)).type.enumValue,s={VOID:n("System.Void"),BOOLEAN:n("System.Boolean"),CHAR:n("System.Char"),BYTE:n("System.SByte"),UBYTE:n("System.Byte"),SHORT:n("System.Int16"),USHORT:n("System.UInt16"),INT:n("System.Int32"),UINT:n("System.UInt32"),LONG:n("System.Int64"),ULONG:n("System.UInt64"),NINT:n("System.IntPtr"),NUINT:n("System.UIntPtr"),FLOAT:n("System.Single"),DOUBLE:n("System.Double"),POINTER:n("System.IntPtr",r=>r.field("m_value")),VALUE_TYPE:n("System.Decimal"),OBJECT:n("System.Object"),STRING:n("System.String"),CLASS:n("System.Array"),ARRAY:n("System.Void",r=>r.arrayClass),NARRAY:n("System.Void",r=>new e.Class(e.exports.classGetArrayClass(r,2))),GENERIC_INSTANCE:n("System.Int32",r=>r.interfaces.find(o=>o.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:s}),V({...s,VAR:n("System.Action`1",r=>r.generics[0]),MVAR:n("System.Array",r=>r.method("AsReadOnly",1).generics[0])})}get class(){return new e.Class(e.exports.typeGetClass(this))}get fridaAlias(){function n(s){let r=s.class.fields.filter(o=>!o.isStatic);return r.length==0?["char"]:r.map(o=>o.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case e.Type.Enum.VOID:return"void";case e.Type.Enum.BOOLEAN:return"bool";case e.Type.Enum.CHAR:return"uchar";case e.Type.Enum.BYTE:return"int8";case e.Type.Enum.UBYTE:return"uint8";case e.Type.Enum.SHORT:return"int16";case e.Type.Enum.USHORT:return"uint16";case e.Type.Enum.INT:return"int32";case e.Type.Enum.UINT:return"uint32";case e.Type.Enum.LONG:return"int64";case e.Type.Enum.ULONG:return"uint64";case e.Type.Enum.FLOAT:return"float";case e.Type.Enum.DOUBLE:return"double";case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return"pointer";case e.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:n(this);case e.Type.Enum.CLASS:case e.Type.Enum.OBJECT:case e.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?n(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case e.Type.Enum.BOOLEAN:case e.Type.Enum.CHAR:case e.Type.Enum.BYTE:case e.Type.Enum.UBYTE:case e.Type.Enum.SHORT:case e.Type.Enum.USHORT:case e.Type.Enum.INT:case e.Type.Enum.UINT:case e.Type.Enum.LONG:case e.Type.Enum.ULONG:case e.Type.Enum.FLOAT:case e.Type.Enum.DOUBLE:case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return!0;default:return!1}}get name(){let n=e.exports.typeGetName(this);try{return n.readUtf8String()}finally{e.free(n)}}get object(){return new e.Object(e.exports.typeGetObject(this))}get enumValue(){return e.exports.typeGetTypeEnum(this)}is(n){return e.exports.typeEquals.isNull()?this.object.method("Equals").invoke(n.object):!!e.exports.typeEquals(this,n)}toString(){return this.name}};h([c],t.prototype,"class",null),h([c],t.prototype,"fridaAlias",null),h([c],t.prototype,"isByReference",null),h([c],t.prototype,"isPrimitive",null),h([c],t.prototype,"name",null),h([c],t.prototype,"object",null),h([c],t.prototype,"enumValue",null),h([c],t,"Enum",null),t=h([$],t),e.Type=t})(d||(d={}));var d;(function(e){class t extends _{type;constructor(n,s){super(n),this.type=s}box(){return new e.Object(e.exports.valueTypeBox(this.type.class,this))}field(n){return this.tryField(n)??g(`couldn't find non-static field ${n} in hierarchy of class ${this.type.name}`)}method(n,s=-1){return this.tryMethod(n,s)??g(`couldn't find non-static method ${n} in hierarchy of class ${this.type.name}`)}tryField(n){let s=this.type.class.tryField(n);if(s?.isStatic){for(let r of this.type.class.hierarchy())for(let o of r.fields)if(o.name==n&&!o.isStatic)return o.bind(this);return}return s?.bind(this)}tryMethod(n,s=-1){let r=this.type.class.tryMethod(n,s);if(r?.isStatic){for(let o of this.type.class.hierarchy())for(let i of o.methods)if(i.name==n&&!i.isStatic&&(s<0||i.parameterCount==s))return i.bind(this);return}return r?.bind(this)}toString(){let n=this.method("ToString",0);return this.isNull()?"null":n.class.isValueType?n.invoke().content??"null":this.box().toString()??"null"}}e.ValueType=t})(d||(d={}));globalThis.Il2Cpp=d;Il2Cpp.perform(function(){console.log("\u{1F527} Loading Robust GoodyHutHelper Instance Manager...");let e=!1,t=0;function l(){try{console.log("\u{1F3AF} Installing GetCollectTime hook for instant collection completion...");let o=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("GoodyHutHelper");if(!o){console.log("\u274C GoodyHutHelper class not found");return}let i=o.method("GetCollectTime");if(!i){console.log("\u274C GetCollectTime method not found");return}console.log(`\u{1F3AF} Found GetCollectTime method at: ${i.handle}`),i.implementation=function(){return t++,t%10===1&&console.log(`\u26A1 GetCollectTime hook triggered (call #${t}) - returning 0 for instant completion`),0},e=!0,console.log("\u2705 GetCollectTime hook installed successfully!"),console.log("\u{1F4A1} All GoodyHut collections will now complete instantly")}catch(r){console.log(`\u274C Failed to install GetCollectTime hook: ${r}`),e=!1}}l();class n{validInstances=[];collectibleInstances=[];batchSize=10;batchDelay=3e3;attemptInstantCompletion=!0;buyThroughFailureCount=0;buyThroughSuccessCount=0;buyThroughFailureThreshold=20;safeInvoke(o,i){try{if(!o||o.isNull())return{error:"Null instance",value:null};let a=o.method(i);return a?{error:null,value:a.invoke()}:{error:`Method ${i} not found`,value:null}}catch(a){let u=String(a);return u.includes("access violation")||u.includes("0x0")?{error:"Access violation - invalid instance",value:null}:{error:`Method error: ${u}`,value:null}}}validateInstance(o,i){console.log(`Validating instance from EntityController ${i}...`);let a={instance:o,entityIndex:i,isValid:!1,canCollect:!1,canBuyThrough:!1,state:"UNKNOWN",rewardType:"UNKNOWN",rewardAmount:null},u=this.safeInvoke(o,"IsJobComplete");if(u.error)return a.error=u.error,a;let m=this.safeInvoke(o,"CanCollect");if(m.error)return a.error=m.error,a;let f=this.safeInvoke(o,"CanBuyThrough");if(f.error)return a.error=f.error,a;let y=this.safeInvoke(o,"GetRewardType"),p=this.safeInvoke(o,"GetRewardAmount");a.isValid=!0,a.canCollect=m.value,a.canBuyThrough=f.value,a.rewardType=y.error?"UNKNOWN":String(y.value),a.rewardAmount=p.error?null:p.value;let b=u.value;return b===!0&&a.canCollect===!0?a.state="IDLE_READY":b===!1&&a.canCollect===!1?a.state="COLLECTING":b===!0&&a.canCollect===!1?a.state="COMPLETED_AWAITING":a.state="INCONSISTENT",console.log(`\u2705 EntityController ${i}: Valid - ${a.state} (CanCollect: ${a.canCollect}, Reward: ${a.rewardAmount} ${a.rewardType})`),a}scanAndValidateInstances(){console.log("\u{1F50D} Starting comprehensive instance validation..."),this.validInstances=[],this.collectibleInstances=[];try{let o=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),i=Il2Cpp.gc.choose(o);console.log(`Found ${i.length} EntityController instances`),console.log(`Validating GoodyHutHelper components...
`);let a=0,u=0,m=0,f=0;i.forEach((y,p)=>{try{let b=y.field("m_goodyHut");if(b&&b.value&&b.value!==null&&b.value.toString()!=="0x0"){let T=b.value,w=this.validateInstance(T,p);w.isValid?(this.validInstances.push(w),a++,w.canCollect&&(this.collectibleInstances.push(w),m++,w.rewardType==="GEMS"&&f++)):u++}}catch{u++}}),console.log(`
=== VALIDATION SUMMARY ===`),console.log(`\u2705 Valid instances: ${a}`),console.log(`\u274C Invalid instances: ${u}`),console.log(`\u{1F3AF} Collectible instances: ${m}`),console.log(`\u{1F48E} GEMS collectible instances: ${f}`),console.log(`\u{1F4CA} Success rate: ${Math.round(a/(a+u)*100)}%`)}catch(o){console.log(`\u274C Error during validation: ${o}`)}}showValidInstances(){console.log(`
=== VALID INSTANCES DETAILS ===`),this.validInstances.forEach((o,i)=>{console.log(`
[${i}] EntityController ${o.entityIndex}:`),console.log(`  State: ${o.state}`),console.log(`  CanCollect: ${o.canCollect}`),console.log(`  CanBuyThrough: ${o.canBuyThrough}`),console.log(`  Reward: ${o.rewardAmount} ${o.rewardType}`);let a=this.safeInvoke(o.instance,"GetJobTimeLeft"),u=this.safeInvoke(o.instance,"GetHealth"),m=this.safeInvoke(o.instance,"GetExplorations");a.error||console.log(`  TimeLeft: ${a.value}`),u.error||console.log(`  Health: ${u.value}`),m.error||console.log(`  Explorations: ${m.value}`)})}getBestCollectibleInstance(){if(this.collectibleInstances.length===0)return console.log("\u274C No collectible instances found"),null;let o=this.collectibleInstances.filter(a=>a.rewardType==="GEMS");if(o.length===0)return console.log("\u274C No collectible instances with GEMS rewards found"),console.log(`Available rewards: ${this.collectibleInstances.map(a=>`${a.rewardAmount} ${a.rewardType}`).join(", ")}`),null;console.log(`\u{1F48E} Found ${o.length} collectible instances with GEMS rewards`);let i=o.filter(a=>a.state==="IDLE_READY");return i.length>0?(console.log(`\u2705 Using IDLE_READY GEMS instance: ${i[0].rewardAmount} GEMS`),i[0]):(console.log(`\u2705 Using first GEMS collectible instance (${o[0].state}): ${o[0].rewardAmount} GEMS`),o[0])}shouldAttemptInstantCompletion(o){return this.attemptInstantCompletion?this.buyThroughFailureCount>=this.buyThroughFailureThreshold?(console.log(`\u26A0\uFE0F Disabling instant completion after ${this.buyThroughFailureCount} consecutive failures`),this.attemptInstantCompletion=!1,!1):!!o.canBuyThrough:!1}executeInstantCompletion(o){let i=this.safeInvoke(o.instance,"DoJobBuyThrough");return i.error?(this.buyThroughFailureCount++,i.error.includes("abort was called")?console.log("    \u26A0\uFE0F Instant completion blocked (insufficient currency or game restriction)"):console.log(`    \u26A0\uFE0F Instant completion failed: ${i.error}`),!1):(this.buyThroughSuccessCount++,this.buyThroughFailureCount=0,console.log("    \u{1F48E} Instant completion successful!"),!0)}testDoJobBuyThrough(o){console.log(`
\u{1F48E} Executing DoJobBuyThrough on EntityController ${o.entityIndex}...`);let i=this.safeInvoke(o.instance,"DoJobBuyThrough");return i.error?(console.log(`\u26A0\uFE0F DoJobBuyThrough not available: ${i.error}`),!1):(console.log("\u2705 DoJobBuyThrough executed successfully - instant completion!"),!0)}executeStartCollect(o){console.log(`
\u{1F680} Executing StartCollect on EntityController ${o.entityIndex}...`);let i=this.safeInvoke(o.instance,"CanCollect");if(i.error)return console.log(`\u274C Instance became invalid: ${i.error}`),!1;if(!i.value)return console.log("\u274C Instance no longer collectible"),!1;let a=this.safeInvoke(o.instance,"StartCollect");return a.error?(console.log(`\u274C StartCollect failed: ${a.error}`),!1):(console.log("\u2705 StartCollect executed successfully!"),setTimeout(()=>{this.safeInvoke(o.instance,"DoJobBuyThrough").error?console.log("\u26A0\uFE0F DoJobBuyThrough not available (this is normal if collection is already complete)"):console.log("\u{1F48E} DoJobBuyThrough executed successfully - instant completion!"),console.log(`\u{1F389} Collection process completed for ${o.rewardAmount} GEMS`)},500),!0)}async validateBatch(o,i,a){let u=Math.min(i+a,o.length),m=[];console.log(`\u{1F50D} Validating batch instances ${i+1}-${u}...`);for(let f=i;f<u;f++)try{let p=o[f].field("m_goodyHut");if(p&&p.value&&p.value!==null&&p.value.toString()!=="0x0"){let b=p.value,T=this.validateInstance(b,f);T.isValid&&T.canCollect&&T.rewardType==="GEMS"&&m.push(T)}}catch{}return m}async collectBatch(o,i){if(o.length===0)return console.log(`\u{1F4E6} Batch ${i}: No GEMS instances to collect`),{collectionsStarted:0,instantCompletions:0};console.log(`\u{1F680} Batch ${i}: Starting collection of ${o.length} GEMS instances...`);let a=0,u=0,m=o.map(async(b,T)=>{try{console.log(`  \u{1F48E} [${T+1}/${o.length}] Collecting ${b.rewardAmount} GEMS from EntityController ${b.entityIndex}`);let w=this.safeInvoke(b.instance,"StartCollect");if(w.error)return console.log(`    \u274C StartCollect failed: ${w.error}`),{started:!1,instantCompleted:!1};console.log("    \u2705 Collection started successfully"),a++;let x=!1;return e?(console.log("    \u26A1 Collection completing instantly via GetCollectTime hook"),x=!0,u++):(await new Promise(E=>setTimeout(E,100)),this.shouldAttemptInstantCompletion(b)?(x=this.executeInstantCompletion(b),x&&u++):console.log("    \u23F3 Instant completion skipped (will complete naturally)")),console.log(`    \u{1F389} Collection initiated for ${b.rewardAmount} GEMS`),{started:!0,instantCompleted:x}}catch(w){return console.log(`    \u274C Collection error: ${w}`),{started:!1,instantCompleted:!1}}}),f=await Promise.all(m),y=f.filter(b=>b.started).length,p=f.filter(b=>b.instantCompleted).length;return console.log(`\u{1F4E6} Batch ${i} completed: ${y}/${o.length} collections started, ${p}/${o.length} instant completions`),{collectionsStarted:y,instantCompletions:p}}async processBatchCollection(){console.log("\u{1F504} Starting batch collection process..."),console.log(e?"\u26A1 GetCollectTime hook is active - collections will complete instantly":"\u26A0\uFE0F GetCollectTime hook not available - falling back to DoJobBuyThrough method"),this.buyThroughFailureCount=0,this.buyThroughSuccessCount=0,this.attemptInstantCompletion=!0;try{let o=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),i=Il2Cpp.gc.choose(o);console.log(`\u{1F4CA} Found ${i.length} EntityController instances`);let a=Math.ceil(i.length/this.batchSize);console.log(`\u{1F4E6} Processing in ${a} batches of ${this.batchSize} instances each
`);let u=0,m=0;for(let f=0;f<a;f++){let y=f*this.batchSize,p=f+1;console.log(`
=== BATCH ${p}/${a} ===`);let b=await this.validateBatch(i,y,this.batchSize);if(console.log(`\u2705 Found ${b.length} valid GEMS instances in batch ${p}`),b.length>0){let T=await this.collectBatch(b,p);u+=T.collectionsStarted,m+=T.instantCompletions}this.buyThroughFailureCount>0&&console.log(`\u26A0\uFE0F Instant completion status: ${this.buyThroughSuccessCount} successes, ${this.buyThroughFailureCount} failures`),f<a-1&&(console.log(`\u23F3 Waiting ${this.batchDelay/1e3}s before next batch...`),await new Promise(T=>setTimeout(T,this.batchDelay)))}if(console.log(`
\u{1F389} Batch collection completed!`),console.log(`\u{1F4CA} Collections started: ${u}`),console.log(`\u{1F48E} Instant completions: ${m}`),e?console.log(`\u26A1 Completion method: GetCollectTime hook (${t} hook calls)`):m>0?console.log("\u{1F48E} Completion method: DoJobBuyThrough"):u>0&&console.log("\u23F3 Completion method: Natural completion over time"),m===0&&u>0&&!e&&(console.log("\u2139\uFE0F Note: Collections were started but instant completion was not available"),console.log("\u2139\uFE0F Collections will complete naturally over time")),!e&&(this.buyThroughSuccessCount>0||this.buyThroughFailureCount>0)){let f=Math.round(this.buyThroughSuccessCount/(this.buyThroughSuccessCount+this.buyThroughFailureCount)*100);console.log(`\u{1F4C8} DoJobBuyThrough success rate: ${f}% (${this.buyThroughSuccessCount}/${this.buyThroughSuccessCount+this.buyThroughFailureCount})`)}}catch(o){console.log(`\u274C Batch collection error: ${o}`)}}setInstantCompletionEnabled(o){this.attemptInstantCompletion=o,console.log(`\u{1F48E} Instant completion ${o?"enabled":"disabled"}`)}getInstantCompletionStats(){return{enabled:this.attemptInstantCompletion,successes:this.buyThroughSuccessCount,failures:this.buyThroughFailureCount}}getHookStatus(){return{installed:e,callCount:t}}reinstallHook(){console.log("\u{1F504} Attempting to reinstall GetCollectTime hook..."),l()}}let s=new n;globalThis.goodyManager={scan:()=>s.scanAndValidateInstances(),showValid:()=>s.showValidInstances(),getBest:()=>s.getBestCollectibleInstance(),startCollection:()=>{let r=s.getBestCollectibleInstance();return r?s.executeStartCollect(r):(console.log("\u274C No collectible instances available"),!1)},batchCollection:()=>s.processBatchCollection(),enableInstantCompletion:()=>s.setInstantCompletionEnabled(!0),disableInstantCompletion:()=>s.setInstantCompletionEnabled(!1),getStats:()=>s.getInstantCompletionStats(),getHookStatus:()=>s.getHookStatus(),reinstallHook:()=>s.reinstallHook(),help:()=>{console.log("=== GoodyHut Instance Manager Commands ==="),console.log("goodyManager.scan() - Scan and validate all instances"),console.log("goodyManager.showValid() - Show details of valid instances"),console.log("goodyManager.getBest() - Get best collectible instance (GEMS only)"),console.log("goodyManager.startCollection() - Start collection on best GEMS instance"),console.log("goodyManager.batchCollection() - Process all GEMS instances in batches of 10"),console.log(""),console.log("=== Instant Completion Controls ==="),console.log("goodyManager.enableInstantCompletion() - Enable DoJobBuyThrough attempts"),console.log("goodyManager.disableInstantCompletion() - Disable DoJobBuyThrough attempts"),console.log("goodyManager.getStats() - Show instant completion statistics"),console.log(""),console.log("=== Hook Management ==="),console.log("goodyManager.getHookStatus() - Check GetCollectTime hook status"),console.log("goodyManager.reinstallHook() - Reinstall GetCollectTime hook if needed"),console.log(""),console.log("\u{1F48E} NOTE: Only instances with GEMS rewards will be collected!"),console.log("\u{1F4E6} BATCH MODE: Collections will start even if instant completion fails"),console.log("\u26A1 HOOK MODE: GetCollectTime hook provides instant completion without premium currency"),console.log("\u{1F504} FALLBACK: System falls back to DoJobBuyThrough if hook is not available")}},console.log("\u{1F527} Robust GoodyHutHelper Instance Manager loaded!"),console.log(`\u26A1 GetCollectTime hook status: ${e?"ACTIVE":"FAILED"}`),console.log("Use goodyManager.help() for available commands"),console.log("Start with: goodyManager.scan() or goodyManager.batchCollection()")});
