# GoodyHut Auto-Complete Script Usage Guide

## Quick Start

### 1. Build the Scripts
```bash
# Build basic version
npm run build

# Build enhanced version (recommended)
npm run build-enhanced
```

### 2. Run the Script

**Option A: Spawn new game instance (recommended)**
```bash
# Basic version
npm run spawn

# Enhanced version
npm run spawn-enhanced
```

**Option B: Attach to running game**
```bash
# Basic version
npm run attach

# Enhanced version
npm run attach-enhanced
```

## Script Versions

### Basic Version (`src/index.ts`)
- Simple auto-completion functionality
- Hooks `StartCollect()` method
- Periodic scanning backup
- Basic error handling

### Enhanced Version (`src/enhanced-goody-hut.ts`) - **Recommended**
- Advanced error handling and retry logic
- Detailed logging and statistics
- Runtime configuration controls
- Instance information display
- Performance monitoring

## Expected Console Output

### Enhanced Version Output:
```
[12:34:56] SUCCESS Enhanced GoodyHutHelper Auto-Complete Script Loaded
[12:34:56] INFO GoodyHutHelper class found: 0x7a1b2c3d4e
[12:34:56] INFO Hooking StartCollect method...
[12:34:56] SUCCESS StartCollect hook installed successfully!
[12:34:56] INFO Starting periodic scan every 2000ms...
[12:34:57] INFO Performing initial scan...
[12:34:57] INFO Scanning 3 GoodyHutHelper instances...

# When you start a collection:
[12:35:15] INFO StartCollect called - triggering original method first
[12:35:15] INFO Starting collection on instance: Health: 100/100, Explorations: 2, Reward: 1500 Gold
[12:35:15] INFO Attempting auto-completion after StartCollect...
[12:35:15] INFO Attempting auto-completion of goody hut collection...
[12:35:15] SUCCESS Successfully auto-completed goody hut collection!
[12:35:15] SUCCESS Auto-completed collection immediately after start!

# Statistics every 30 seconds:
[12:35:45] STATS Runtime: 45s | Attempts: 3 | Success: 3 | Failed: 0 | Rate: 100%
```

## Runtime Controls (Enhanced Version Only)

While the script is running, you can use these console commands:

```javascript
// Enable/disable auto-completion
goodyHutConfig.enable()
goodyHutConfig.disable()

// Toggle verbose logging
goodyHutConfig.verbose(true)   // Enable detailed logs
goodyHutConfig.verbose(false)  // Disable detailed logs

// Manual operations
goodyHutConfig.scan()   // Trigger immediate scan
goodyHutConfig.stats()  // Show current statistics
```

## How It Works

### 1. Method Hooking
- Intercepts calls to `GoodyHutHelper.StartCollect()`
- Allows original collection to start
- Immediately calls `DoJobBuyThrough()` to complete instantly

### 2. Fallback Scanning
- Scans all `GoodyHutHelper` instances every 2 seconds
- Identifies collecting instances using `IsJobComplete()` and `CanCollect()`
- Attempts auto-completion on found instances

### 3. State Detection
The script identifies collecting goody huts by checking:
- `IsJobComplete()` returns `false` (collection not finished)
- `CanCollect()` returns `false` (can't start new collection)
- This combination indicates collection in progress

## Troubleshooting

### Common Issues and Solutions

**1. "GoodyHutHelper class not found"**
```
Solution: Wait for game to fully load, then restart script
```

**2. "Cannot use buy-through on this instance"**
```
Cause: Goody hut doesn't support instant completion
Solution: This is normal - some huts may not allow buy-through
```

**3. "DoJobBuyThrough call failed"**
```
Possible causes:
- Insufficient premium currency
- Goody hut already completed
- Game state changed
Solution: Script will retry automatically (enhanced version)
```

**4. No collections detected**
```
Solution: 
1. Start a goody hut collection manually
2. Check console for "StartCollect called" message
3. Use goodyHutConfig.scan() to trigger manual scan
```

### Debug Mode

For troubleshooting, enable verbose logging:
```javascript
goodyHutConfig.verbose(true)
```

This will show detailed information about:
- Method calls and results
- Instance states and properties
- Scan results and decisions

## Performance Notes

- **CPU Impact**: Minimal - only scans every 2 seconds
- **Memory Impact**: Low - tracks small number of instances
- **Network Impact**: None - only calls local game methods
- **Battery Impact**: Negligible on modern devices

## Safety Features

- **Error Recovery**: Continues operation if individual completions fail
- **Retry Logic**: Enhanced version retries failed completions up to 3 times
- **State Validation**: Verifies collection state before attempting completion
- **Method Validation**: Checks method availability before calling

## Game Compatibility

- **Tested On**: Dominations mobile game
- **IL2CPP Version**: Compatible with frida-il2cpp-bridge
- **Platform**: Android (primary), iOS (should work)
- **Game Updates**: May require script updates if method signatures change

## Legal Notice

This script is for educational and research purposes. Use responsibly and in accordance with the game's terms of service. The script works by calling existing game methods and does not modify game data or bypass security measures.
