📦
75105 /src/enhanced-goody-hut.js
✄
var u=function(e,t,a,n){var s=arguments.length,r=s<3?t:n===null?n=Object.getOwnPropertyDescriptor(t,a):n,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(e,t,a,n);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(r=(s<3?o(r):s>3?o(t,a,r):o(t,a))||r);return s>3&&r&&Object.defineProperty(t,a,r),r},d;(function(e){e.application={get dataPath(){return t("get_persistentDataPath")},get identifier(){return t("get_identifier")??t("get_bundleIdentifier")??Process.mainModule.name},get version(){return t("get_version")??K(e.module).toString(16)}},A(e,"unityVersion",()=>{try{let n=e.$config.unityVersion??t("get_unityVersion");if(n!=null)return n}catch{}let a="69 6c 32 63 70 70";for(let n of e.module.enumerateRanges("r--").concat(Process.getRangeByAddress(e.module.base)))for(let{address:s}of Memory.scanSync(n.base,n.size,a)){for(;s.readU8()!=0;)s=s.sub(1);let r=M.find(s.add(1).readCString());if(r!=null)return r}y("couldn't determine the Unity version, please specify it manually")},l),A(e,"unityVersionIsBelow201830",()=>M.lt(e.unityVersion,"2018.3.0"),l),A(e,"unityVersionIsBelow202120",()=>M.lt(e.unityVersion,"2021.2.0"),l);function t(a){let n=e.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+a)),s=new NativeFunction(n,"pointer",[]);return s.isNull()?null:new e.String(s()).asNullable()?.content??null}})(d||(d={}));var d;(function(e){function t(a,n){let s={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},r=typeof a=="boolean"?"System.Boolean":typeof a=="number"?s[n??"int32"]:a instanceof Int64?"System.Int64":a instanceof UInt64?"System.UInt64":a instanceof NativePointer?s[n??"intptr"]:y(`Cannot create boxed primitive using value of type '${typeof a}'`),o=e.corlib.class(r??y(`Unknown primitive type name '${n}'`)).alloc();return(o.tryField("m_value")??o.tryField("_pointer")??y(`Could not find primitive field in class '${r}'`)).value=a,o}e.boxed=t})(d||(d={}));var d;(function(e){e.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(d||(d={}));var d;(function(e){function t(o,i){o=o??`${e.application.identifier}_${e.application.version}.cs`,i=i??e.application.dataPath??Process.getCurrentDir(),s(i);let c=`${i}/${o}`,h=new File(c,"w");for(let g of e.domain.assemblies){P(`dumping ${g.name}...`);for(let p of g.image.classes)h.write(`${p}

`)}h.flush(),h.close(),z(`dump saved to ${c}`),r()}e.dump=t;function a(o,i=!1){o=o??`${e.application.dataPath??Process.getCurrentDir()}/${e.application.identifier}_${e.application.version}`,!i&&n(o)&&y(`directory ${o} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let c of e.domain.assemblies){P(`dumping ${c.name}...`);let h=`${o}/${c.name.replaceAll(".","/")}.cs`;s(h.substring(0,h.lastIndexOf("/")));let g=new File(h,"w");for(let p of c.image.classes)g.write(`${p}

`);g.flush(),g.close()}z(`dump saved to ${o}`),r()}e.dumpTree=a;function n(o){return e.corlib.class("System.IO.Directory").method("Exists").invoke(e.string(o))}function s(o){e.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(e.string(o))}function r(){j("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(d||(d={}));var d;(function(e){function t(a="current"){let n=e.exports.threadGetCurrent();return Interceptor.attach(e.module.getExportByName("__cxa_throw"),function(s){a=="current"&&!e.exports.threadGetCurrent().equals(n)||P(new e.Object(s[0].readPointer()))})}e.installExceptionListener=t})(d||(d={}));var d;(function(e){e.exports={get alloc(){return t("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return t("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return t("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return t("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return t("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return t("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return t("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return t("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return t("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return t("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return t("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return t("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return t("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return t("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return t("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return t("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return t("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return t("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return t("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return t("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return t("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return t("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return t("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return t("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return t("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return t("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return t("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return t("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return t("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return t("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return t("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return t("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return t("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return t("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return t("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return t("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return t("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return t("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return t("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return t("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return t("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return t("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return t("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return t("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return t("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return t("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return t("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return t("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return t("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return t("il2cpp_free","void",["pointer"])},get gcCollect(){return t("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return t("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return t("il2cpp_gc_disable","void",[])},get gcEnable(){return t("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return t("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return t("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return t("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return t("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return t("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return t("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return t("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return t("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return t("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return t("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return t("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return t("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return t("il2cpp_stop_gc_world","void",[])},get getCorlib(){return t("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return t("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return t("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return t("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return t("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return t("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return t("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return t("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return t("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return t("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return t("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return t("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return t("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return t("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return t("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return t("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return t("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return t("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return t("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return t("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return t("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return t("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return t("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return t("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return t("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return t("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return t("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return t("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return t("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return t("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return t("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return t("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return t("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return t("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return t("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return t("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return t("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return t("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return t("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return t("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return t("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return t("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return t("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return t("il2cpp_string_length","int32",["pointer"])},get stringNew(){return t("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return t("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return t("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return t("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return t("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return t("il2cpp_thread_current","pointer",[])},get threadIsVm(){return t("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return t("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return t("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return t("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return t("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return t("il2cpp_type_get_type","int",["pointer"])}},X(e.exports,l),A(e,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),l);function t(a,n,s){let r=e.$config.exports?.[a]?.()??e.module.findExportByName(a)??e.memorySnapshotExports[a],o=new NativeFunction(r??NULL,n,s);return o.isNull()?new Proxy(o,{get(i,c){let h=i[c];return typeof h=="function"?h.bind(i):h},apply(){r==null?y(`couldn't resolve export ${a}`):r.isNull()&&y(`export ${a} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):o}})(d||(d={}));var d;(function(e){function t(n){return s=>s instanceof e.Class?n.isAssignableFrom(s):n.isAssignableFrom(s.class)}e.is=t;function a(n){return s=>s instanceof e.Class?s.equals(n):s.class.equals(n)}e.isExactly=a})(d||(d={}));var d;(function(e){e.gc={get heapSize(){return e.exports.gcGetHeapSize()},get isEnabled(){return!e.exports.gcIsDisabled()},get isIncremental(){return!!e.exports.gcIsIncremental()},get maxTimeSlice(){return e.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return e.exports.gcGetUsedSize()},set isEnabled(t){t?e.exports.gcEnable():e.exports.gcDisable()},set maxTimeSlice(t){e.exports.gcSetMaxTimeSlice(t)},choose(t){let a=[],n=(r,o)=>{for(let i=0;i<o;i++)a.push(new e.Object(r.add(i*Process.pointerSize).readPointer()))},s=new NativeCallback(n,"void",["pointer","int","pointer"]);if(e.unityVersionIsBelow202120){let r=new NativeCallback(()=>{},"void",[]),o=e.exports.livenessCalculationBegin(t,0,s,NULL,r,r);e.exports.livenessCalculationFromStatics(o),e.exports.livenessCalculationEnd(o)}else{let r=(c,h)=>!c.isNull()&&h.compare(0)==0?(e.free(c),NULL):e.alloc(h),o=new NativeCallback(r,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let i=e.exports.livenessAllocateStruct(t,0,s,NULL,o);e.exports.livenessCalculationFromStatics(i),e.exports.livenessFinalize(i),this.startWorld(),e.exports.livenessFreeStruct(i)}return a},collect(t){e.exports.gcCollect(t<0?0:t>2?2:t)},collectALittle(){e.exports.gcCollectALittle()},startWorld(){return e.exports.gcStartWorld()},startIncrementalCollection(){return e.exports.gcStartIncrementalCollection()},stopWorld(){return e.exports.gcStopWorld()}}})(d||(d={}));var V;(function(e){A(e,"apiLevel",()=>{let a=t("ro.build.version.sdk");return a?parseInt(a):null},l);function t(a){let n=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(n){let s=new NativeFunction(n,"void",["pointer","pointer"]),r=Memory.alloc(92).writePointer(NULL);return s(Memory.allocUtf8String(a),r),r.readCString()??void 0}}})(V||(V={}));function y(e){let t=new Error(e);throw t.name="Il2CppError",t.stack=t.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),t}function j(e){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${e}`)}function z(e){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${e}`)}function P(e){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${e}`)}function X(e,t,a=Object.getOwnPropertyDescriptors(e)){for(let n in a)a[n]=t(e,n,a[n]);return Object.defineProperties(e,a),e}function A(e,t,a,n){globalThis.Object.defineProperty(e,t,n?.(e,t,{get:a,configurable:!0})??{get:a,configurable:!0})}function H(e){let t=3735928559,a=1103547991;for(let n=0,s;n<e.length;n++)s=e.charCodeAt(n),t=Math.imul(t^s,2654435761),a=Math.imul(a^s,1597334677);return t=Math.imul(t^t>>>16,2246822507),t^=Math.imul(a^a>>>13,3266489909),a=Math.imul(a^a>>>16,2246822507),a^=Math.imul(t^t>>>13,3266489909),4294967296*(2097151&a)+(t>>>0)}function K(e){return H(e.enumerateExports().sort((t,a)=>t.name.localeCompare(a.name)).map(t=>t.name+t.address.sub(e.base)).join(""))}function l(e,t,a){let n=a.get;if(!n)throw new Error("@lazy can only be applied to getter accessors");return a.get=function(){let s=n.call(this);return Object.defineProperty(this,t,{value:s,configurable:a.configurable,enumerable:a.enumerable,writable:!1}),s},a}var x=class{handle;constructor(t){t instanceof NativePointer?this.handle=t:this.handle=t.handle}equals(t){return this.handle.equals(t.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function Q(e){return Object.keys(e).reduce((t,a)=>(t[t[a]]=a,t),e)}NativePointer.prototype.offsetOf=function(e,t){t??=512;for(let a=0;t>0?a<t:a<-t;a++)if(e(t>0?this.add(a):this.sub(a)))return a;return null};function F(e){let t=[],a=Memory.alloc(Process.pointerSize),n=e(a);for(;!n.isNull();)t.push(n),n=e(a);return t}function D(e){let t=Memory.alloc(Process.pointerSize),a=e(t);if(a.isNull())return[];let n=new Array(t.readInt());for(let s=0;s<n.length;s++)n[s]=a.add(s*Process.pointerSize).readPointer();return n}function L(e){return new Proxy(e,{cache:new Map,construct(t,a){let n=a[0].toUInt32();return this.cache.has(n)||this.cache.set(n,new t(a[0])),this.cache.get(n)}})}var M;(function(e){let t=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function a(o){return o?.match(t)?.[0]}e.find=a;function n(o,i){return r(o,i)>=0}e.gte=n;function s(o,i){return r(o,i)<0}e.lt=s;function r(o,i){let c=o.match(t),h=i.match(t);for(let g=1;g<=3;g++){let p=Number(c?.[g]??-1),b=Number(h?.[g]??-1);if(p>b)return 1;if(p<b)return-1}return 0}})(M||(M={}));var d;(function(e){function t(i=Process.pointerSize){return e.exports.alloc(i)}e.alloc=t;function a(i){return e.exports.free(i)}e.free=a;function n(i,c){switch(c.enumValue){case e.Type.Enum.BOOLEAN:return!!i.readS8();case e.Type.Enum.BYTE:return i.readS8();case e.Type.Enum.UBYTE:return i.readU8();case e.Type.Enum.SHORT:return i.readS16();case e.Type.Enum.USHORT:return i.readU16();case e.Type.Enum.INT:return i.readS32();case e.Type.Enum.UINT:return i.readU32();case e.Type.Enum.CHAR:return i.readU16();case e.Type.Enum.LONG:return i.readS64();case e.Type.Enum.ULONG:return i.readU64();case e.Type.Enum.FLOAT:return i.readFloat();case e.Type.Enum.DOUBLE:return i.readDouble();case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return i.readPointer();case e.Type.Enum.POINTER:return new e.Pointer(i.readPointer(),c.class.baseType);case e.Type.Enum.VALUE_TYPE:return new e.ValueType(i,c);case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:return new e.Object(i.readPointer());case e.Type.Enum.GENERIC_INSTANCE:return c.class.isValueType?new e.ValueType(i,c):new e.Object(i.readPointer());case e.Type.Enum.STRING:return new e.String(i.readPointer());case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i.readPointer())}y(`couldn't read the value from ${i} using an unhandled or unknown type ${c.name} (${c.enumValue}), please file an issue`)}e.read=n;function s(i,c,h){switch(h.enumValue){case e.Type.Enum.BOOLEAN:return i.writeS8(+c);case e.Type.Enum.BYTE:return i.writeS8(c);case e.Type.Enum.UBYTE:return i.writeU8(c);case e.Type.Enum.SHORT:return i.writeS16(c);case e.Type.Enum.USHORT:return i.writeU16(c);case e.Type.Enum.INT:return i.writeS32(c);case e.Type.Enum.UINT:return i.writeU32(c);case e.Type.Enum.CHAR:return i.writeU16(c);case e.Type.Enum.LONG:return i.writeS64(c);case e.Type.Enum.ULONG:return i.writeU64(c);case e.Type.Enum.FLOAT:return i.writeFloat(c);case e.Type.Enum.DOUBLE:return i.writeDouble(c);case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return i.writePointer(c);case e.Type.Enum.VALUE_TYPE:return Memory.copy(i,c,h.class.valueTypeSize),i;case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:return c instanceof e.ValueType?(Memory.copy(i,c,h.class.valueTypeSize),i):i.writePointer(c)}y(`couldn't write value ${c} to ${i} using an unhandled or unknown type ${h.name} (${h.enumValue}), please file an issue`)}e.write=s;function r(i,c){if(globalThis.Array.isArray(i)){let h=Memory.alloc(c.class.valueTypeSize),g=c.class.fields.filter(p=>!p.isStatic);for(let p=0;p<g.length;p++){let b=r(i[p],g[p].type);s(h.add(g[p].offset).sub(e.Object.headerSize),b,g[p].type)}return new e.ValueType(h,c)}else if(i instanceof NativePointer){if(c.isByReference)return new e.Reference(i,c);switch(c.enumValue){case e.Type.Enum.POINTER:return new e.Pointer(i,c.class.baseType);case e.Type.Enum.STRING:return new e.String(i);case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:case e.Type.Enum.OBJECT:return new e.Object(i);case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(i);default:return i}}else return c.enumValue==e.Type.Enum.BOOLEAN?!!i:c.enumValue==e.Type.Enum.VALUE_TYPE&&c.class.isEnum?r([i],c):i}e.fromFridaValue=r;function o(i){if(typeof i=="boolean")return+i;if(i instanceof e.ValueType){if(i.type.class.isEnum)return i.field("value__").value;{let c=i.type.class.fields.filter(h=>!h.isStatic).map(h=>o(h.bind(i).value));return c.length==0?[0]:c}}else return i}e.toFridaValue=o})(d||(d={}));var d;(function(e){A(e,"module",()=>a()??y("Could not find IL2CPP module"));async function t(s=!1){let r=a()??await new Promise(o=>{let[i,c]=n(),h=setTimeout(()=>{j(`after 10 seconds, IL2CPP module '${i}' has not been loaded yet, is the app running?`)},1e4),g=Process.attachModuleObserver({onAdded(p){(p.name==i||c&&p.name==c)&&(clearTimeout(h),setImmediate(()=>{o(p),g.detach()}))}})});return Reflect.defineProperty(e,"module",{value:r}),e.exports.getCorlib().isNull()?await new Promise(o=>{let i=Interceptor.attach(e.exports.initialize,{onLeave(){i.detach(),s?o(!0):setImmediate(()=>o(!1))}})}):!1}e.initialize=t;function a(){let[s,r]=n();return Process.findModuleByName(s)??Process.findModuleByName(r??s)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function n(){if(e.$config.moduleName)return[e.$config.moduleName];switch(Process.platform){case"linux":return[V.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}y(`${Process.platform} is not supported yet`)}})(d||(d={}));var d;(function(e){async function t(a,n="bind"){let s=null;try{let r=await e.initialize(n=="main");if(n=="main"&&!r)return t(()=>e.mainThread.schedule(a),"free");e.currentThread==null&&(s=e.domain.attach()),n=="bind"&&s!=null&&Script.bindWeak(globalThis,()=>s?.detach());let o=a();return o instanceof Promise?await o:o}catch(r){return Script.nextTick(o=>{throw o},r),Promise.reject(r)}finally{n=="free"&&s!=null&&s.detach()}}e.perform=t})(d||(d={}));var d;(function(e){class t{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let r=`
${this.#e.buffer.join(`
`)}
`;if(this.#d)P(r);else{let o=H(r);this.#e.history.has(o)||(this.#e.history.add(o),P(r))}this.#e.buffer.length=0}}};#u=e.mainThread.id;#d=!1;#h;#c=[];#l;#t;#n;#r;#s;#i;#o;#a;constructor(r){this.#h=r}thread(r){return this.#u=r.id,this}verbose(r){return this.#d=r,this}domain(){return this.#l=e.domain,this}assemblies(...r){return this.#t=r,this}classes(...r){return this.#n=r,this}methods(...r){return this.#r=r,this}filterAssemblies(r){return this.#s=r,this}filterClasses(r){return this.#i=r,this}filterMethods(r){return this.#o=r,this}filterParameters(r){return this.#a=r,this}and(){let r=b=>{if(this.#a==null){this.#c.push(b);return}for(let E of b.parameters)if(this.#a(E)){this.#c.push(b);break}},o=b=>{for(let E of b)r(E)},i=b=>{if(this.#o==null){o(b.methods);return}for(let E of b.methods)this.#o(E)&&r(E)},c=b=>{for(let E of b)i(E)},h=b=>{if(this.#i==null){c(b.image.classes);return}for(let E of b.image.classes)this.#i(E)&&i(E)},g=b=>{for(let E of b)h(E)},p=b=>{if(this.#s==null){g(b.assemblies);return}for(let E of b.assemblies)this.#s(E)&&h(E)};return this.#r?o(this.#r):this.#n?c(this.#n):this.#t?g(this.#t):this.#l&&p(this.#l),this.#t=void 0,this.#n=void 0,this.#r=void 0,this.#s=void 0,this.#i=void 0,this.#o=void 0,this.#a=void 0,this}attach(){for(let r of this.#c)if(!r.virtualAddress.isNull())try{this.#h(r,this.#e,this.#u)}catch(o){switch(o.message){case/unable to intercept function at \w+; please file a bug/.exec(o.message)?.input:case"already replaced this function":break;default:throw o}}}}e.Tracer=t;function a(s=!1){let r=()=>(i,c,h)=>{let g=i.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(i.virtualAddress,{onEnter(){this.threadId==h&&c.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==h&&(c.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m`),c.flush())}})},o=()=>(i,c,h)=>{let g=i.relativeVirtualAddress.toString(16).padStart(8,"0"),p=+!i.isStatic|+e.unityVersionIsBelow201830,b=function(...$){if(this.threadId==h){let U=i.isStatic?void 0:new e.Parameter("this",-1,i.class.type),k=U?[U].concat(i.parameters):i.parameters;c.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m(${k.map(R=>`\x1B[32m${R.name}\x1B[0m = \x1B[31m${e.fromFridaValue($[R.position+p],R.type)}\x1B[0m`).join(", ")})`)}let v=i.nativeFunction(...$);return this.threadId==h&&(c.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${i.class.type.name}::\x1B[1m${i.name}\x1B[0m\x1B[0m${v==null?"":` = \x1B[36m${e.fromFridaValue(v,i.returnType)}`}\x1B[0m`),c.flush()),v};i.revert();let E=new NativeCallback(b,i.returnType.fridaAlias,i.fridaSignature);Interceptor.replace(i.virtualAddress,E)};return new e.Tracer(s?o():r())}e.trace=a;function n(s){let r=e.domain.assemblies.flatMap(c=>c.image.classes.flatMap(h=>h.methods.filter(g=>!g.virtualAddress.isNull()))).sort((c,h)=>c.virtualAddress.compare(h.virtualAddress)),o=c=>{let h=0,g=r.length-1;for(;h<=g;){let p=Math.floor((h+g)/2),b=r[p].virtualAddress.compare(c);if(b==0)return r[p];b>0?g=p-1:h=p+1}return r[g]},i=()=>(c,h,g)=>{Interceptor.attach(c.virtualAddress,function(){if(this.threadId==g){let p=globalThis.Thread.backtrace(this.context,s);p.unshift(c.virtualAddress);for(let b of p)if(b.compare(e.module.base)>0&&b.compare(e.module.base.add(e.module.size))<0){let E=o(b);if(E){let $=b.sub(E.virtualAddress);$.compare(4095)<0&&h.buffer.push(`\x1B[2m0x${E.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${$.toString(16).padStart(3,"0")}\x1B[0m ${E.class.type.name}::\x1B[1m${E.name}\x1B[0m`)}}h.flush()}})};return new e.Tracer(i())}e.backtrace=n})(d||(d={}));var d;(function(e){class t extends x{static get headerSize(){return e.corlib.class("System.Array").instanceSize}get elements(){let r=e.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(o=>o.readS16()==118)??y("couldn't find the elements offset in the native array struct");return A(e.Array.prototype,"elements",function(){return new e.Pointer(this.handle.add(r),this.elementType)},l),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return e.exports.arrayGetLength(this)}get object(){return new e.Object(this)}get(s){return(s<0||s>=this.length)&&y(`cannot get element at index ${s} as the array length is ${this.length}`),this.elements.get(s)}set(s,r){(s<0||s>=this.length)&&y(`cannot set element at index ${s} as the array length is ${this.length}`),this.elements.set(s,r)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let s=0;s<this.length;s++)yield this.elements.get(s)}}u([l],t.prototype,"elementSize",null),u([l],t.prototype,"elementType",null),u([l],t.prototype,"length",null),u([l],t.prototype,"object",null),u([l],t,"headerSize",null),e.Array=t;function a(n,s){let r=typeof s=="number"?s:s.length,o=new e.Array(e.exports.arrayNew(n,r));return globalThis.Array.isArray(s)&&o.elements.write(s),o}e.array=a})(d||(d={}));var d;(function(e){let t=class extends x{get image(){if(e.exports.assemblyGetImage.isNull()){let n=this.object.tryMethod("GetType",1)?.invoke(e.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??y(`couldn't find the runtime module object of assembly ${this.name}`);return new e.Image(n.field("_impl").value)}return new e.Image(e.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let n of e.domain.object.method("GetAssemblies",1).invoke(!1))if(n.field("_mono_assembly").value.equals(this))return n;y("couldn't find the object of the native assembly struct")}};u([l],t.prototype,"name",null),u([l],t.prototype,"object",null),t=u([L],t),e.Assembly=t})(d||(d={}));var d;(function(e){let t=class extends x{get actualInstanceSize(){let n=e.corlib.class("System.String"),s=n.handle.offsetOf(r=>r.readInt()==n.instanceSize-2)??y("couldn't find the actual instance size offset in the native class struct");return A(e.Class.prototype,"actualInstanceSize",function(){return this.handle.add(s).readS32()},l),this.actualInstanceSize}get arrayClass(){return new e.Class(e.exports.classGetArrayClass(this,1))}get arrayElementSize(){return e.exports.classGetArrayElementSize(this)}get assemblyName(){return e.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new e.Class(e.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new e.Type(e.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new e.Class(e.exports.classGetElementClass(this)).asNullable()}get fields(){return F(n=>e.exports.classGetFields(this,n)).map(n=>new e.Field(n))}get flags(){return e.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let n=this.image.tryClass(this.fullName)?.asNullable();return n?.equals(this)?null:n??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let n=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(n).map(s=>new e.Class(e.exports.classFromObject(s)))}get hasReferences(){return!!e.exports.classHasReferences(this)}get hasStaticConstructor(){let n=this.tryMethod(".cctor");return n!=null&&!n.virtualAddress.isNull()}get image(){return new e.Image(e.exports.classGetImage(this))}get instanceSize(){return e.exports.classGetInstanceSize(this)}get isAbstract(){return!!e.exports.classIsAbstract(this)}get isBlittable(){return!!e.exports.classIsBlittable(this)}get isEnum(){return!!e.exports.classIsEnum(this)}get isGeneric(){return!!e.exports.classIsGeneric(this)}get isInflated(){return!!e.exports.classIsInflated(this)}get isInterface(){return!!e.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!e.exports.classIsValueType(this)}get interfaces(){return F(n=>e.exports.classGetInterfaces(this,n)).map(n=>new e.Class(n))}get methods(){return F(n=>e.exports.classGetMethods(this,n)).map(n=>new e.Method(n))}get name(){return e.exports.classGetName(this).readUtf8String()}get namespace(){return e.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return F(n=>e.exports.classGetNestedClasses(this,n)).map(n=>new e.Class(n))}get parent(){return new e.Class(e.exports.classGetParent(this)).asNullable()}get pointerClass(){return new e.Class(e.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let n=0,s=this.name;for(let r=this.name.length-1;r>0;r--){let o=s[r];if(o=="]")n++;else{if(o=="["||n==0)break;if(o==",")n++;else break}}return n}get staticFieldsData(){return e.exports.classGetStaticFieldData(this)}get valueTypeSize(){return e.exports.classGetValueTypeSize(this,NULL)}get type(){return new e.Type(e.exports.classGetType(this))}alloc(){return new e.Object(e.exports.objectNew(this))}field(n){return this.tryField(n)??y(`couldn't find field ${n} in class ${this.type.name}`)}*hierarchy(n){let s=n?.includeCurrent??!0?this:this.parent;for(;s;)yield s,s=s.parent}inflate(...n){this.isGeneric||y(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=n.length&&y(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${n.length}`);let s=n.map(i=>i.type.object),r=e.array(e.corlib.class("System.Type"),s),o=this.type.object.method("MakeGenericType",1).invoke(r);return new e.Class(e.exports.classFromObject(o))}initialize(){return e.exports.classInitialize(this),this}isAssignableFrom(n){return!!e.exports.classIsAssignableFrom(this,n)}isSubclassOf(n,s){return!!e.exports.classIsSubclassOf(this,n,+s)}method(n,s=-1){return this.tryMethod(n,s)??y(`couldn't find method ${n} in class ${this.type.name}`)}nested(n){return this.tryNested(n)??y(`couldn't find nested class ${n} in class ${this.type.name}`)}new(){let n=this.alloc(),s=Memory.alloc(Process.pointerSize);e.exports.objectInitialize(n,s);let r=s.readPointer();return r.isNull()||y(new e.Object(r).toString()),n}tryField(n){return new e.Field(e.exports.classGetFieldFromName(this,Memory.allocUtf8String(n))).asNullable()}tryMethod(n,s=-1){return new e.Method(e.exports.classGetMethodFromName(this,Memory.allocUtf8String(n),s)).asNullable()}tryNested(n){return this.nestedClasses.find(s=>s.name==n)}toString(){let n=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${n?` : ${n.map(s=>s?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(n){let s=new NativeCallback(r=>n(new e.Class(r)),"void",["pointer","pointer"]);return e.exports.classForEach(s,NULL)}};u([l],t.prototype,"arrayClass",null),u([l],t.prototype,"arrayElementSize",null),u([l],t.prototype,"assemblyName",null),u([l],t.prototype,"declaringClass",null),u([l],t.prototype,"baseType",null),u([l],t.prototype,"elementClass",null),u([l],t.prototype,"fields",null),u([l],t.prototype,"flags",null),u([l],t.prototype,"fullName",null),u([l],t.prototype,"generics",null),u([l],t.prototype,"hasReferences",null),u([l],t.prototype,"hasStaticConstructor",null),u([l],t.prototype,"image",null),u([l],t.prototype,"instanceSize",null),u([l],t.prototype,"isAbstract",null),u([l],t.prototype,"isBlittable",null),u([l],t.prototype,"isEnum",null),u([l],t.prototype,"isGeneric",null),u([l],t.prototype,"isInflated",null),u([l],t.prototype,"isInterface",null),u([l],t.prototype,"isValueType",null),u([l],t.prototype,"interfaces",null),u([l],t.prototype,"methods",null),u([l],t.prototype,"name",null),u([l],t.prototype,"namespace",null),u([l],t.prototype,"nestedClasses",null),u([l],t.prototype,"parent",null),u([l],t.prototype,"pointerClass",null),u([l],t.prototype,"rank",null),u([l],t.prototype,"staticFieldsData",null),u([l],t.prototype,"valueTypeSize",null),u([l],t.prototype,"type",null),t=u([L],t),e.Class=t})(d||(d={}));var d;(function(e){function t(a,n){let s=e.corlib.class("System.Delegate"),r=e.corlib.class("System.MulticastDelegate");s.isAssignableFrom(a)||y(`cannot create a delegate for ${a.type.name} as it's a non-delegate class`),(a.equals(s)||a.equals(r))&&y(`cannot create a delegate for neither ${s.type.name} nor ${r.type.name}, use a subclass instead`);let o=a.alloc(),i=o.handle.toString(),c=o.tryMethod("Invoke")??y(`cannot create a delegate for ${a.type.name}, there is no Invoke method`);o.method(".ctor").invoke(o,c.handle);let h=c.wrap(n);return o.field("method_ptr").value=h,o.field("invoke_impl").value=h,e._callbacksToKeepAlive[i]=h,o}e.delegate=t,e._callbacksToKeepAlive={}})(d||(d={}));var d;(function(e){let t=class extends x{get assemblies(){let n=D(s=>e.exports.domainGetAssemblies(this,s));if(n.length==0){let s=this.object.method("GetAssemblies").overload().invoke();n=globalThis.Array.from(s).map(r=>r.field("_mono_assembly").value)}return n.map(s=>new e.Assembly(s))}get object(){return e.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(n){return this.tryAssembly(n)??y(`couldn't find assembly ${n}`)}attach(){return new e.Thread(e.exports.threadAttach(this))}tryAssembly(n){return new e.Assembly(e.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(n))).asNullable()}};u([l],t.prototype,"assemblies",null),u([l],t.prototype,"object",null),t=u([L],t),e.Domain=t,A(e,"domain",()=>new e.Domain(e.exports.domainGet()),l)})(d||(d={}));var d;(function(e){class t extends x{get class(){return new e.Class(e.exports.fieldGetClass(this))}get flags(){return e.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let n=e.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return A(e.Field.prototype,"isThreadStatic",function(){return this.offset==n},l),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.fieldGetName(this).readUtf8String()}get offset(){return e.exports.fieldGetOffset(this)}get type(){return new e.Type(e.exports.fieldGetType(this))}get value(){this.isStatic||y(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let n=Memory.alloc(Process.pointerSize);return e.exports.fieldGetStaticValue(this.handle,n),e.read(n,this.type)}set value(n){this.isStatic||y(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&y(`cannot write the value of field ${this.name} as it's thread static or literal`);let s=n instanceof e.Object&&this.type.class.isValueType?n.unbox():n instanceof x?n.handle:n instanceof NativePointer?n:e.write(Memory.alloc(this.type.class.valueTypeSize),n,this.type);e.exports.fieldSetStaticValue(this.handle,s)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?e.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(n){this.isStatic&&y(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let s=this.offset-(n instanceof e.ValueType?e.Object.headerSize:0);return new Proxy(this,{get(r,o){return o=="value"?e.read(n.handle.add(s),r.type):Reflect.get(r,o)},set(r,o,i){return o=="value"?(e.write(n.handle.add(s),i,r.type),!0):Reflect.set(r,o,i)}})}}u([l],t.prototype,"class",null),u([l],t.prototype,"flags",null),u([l],t.prototype,"isLiteral",null),u([l],t.prototype,"isStatic",null),u([l],t.prototype,"isThreadStatic",null),u([l],t.prototype,"modifier",null),u([l],t.prototype,"name",null),u([l],t.prototype,"offset",null),u([l],t.prototype,"type",null),e.Field=t})(d||(d={}));var d;(function(e){class t{handle;constructor(n){this.handle=n}get target(){return new e.Object(e.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return e.exports.gcHandleFree(this.handle)}}e.GCHandle=t})(d||(d={}));var d;(function(e){let t=class extends x{get assembly(){return new e.Assembly(e.exports.imageGetAssembly(this))}get classCount(){return e.unityVersionIsBelow201830?this.classes.length:e.exports.imageGetClassCount(this)}get classes(){if(e.unityVersionIsBelow201830){let n=this.assembly.object.method("GetTypes").invoke(!1),s=globalThis.Array.from(n,o=>new e.Class(e.exports.classFromObject(o))),r=this.tryClass("<Module>");return r&&s.unshift(r),s}else return globalThis.Array.from(globalThis.Array(this.classCount),(n,s)=>new e.Class(e.exports.imageGetClass(this,s)))}get name(){return e.exports.imageGetName(this).readUtf8String()}class(n){return this.tryClass(n)??y(`couldn't find class ${n} in assembly ${this.name}`)}tryClass(n){let s=n.lastIndexOf("."),r=Memory.allocUtf8String(s==-1?"":n.slice(0,s)),o=Memory.allocUtf8String(n.slice(s+1));return new e.Class(e.exports.classFromName(this,r,o)).asNullable()}};u([l],t.prototype,"assembly",null),u([l],t.prototype,"classCount",null),u([l],t.prototype,"classes",null),u([l],t.prototype,"name",null),t=u([L],t),e.Image=t,A(e,"corlib",()=>new e.Image(e.exports.getCorlib()),l)})(d||(d={}));var d;(function(e){class t extends x{static capture(){return new e.MemorySnapshot}constructor(s=e.exports.memorySnapshotCapture()){super(s)}get classes(){return F(s=>e.exports.memorySnapshotGetClasses(this,s)).map(s=>new e.Class(s))}get objects(){return D(s=>e.exports.memorySnapshotGetObjects(this,s)).filter(s=>!s.isNull()).map(s=>new e.Object(s))}free(){e.exports.memorySnapshotFree(this)}}u([l],t.prototype,"classes",null),u([l],t.prototype,"objects",null),e.MemorySnapshot=t;function a(n){let s=e.MemorySnapshot.capture(),r=n(s);return s.free(),r}e.memorySnapshot=a})(d||(d={}));var d;(function(e){class t extends x{get class(){return new e.Class(e.exports.methodGetClass(this))}get flags(){return e.exports.methodGetFlags(this,NULL)}get implementationFlags(){let s=Memory.alloc(Process.pointerSize);return e.exports.methodGetFlags(this,s),s.readU32()}get fridaSignature(){let s=[];for(let r of this.parameters)s.push(r.type.fridaAlias);return(!this.isStatic||e.unityVersionIsBelow201830)&&s.unshift("pointer"),this.isInflated&&s.push("pointer"),s}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let s=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(s).map(r=>new e.Class(e.exports.classFromObject(r)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!e.exports.methodIsGeneric(this)}get isInflated(){return!!e.exports.methodIsInflated(this)}get isStatic(){return!e.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new e.Object(e.exports.methodGetObject(this,NULL))}get parameterCount(){return e.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(s,r)=>{let o=e.exports.methodGetParameterName(this,r).readUtf8String(),i=e.exports.methodGetParameterType(this,r);return new e.Parameter(o,r,new e.Type(i))})}get relativeVirtualAddress(){return this.virtualAddress.sub(e.module.base)}get returnType(){return new e.Type(e.exports.methodGetReturnType(this))}get virtualAddress(){let s=e.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,r=s.field("method_ptr").value,i=s.field("method").value.offsetOf(c=>c.readPointer().equals(r))??y("couldn't find the virtual address offset in the native method struct");return A(e.Method.prototype,"virtualAddress",function(){return this.handle.add(i).readPointer()},l),e.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(s){try{Interceptor.replace(this.virtualAddress,this.wrap(s))}catch(r){switch(r.message){case"access violation accessing 0x0":y(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(r.message)?.input:j(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":j(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw r}}}inflate(...s){if(!this.isGeneric||this.generics.length!=s.length){for(let c of this.overloads())if(c.isGeneric&&c.generics.length==s.length)return c.inflate(...s);y(`could not find inflatable signature of method ${this.name} with ${s.length} generic parameter(s)`)}let r=s.map(c=>c.type.object),o=e.array(e.corlib.class("System.Type"),r),i=this.object.method("MakeGenericMethod",1).invoke(o);return new e.Method(i.field("mhandle").value)}invoke(...s){return this.isStatic||y(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...s)}invokeRaw(s,...r){let o=r.map(e.toFridaValue);(!this.isStatic||e.unityVersionIsBelow201830)&&o.unshift(s),this.isInflated&&o.push(this.handle);try{let i=this.nativeFunction(...o);return e.fromFridaValue(i,this.returnType)}catch(i){switch(i==null&&y("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),i.message){case"bad argument count":y(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${r.length}`);case"expected a pointer":case"expected number":case"expected array with fields":y(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw i}}overload(...s){return this.tryOverload(...s)??y(`couldn't find overloaded method ${this.name}(${s.map(o=>o instanceof e.Class?o.type.name:o)})`)}*overloads(){for(let s of this.class.hierarchy())for(let r of s.methods)this.name==r.name&&(yield r)}parameter(s){return this.tryParameter(s)??y(`couldn't find parameter ${s} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...s){let r=s.length*1,o=s.length*2,i;e:for(let c of this.overloads()){if(c.parameterCount!=s.length)continue;let h=0,g=0;for(let p of c.parameters){let b=s[g];if(b instanceof e.Class)if(p.type.is(b.type))h+=2;else if(p.type.class.isAssignableFrom(b))h+=1;else continue e;else if(p.type.name==b)h+=2;else continue e;g++}if(!(h<r)){if(h==o)return c;if(i==null||h>i[0])i=[h,c];else if(h==i[0]){let p=0;for(let b of i[1].parameters){if(b.type.class.isAssignableFrom(c.parameters[p].type.class)){i=[h,c];continue e}p++}}}}return i?.[1]}tryParameter(s){return this.parameters.find(r=>r.name==s)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(s=>s.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(s){return this.isStatic&&y(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(r,o,i){switch(o){case"invoke":let c=s instanceof e.ValueType?r.class.isValueType?s.handle.sub(a()?e.Object.headerSize:0):y(`cannot invoke method ${r.class.type.name}::${r.name} against a value type, you must box it first`):r.class.isValueType?s.handle.add(a()?0:e.Object.headerSize):s.handle;return r.invokeRaw.bind(r,c);case"overloads":return function*(){for(let g of r[o]())g.isStatic||(yield g)};case"inflate":case"overload":case"tryOverload":let h=Reflect.get(r,o).bind(i);return function(...g){return h(...g)?.bind(s)}}return Reflect.get(r,o)}})}wrap(s){let r=+!this.isStatic|+e.unityVersionIsBelow201830;return new NativeCallback((...o)=>{let i=this.isStatic?this.class:this.class.isValueType?new e.ValueType(o[0].add(a()?e.Object.headerSize:0),this.class.type):new e.Object(o[0]),c=this.parameters.map((g,p)=>e.fromFridaValue(o[p+r],g.type)),h=s.call(i,...c);return e.toFridaValue(h)},this.returnType.fridaAlias,this.fridaSignature)}}u([l],t.prototype,"class",null),u([l],t.prototype,"flags",null),u([l],t.prototype,"implementationFlags",null),u([l],t.prototype,"fridaSignature",null),u([l],t.prototype,"generics",null),u([l],t.prototype,"isExternal",null),u([l],t.prototype,"isGeneric",null),u([l],t.prototype,"isInflated",null),u([l],t.prototype,"isStatic",null),u([l],t.prototype,"isSynchronized",null),u([l],t.prototype,"modifier",null),u([l],t.prototype,"name",null),u([l],t.prototype,"nativeFunction",null),u([l],t.prototype,"object",null),u([l],t.prototype,"parameterCount",null),u([l],t.prototype,"parameters",null),u([l],t.prototype,"relativeVirtualAddress",null),u([l],t.prototype,"returnType",null),e.Method=t;let a=()=>{let n=e.corlib.class("System.Int64").alloc();n.field("m_value").value=3735928559;let s=n.method("Equals",1).overload(n.class).invokeRaw(n,3735928559);return(a=()=>s)()}})(d||(d={}));var d;(function(e){class t extends x{static get headerSize(){return e.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&y(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(n,s,r){return s=="class"?Reflect.get(n,s).parent:s=="base"?Reflect.getOwnPropertyDescriptor(e.Object.prototype,s).get.bind(r)():Reflect.get(n,s)}})}get class(){return new e.Class(e.exports.objectGetClass(this))}get monitor(){return new e.Object.Monitor(this)}get size(){return e.exports.objectGetSize(this)}field(n){return this.tryField(n)??y(`couldn't find non-static field ${n} in hierarchy of class ${this.class.type.name}`)}method(n,s=-1){return this.tryMethod(n,s)??y(`couldn't find non-static method ${n} in hierarchy of class ${this.class.type.name}`)}ref(n){return new e.GCHandle(e.exports.gcHandleNew(this,+n))}virtualMethod(n){return new e.Method(e.exports.objectGetVirtualMethod(this,n)).bind(this)}tryField(n){let s=this.class.tryField(n);if(s?.isStatic){for(let r of this.class.hierarchy({includeCurrent:!1}))for(let o of r.fields)if(o.name==n&&!o.isStatic)return o.bind(this);return}return s?.bind(this)}tryMethod(n,s=-1){let r=this.class.tryMethod(n,s);if(r?.isStatic){for(let o of this.class.hierarchy())for(let i of o.methods)if(i.name==n&&!i.isStatic&&(s<0||i.parameterCount==s))return i.bind(this);return}return r?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new e.ValueType(e.exports.objectUnbox(this),this.class.type):y(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(n){return new e.GCHandle(e.exports.gcHandleNewWeakRef(this,+n))}}u([l],t.prototype,"class",null),u([l],t.prototype,"size",null),u([l],t,"headerSize",null),e.Object=t,function(a){class n{handle;constructor(r){this.handle=r}enter(){return e.exports.monitorEnter(this.handle)}exit(){return e.exports.monitorExit(this.handle)}pulse(){return e.exports.monitorPulse(this.handle)}pulseAll(){return e.exports.monitorPulseAll(this.handle)}tryEnter(r){return!!e.exports.monitorTryEnter(this.handle,r)}tryWait(r){return!!e.exports.monitorTryWait(this.handle,r)}wait(){return e.exports.monitorWait(this.handle)}}a.Monitor=n}(t=e.Object||(e.Object={}))})(d||(d={}));var d;(function(e){class t{name;position;type;constructor(n,s,r){this.name=n,this.position=s,this.type=r}toString(){return`${this.type.name} ${this.name}`}}e.Parameter=t})(d||(d={}));var d;(function(e){class t extends x{type;constructor(n,s){super(n),this.type=s}get(n){return e.read(this.handle.add(n*this.type.class.arrayElementSize),this.type)}read(n,s=0){let r=new globalThis.Array(n);for(let o=0;o<n;o++)r[o]=this.get(o+s);return r}set(n,s){e.write(this.handle.add(n*this.type.class.arrayElementSize),s,this.type)}toString(){return this.handle.toString()}write(n,s=0){for(let r=0;r<n.length;r++)this.set(r+s,n[r])}}e.Pointer=t})(d||(d={}));var d;(function(e){class t extends x{type;constructor(s,r){super(s),this.type=r}get value(){return e.read(this.handle,this.type)}set value(s){e.write(this.handle,s,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}e.Reference=t;function a(n,s){let r=Memory.alloc(Process.pointerSize);switch(typeof n){case"boolean":return new e.Reference(r.writeS8(+n),e.corlib.class("System.Boolean").type);case"number":switch(s?.enumValue){case e.Type.Enum.UBYTE:return new e.Reference(r.writeU8(n),s);case e.Type.Enum.BYTE:return new e.Reference(r.writeS8(n),s);case e.Type.Enum.CHAR:case e.Type.Enum.USHORT:return new e.Reference(r.writeU16(n),s);case e.Type.Enum.SHORT:return new e.Reference(r.writeS16(n),s);case e.Type.Enum.UINT:return new e.Reference(r.writeU32(n),s);case e.Type.Enum.INT:return new e.Reference(r.writeS32(n),s);case e.Type.Enum.ULONG:return new e.Reference(r.writeU64(n),s);case e.Type.Enum.LONG:return new e.Reference(r.writeS64(n),s);case e.Type.Enum.FLOAT:return new e.Reference(r.writeFloat(n),s);case e.Type.Enum.DOUBLE:return new e.Reference(r.writeDouble(n),s)}case"object":if(n instanceof e.ValueType||n instanceof e.Pointer)return new e.Reference(n.handle,n.type);if(n instanceof e.Object)return new e.Reference(r.writePointer(n),n.class.type);if(n instanceof e.String||n instanceof e.Array)return new e.Reference(r.writePointer(n),n.object.class.type);if(n instanceof NativePointer)switch(s?.enumValue){case e.Type.Enum.NUINT:case e.Type.Enum.NINT:return new e.Reference(r.writePointer(n),s)}else{if(n instanceof Int64)return new e.Reference(r.writeS64(n),e.corlib.class("System.Int64").type);if(n instanceof UInt64)return new e.Reference(r.writeU64(n),e.corlib.class("System.UInt64").type)}default:y(`couldn't create a reference to ${n} using an unhandled type ${s?.name}`)}}e.reference=a})(d||(d={}));var d;(function(e){class t extends x{get content(){return e.exports.stringGetChars(this).readUtf16String(this.length)}set content(s){let r=e.string("vfsfitvnm").handle.offsetOf(o=>o.readInt()==9)??y("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(e.String.prototype,"content",{set(o){e.exports.stringGetChars(this).writeUtf16String(o??""),this.handle.add(r).writeS32(o?.length??0)}}),this.content=s}get length(){return e.exports.stringGetLength(this)}get object(){return new e.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}e.String=t;function a(n){return new e.String(e.exports.stringNew(Memory.allocUtf8String(n??"")))}e.string=a})(d||(d={}));var d;(function(e){class t extends x{get id(){let n=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let s=Process.getCurrentThreadId(),o=ptr(n.apply(e.currentThread)).offsetOf(c=>c.readS32()==s,1024)??y("couldn't find the offset for determining the kernel id of a posix thread"),i=n;n=function(){return ptr(i.apply(this)).add(o).readS32()}}return A(e.Thread.prototype,"id",n,l),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!e.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new e.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let s=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(s.tryField("_syncContext")?.value??s.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(e.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return e.exports.threadDetach(this)}schedule(n){let s=this.synchronizationContext?.tryMethod("Post");return s==null?Process.runOnThread(this.id,n):new Promise(r=>{let o=e.delegate(e.corlib.class("System.Threading.SendOrPostCallback"),()=>{let i=n();setImmediate(()=>r(i))});Script.bindWeak(globalThis,()=>{o.field("method_ptr").value=o.field("invoke_impl").value=e.exports.domainGet}),s.invoke(o,NULL)})}tryLocalValue(n){for(let s=0;s<16;s++){let r=this.staticData.add(s*Process.pointerSize).readPointer();if(!r.isNull()){let o=new e.Object(r.readPointer()).asNullable();if(o?.class?.isSubclassOf(n,!1))return o}}}}u([l],t.prototype,"internal",null),u([l],t.prototype,"isFinalizer",null),u([l],t.prototype,"managedId",null),u([l],t.prototype,"object",null),u([l],t.prototype,"staticData",null),u([l],t.prototype,"synchronizationContext",null),e.Thread=t,A(e,"attachedThreads",()=>{if(e.exports.threadGetAttachedThreads.isNull()){let a=e.currentThread?.handle??y("Current thread is not attached to IL2CPP"),n=a.toMatchPattern(),s=[];for(let r of Process.enumerateRanges("rw-"))if(r.file==null){let o=Memory.scanSync(r.base,r.size,n);if(o.length==1){for(;;){let i=o[0].address.sub(o[0].size*s.length).readPointer();if(i.isNull()||!i.readPointer().equals(a.readPointer()))break;s.unshift(new e.Thread(i))}break}}return s}return D(e.exports.threadGetAttachedThreads).map(a=>new e.Thread(a))}),A(e,"currentThread",()=>new e.Thread(e.exports.threadGetCurrent()).asNullable()),A(e,"mainThread",()=>e.attachedThreads[0])})(d||(d={}));var d;(function(e){let t=class extends x{static get Enum(){let n=(r,o=i=>i)=>o(e.corlib.class(r)).type.enumValue,s={VOID:n("System.Void"),BOOLEAN:n("System.Boolean"),CHAR:n("System.Char"),BYTE:n("System.SByte"),UBYTE:n("System.Byte"),SHORT:n("System.Int16"),USHORT:n("System.UInt16"),INT:n("System.Int32"),UINT:n("System.UInt32"),LONG:n("System.Int64"),ULONG:n("System.UInt64"),NINT:n("System.IntPtr"),NUINT:n("System.UIntPtr"),FLOAT:n("System.Single"),DOUBLE:n("System.Double"),POINTER:n("System.IntPtr",r=>r.field("m_value")),VALUE_TYPE:n("System.Decimal"),OBJECT:n("System.Object"),STRING:n("System.String"),CLASS:n("System.Array"),ARRAY:n("System.Void",r=>r.arrayClass),NARRAY:n("System.Void",r=>new e.Class(e.exports.classGetArrayClass(r,2))),GENERIC_INSTANCE:n("System.Int32",r=>r.interfaces.find(o=>o.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:s}),Q({...s,VAR:n("System.Action`1",r=>r.generics[0]),MVAR:n("System.Array",r=>r.method("AsReadOnly",1).generics[0])})}get class(){return new e.Class(e.exports.typeGetClass(this))}get fridaAlias(){function n(s){let r=s.class.fields.filter(o=>!o.isStatic);return r.length==0?["char"]:r.map(o=>o.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case e.Type.Enum.VOID:return"void";case e.Type.Enum.BOOLEAN:return"bool";case e.Type.Enum.CHAR:return"uchar";case e.Type.Enum.BYTE:return"int8";case e.Type.Enum.UBYTE:return"uint8";case e.Type.Enum.SHORT:return"int16";case e.Type.Enum.USHORT:return"uint16";case e.Type.Enum.INT:return"int32";case e.Type.Enum.UINT:return"uint32";case e.Type.Enum.LONG:return"int64";case e.Type.Enum.ULONG:return"uint64";case e.Type.Enum.FLOAT:return"float";case e.Type.Enum.DOUBLE:return"double";case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return"pointer";case e.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:n(this);case e.Type.Enum.CLASS:case e.Type.Enum.OBJECT:case e.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?n(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case e.Type.Enum.BOOLEAN:case e.Type.Enum.CHAR:case e.Type.Enum.BYTE:case e.Type.Enum.UBYTE:case e.Type.Enum.SHORT:case e.Type.Enum.USHORT:case e.Type.Enum.INT:case e.Type.Enum.UINT:case e.Type.Enum.LONG:case e.Type.Enum.ULONG:case e.Type.Enum.FLOAT:case e.Type.Enum.DOUBLE:case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return!0;default:return!1}}get name(){let n=e.exports.typeGetName(this);try{return n.readUtf8String()}finally{e.free(n)}}get object(){return new e.Object(e.exports.typeGetObject(this))}get enumValue(){return e.exports.typeGetTypeEnum(this)}is(n){return e.exports.typeEquals.isNull()?this.object.method("Equals").invoke(n.object):!!e.exports.typeEquals(this,n)}toString(){return this.name}};u([l],t.prototype,"class",null),u([l],t.prototype,"fridaAlias",null),u([l],t.prototype,"isByReference",null),u([l],t.prototype,"isPrimitive",null),u([l],t.prototype,"name",null),u([l],t.prototype,"object",null),u([l],t.prototype,"enumValue",null),u([l],t,"Enum",null),t=u([L],t),e.Type=t})(d||(d={}));var d;(function(e){class t extends x{type;constructor(n,s){super(n),this.type=s}box(){return new e.Object(e.exports.valueTypeBox(this.type.class,this))}field(n){return this.tryField(n)??y(`couldn't find non-static field ${n} in hierarchy of class ${this.type.name}`)}method(n,s=-1){return this.tryMethod(n,s)??y(`couldn't find non-static method ${n} in hierarchy of class ${this.type.name}`)}tryField(n){let s=this.type.class.tryField(n);if(s?.isStatic){for(let r of this.type.class.hierarchy())for(let o of r.fields)if(o.name==n&&!o.isStatic)return o.bind(this);return}return s?.bind(this)}tryMethod(n,s=-1){let r=this.type.class.tryMethod(n,s);if(r?.isStatic){for(let o of this.type.class.hierarchy())for(let i of o.methods)if(i.name==n&&!i.isStatic&&(s<0||i.parameterCount==s))return i.bind(this);return}return r?.bind(this)}toString(){let n=this.method("ToString",0);return this.isNull()?"null":n.class.isValueType?n.invoke().content??"null":this.box().toString()??"null"}}e.ValueType=t})(d||(d={}));globalThis.Il2Cpp=d;Il2Cpp.perform(function(){let e=Il2Cpp.domain.assembly("Assembly-CSharp").image,t=e.class("GoodyHutHelper");console.log("[+] Enhanced GoodyHutHelper Auto-Complete Script Loaded"),console.log(`[+] GoodyHutHelper class found: ${t.handle}`);let a={AUTO_COMPLETE_ENABLED:!0,SCAN_INTERVAL:2e3,COMPLETION_DELAY:100,MAX_RETRIES:3,VERBOSE_LOGGING:!0},n={totalAttempts:0,successfulCompletions:0,failedCompletions:0,startTime:Date.now()},s=new Map;function r(m,f){let T=new Date().toLocaleTimeString();console.log(`[${T}] ${m} ${f}`)}function o(m,f,...T){try{let _=m.method(f);if(_){let S=_.invoke(...T);return a.VERBOSE_LOGGING&&r("DEBUG",`Successfully called ${f}, result: ${S}`),S}else return r("WARN",`Method ${f} not found on instance`),null}catch(_){return r("ERROR",`Error calling ${f}: ${_}`),null}}function i(m){try{let f=o(m,"CanBuyThrough");return f!==null&&f}catch(f){return r("ERROR",`Error checking CanBuyThrough: ${f}`),!1}}function c(m){try{let f=o(m,"IsJobComplete"),T=o(m,"CanCollect"),_=o(m,"GetJobTimeLeft"),S="UNKNOWN",N=!1,w=`JobComplete: ${f}, CanCollect: ${T}, TimeLeft: ${_}`;return f===!0&&T===!0?(S="IDLE_READY",N=!1,w+=" - Ready for new collection"):f===!1&&T===!1?(S="COLLECTING",N=!0,w+=" - Collection in progress"):f===!0&&T===!1?(S="COMPLETED_AWAITING",N=!1,w+=" - Completed, awaiting reward collection"):f===!1&&T===!0&&(S="INCONSISTENT",N=!1,w+=" - Inconsistent state, needs investigation"),_!==null&&typeof _=="number"&&(_>0?S!=="COLLECTING"&&(w+=` - WARNING: TimeLeft positive (${_}) but state is ${S}`,N=!0,S="COLLECTING_BY_TIMER"):_<-1&&(w+=` - Completed ${Math.abs(_)}s ago`)),{state:S,isCollecting:N,details:w}}catch(f){return r("ERROR",`Error checking collection state: ${f}`),{state:"ERROR",isCollecting:!1,details:`Error: ${f}`}}}function h(m){return c(m).isCollecting}function g(m){try{if(n.totalAttempts++,r("INFO","Attempting auto-completion of goody hut collection..."),!i(m))return r("WARN","Cannot use buy-through on this instance - may require premium currency"),n.failedCompletions++,!1;let f=s.get(m)||0;return f>=a.MAX_RETRIES?(r("WARN",`Max retries (${a.MAX_RETRIES}) reached for instance, skipping`),s.delete(m),n.failedCompletions++,!1):o(m,"DoJobBuyThrough")!==null?(r("SUCCESS","Successfully auto-completed goody hut collection!"),s.delete(m),n.successfulCompletions++,!0):(r("WARN",`DoJobBuyThrough call failed (attempt ${f+1}/${a.MAX_RETRIES})`),s.set(m,f+1),n.failedCompletions++,!1)}catch(f){return r("ERROR",`Error in auto-completion: ${f}`),n.failedCompletions++,!1}}function p(m){try{let f=o(m,"GetHealth"),T=o(m,"GetMaxHealth"),_=o(m,"GetExplorations"),S=o(m,"GetRewardType"),N=o(m,"GetRewardAmount");return`Health: ${f}/${T}, Explorations: ${_}, Reward: ${N} ${S}`}catch{return"Info unavailable"}}function b(){r("DEBUG","=== Instance Detection Debug ===");try{r("DEBUG","Attempting Il2Cpp.gc.choose...");let m=Il2Cpp.gc.choose(t);r("DEBUG",`Il2Cpp.gc.choose found ${m.length} instances`);try{let _=e.class("EntityController"),S=Il2Cpp.gc.choose(_);r("DEBUG",`Found ${S.length} EntityController instances`);let N=0;S.forEach((w,O)=>{try{let G=w.field("m_goodyHut");G&&G.value&&G.value!==null&&G.value.toString()!=="0x0"&&(N++,r("DEBUG",`EntityController ${O} has GoodyHutHelper component`))}catch{}}),r("DEBUG",`Found ${N} entities with GoodyHutHelper components`)}catch(_){r("DEBUG",`Error checking EntityController: ${_}`)}r("DEBUG","Checking GoodyHutHelper class methods...");let f=t.methods;r("DEBUG",`GoodyHutHelper has ${f.length} methods`),["StartCollect","CanCollect","IsJobComplete","DoJobBuyThrough"].forEach(_=>{try{let S=t.method(_);r("DEBUG",`Method ${_}: ${S?"Found":"Not found"}`)}catch(S){r("DEBUG",`Method ${_}: Error - ${S}`)}})}catch(m){r("ERROR",`Error in debugInstanceDetection: ${m}`)}r("DEBUG","=== End Debug ===")}function E(){try{let m=Il2Cpp.gc.choose(t);if(m.length===0){try{let _=e.class("EntityController"),S=Il2Cpp.gc.choose(_);S.length>0?(r("DEBUG",`No direct GoodyHutHelper instances, but found ${S.length} EntityController instances`),S.forEach((N,w)=>{try{let O=N.field("m_goodyHut");if(O&&O.value&&O.value!==null&&O.value.toString()!=="0x0"){let G=O.value;r("DEBUG",`Found GoodyHutHelper via EntityController ${w}`);let B=c(G);if(r("DEBUG",`EntityController ${w} state: ${B.state} - ${B.details}`),B.isCollecting){let W=p(G);r("INFO",`EntityController ${w} goody hut collecting - ${W}`),a.AUTO_COMPLETE_ENABLED&&g(G)}}}catch{}})):a.VERBOSE_LOGGING&&r("DEBUG","No GoodyHutHelper or EntityController instances found - game may not be fully loaded")}catch(_){a.VERBOSE_LOGGING&&r("DEBUG",`Alternative detection failed: ${_}`)}return}r("INFO",`Scanning ${m.length} GoodyHutHelper instances...`);let f=0,T={IDLE_READY:0,COLLECTING:0,COMPLETED_AWAITING:0,INCONSISTENT:0,ERROR:0,OTHER:0};m.forEach((_,S)=>{try{let N=c(_);if(T.hasOwnProperty(N.state)?T[N.state]++:T.OTHER++,a.VERBOSE_LOGGING){let w=p(_);r("DEBUG",`Instance ${S}: ${N.state} - ${N.details} | ${w}`)}if(N.isCollecting){f++;let w=p(_);r("INFO",`Instance ${S} COLLECTING - ${w}`),r("INFO",`Collection details: ${N.details}`),a.AUTO_COMPLETE_ENABLED&&g(_)}}catch(N){r("ERROR",`Error processing instance ${S}: ${N}`),T.ERROR++}}),r("INFO",`State breakdown: IDLE=${T.IDLE_READY}, COLLECTING=${T.COLLECTING}, COMPLETED=${T.COMPLETED_AWAITING}, INCONSISTENT=${T.INCONSISTENT}, ERROR=${T.ERROR}, OTHER=${T.OTHER}`),f>0?r("SUCCESS",`Found ${f} collecting goody huts - attempting auto-completion`):r("INFO",`Scanned ${m.length} instances, none currently collecting`)}catch(m){r("ERROR",`Error in scanAndAutoComplete: ${m}`)}}function $(){let m=Math.floor((Date.now()-n.startTime)/1e3),f=n.totalAttempts>0?Math.round(n.successfulCompletions/n.totalAttempts*100):0;r("STATS",`Runtime: ${m}s | Attempts: ${n.totalAttempts} | Success: ${n.successfulCompletions} | Failed: ${n.failedCompletions} | Rate: ${f}%`)}try{let m=t.method("StartCollect");m?(r("INFO","Hooking StartCollect method..."),m.implementation=function(){r("INFO","StartCollect called - triggering original method first");let f=this,T=p(f);r("INFO",`Starting collection on instance: ${T}`);let _=f.method("StartCollect").invoke();return a.AUTO_COMPLETE_ENABLED&&setTimeout(()=>{try{r("INFO","Attempting auto-completion after StartCollect..."),g(f)&&r("SUCCESS","Auto-completed collection immediately after start!")}catch(S){r("ERROR",`Error in delayed auto-completion: ${S}`)}},a.COMPLETION_DELAY),_},r("SUCCESS","StartCollect hook installed successfully!")):r("WARN","StartCollect method not found, using periodic scanning only")}catch(m){r("ERROR",`Error hooking StartCollect: ${m}`),r("INFO","Falling back to periodic scanning method")}r("INFO",`Starting periodic scan every ${a.SCAN_INTERVAL}ms...`),setInterval(()=>{E()},a.SCAN_INTERVAL),setInterval(()=>{$()},3e4),setTimeout(()=>{r("INFO","Performing initial debug check..."),b()},3e3),setTimeout(()=>{r("INFO","Performing initial scan..."),E()},5e3);function v(m=0){try{let f=Il2Cpp.gc.choose(t);if(f.length===0){r("ERROR","No GoodyHutHelper instances found for testing");return}if(m>=f.length){r("ERROR",`Instance index ${m} out of range. Found ${f.length} instances.`);return}let T=f[m],_=c(T),S=p(T);r("INFO",`=== MANUAL TEST on Instance ${m} ===`),r("INFO",`State: ${_.state}`),r("INFO",`Details: ${_.details}`),r("INFO",`Info: ${S}`),r("INFO","Attempting manual DoJobBuyThrough..."),g(T)?r("SUCCESS","Manual test completed successfully!"):r("WARN","Manual test failed - check logs above for details")}catch(f){r("ERROR",`Error in manual test: ${f}`)}}function U(){try{let m=Il2Cpp.gc.choose(t);r("INFO",`=== FORCE COMPLETE ALL ${m.length} INSTANCES ===`);let f=0,T=0;m.forEach((_,S)=>{try{let N=c(_);r("INFO",`Force completing instance ${S}: ${N.state}`),f++,g(_)&&T++}catch(N){r("ERROR",`Error force completing instance ${S}: ${N}`)}}),r("INFO",`Force completion results: ${T}/${f} successful`)}catch(m){r("ERROR",`Error in forceCompleteAll: ${m}`)}}function k(){try{let m=Il2Cpp.gc.choose(t);r("INFO",`=== ALL INSTANCE STATES (${m.length} instances) ===`),m.forEach((f,T)=>{try{let _=c(f),S=p(f);r("INFO",`[${T}] ${_.state}: ${_.details} | ${S}`)}catch(_){r("ERROR",`Error getting state for instance ${T}: ${_}`)}})}catch(m){r("ERROR",`Error in showAllStates: ${m}`)}}let R={enable:()=>{a.AUTO_COMPLETE_ENABLED=!0,r("INFO","Auto-completion enabled")},disable:()=>{a.AUTO_COMPLETE_ENABLED=!1,r("INFO","Auto-completion disabled")},verbose:m=>{a.VERBOSE_LOGGING=m,r("INFO",`Verbose logging ${m?"enabled":"disabled"}`)},stats:()=>$(),scan:()=>E(),debug:()=>b(),test:(m=0)=>v(m),forceAll:()=>U(),states:()=>k(),help:()=>{r("INFO","=== GoodyHut Config Commands ==="),r("INFO","goodyHutConfig.enable() - Enable auto-completion"),r("INFO","goodyHutConfig.disable() - Disable auto-completion"),r("INFO","goodyHutConfig.verbose(true/false) - Toggle verbose logging"),r("INFO","goodyHutConfig.stats() - Show statistics"),r("INFO","goodyHutConfig.scan() - Manual scan"),r("INFO","goodyHutConfig.debug() - Debug instance detection"),r("INFO","goodyHutConfig.test(index) - Test auto-complete on specific instance"),r("INFO","goodyHutConfig.forceAll() - Force complete all instances"),r("INFO","goodyHutConfig.states() - Show all instance states")}};globalThis.goodyHutConfig=R,globalThis.GoodyHutHelper=t;function Y(){r("INFO","Checking game load status...");try{let m=["GameManager","SimulationManager","EntityManager","BuildingManager","ResourceManager","UIManager"],f=0;m.forEach(T=>{try{e.class(T)&&(f++,r("DEBUG",`Found class: ${T}`))}catch{r("DEBUG",`Class not found: ${T}`)}}),r("INFO",`Game load status: ${f}/${m.length} core classes found`),f>=2?r("SUCCESS","Game appears to be loaded - core classes accessible"):r("WARN","Game may not be fully loaded - few core classes found")}catch(m){r("ERROR",`Error checking game load status: ${m}`)}}setTimeout(()=>{Y()},2e3),r("SUCCESS","Enhanced GoodyHutHelper auto-completion script fully initialized!"),r("INFO","=== Available Commands ==="),r("INFO","goodyHutConfig.help() - Show all available commands"),r("INFO","goodyHutConfig.states() - Show current state of all instances"),r("INFO","goodyHutConfig.test(0) - Test auto-complete on instance 0"),r("INFO","goodyHutConfig.forceAll() - Force complete all instances"),r("INFO","goodyHutConfig.verbose(true) - Enable detailed logging")});
