import "frida-il2cpp-bridge";

console.log("[+] Connection Test Script Starting...");

Il2Cpp.perform(function(){
	console.log("[+] Il2Cpp.perform() called successfully");
	console.log(`[+] Unity Version: ${Il2Cpp.unityVersion}`);
	console.log(`[+] Application: ${Il2Cpp.application.identifier}`);
	
	try {
		console.log("[+] Listing available assemblies:");
		Il2Cpp.domain.assemblies.forEach((assembly, index) => {
			console.log(`[${index}] ${assembly.name}`);
		});
		
		console.log("[+] Attempting to load Assembly-CSharp...");
		const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
		console.log(`[+] Assembly-CSharp loaded: ${AssemblyCSharp.name}`);
		console.log(`[+] Class count: ${AssemblyCSharp.classCount}`);
		
		console.log("[+] Connection test completed successfully!");
		
	} catch (error) {
		console.log(`[-] Error in connection test: ${error}`);
	}
});
