import "frida-il2cpp-bridge";

Il2Cpp.perform(function(){
	console.log("[+] Minimal Test Script Starting...");
	
	try {
		const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
		console.log("[+] Assembly-CSharp loaded successfully");
		
		// Try to get the GoodyHutHelper class
		const GoodyHutHelper = AssemblyCSharp.class("GoodyHutHelper");
		console.log(`[+] GoodyHutHelper class found: ${GoodyHutHelper.handle}`);
		
		// Try to find instances
		const instances = Il2Cpp.gc.choose(GoodyHutHelper);
		console.log(`[+] Found ${instances.length} GoodyHutHelper instances`);
		
		if (instances.length > 0) {
			console.log("[+] Testing first instance...");
			const instance = instances[0];
			
			// Test basic method calls
			try {
				const isJobComplete = instance.method("IsJobComplete").invoke();
				console.log(`[+] IsJobComplete: ${isJobComplete}`);
			} catch (error) {
				console.log(`[-] Error calling IsJobComplete: ${error}`);
			}
			
			try {
				const canCollect = instance.method("CanCollect").invoke();
				console.log(`[+] CanCollect: ${canCollect}`);
			} catch (error) {
				console.log(`[-] Error calling CanCollect: ${error}`);
			}
			
			try {
				const timeLeft = instance.method("GetJobTimeLeft").invoke();
				console.log(`[+] GetJobTimeLeft: ${timeLeft}`);
			} catch (error) {
				console.log(`[-] Error calling GetJobTimeLeft: ${error}`);
			}
			
			try {
				const canBuyThrough = instance.method("CanBuyThrough").invoke();
				console.log(`[+] CanBuyThrough: ${canBuyThrough}`);
			} catch (error) {
				console.log(`[-] Error calling CanBuyThrough: ${error}`);
			}
		}
		
		// Try to hook StartCollect
		try {
			const startCollectMethod = GoodyHutHelper.method("StartCollect");
			if (startCollectMethod) {
				console.log("[+] StartCollect method found, installing hook...");
				
				startCollectMethod.implementation = function() {
					console.log("[*] StartCollect called!");
					return this.method("StartCollect").invoke();
				};
				
				console.log("[+] Hook installed successfully");
			} else {
				console.log("[-] StartCollect method not found");
			}
		} catch (error) {
			console.log(`[-] Error hooking StartCollect: ${error}`);
		}
		
		console.log("[+] Minimal test completed successfully");
		
	} catch (error) {
		console.log(`[-] Error in minimal test: ${error}`);
		if (error instanceof Error) {
			console.log(`[-] Stack trace: ${error.stack}`);
		}
	}
});
