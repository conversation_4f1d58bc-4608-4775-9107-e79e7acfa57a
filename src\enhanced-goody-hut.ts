import "frida-il2cpp-bridge";

Il2Cpp.perform(function(){
	const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
	
	// Get the GoodyHutHelper class
	const GoodyHutHelper = AssemblyCSharp.class("GoodyHutHelper");
	
	console.log("[+] Enhanced GoodyHutHelper Auto-Complete Script Loaded");
	console.log(`[+] GoodyHutHelper class found: ${GoodyHutHelper.handle}`);
	
	// Configuration
	const CONFIG = {
		AUTO_COMPLETE_ENABLED: true,
		SCAN_INTERVAL: 2000, // 2 seconds
		COMPLETION_DELAY: 100, // 100ms delay after StartCollect
		MAX_RETRIES: 3,
		VERBOSE_LOGGING: true
	};
	
	// Statistics tracking
	const stats = {
		totalAttempts: 0,
		successfulCompletions: 0,
		failedCompletions: 0,
		startTime: Date.now()
	};
	
	// Track active GoodyHutHelper instances
	const activeGoodyHuts = new Map<Il2Cpp.Object, number>(); // instance -> retry count
	
	// Enhanced logging function
	function log(level: string, message: string): void {
		const timestamp = new Date().toLocaleTimeString();
		console.log(`[${timestamp}] ${level} ${message}`);
	}
	
	// Helper function to safely call methods with enhanced error handling
	function safeMethodCall(instance: Il2Cpp.Object, methodName: string, ...args: any[]): any {
		try {
			const method = instance.method(methodName);
			if (method) {
				const result = method.invoke(...args);
				if (CONFIG.VERBOSE_LOGGING) {
					log("DEBUG", `Successfully called ${methodName}, result: ${result}`);
				}
				return result;
			} else {
				log("WARN", `Method ${methodName} not found on instance`);
				return null;
			}
		} catch (error) {
			log("ERROR", `Error calling ${methodName}: ${error}`);
			return null;
		}
	}
	
	// Function to check if instance can use buy-through
	function canUseBuyThrough(instance: Il2Cpp.Object): boolean {
		try {
			const canBuyThrough = safeMethodCall(instance, "CanBuyThrough");
			return canBuyThrough !== null && canBuyThrough;
		} catch (error) {
			log("ERROR", `Error checking CanBuyThrough: ${error}`);
			return false;
		}
	}
	
	// Enhanced function to determine goody hut state with proper logic
	function getGoodyHutState(instance: Il2Cpp.Object): { state: string, isCollecting: boolean, details: string } {
		try {
			const isJobComplete = safeMethodCall(instance, "IsJobComplete");
			const canCollect = safeMethodCall(instance, "CanCollect");
			const timeLeft = safeMethodCall(instance, "GetJobTimeLeft");

			let state = "UNKNOWN";
			let isCollecting = false;
			let details = `JobComplete: ${isJobComplete}, CanCollect: ${canCollect}, TimeLeft: ${timeLeft}`;

			// Correct state detection logic based on observed game behavior
			if (isJobComplete === true && canCollect === true) {
				state = "IDLE_READY";
				isCollecting = false;
				details += " - Ready for new collection";
			} else if (isJobComplete === false && canCollect === false) {
				state = "COLLECTING";
				isCollecting = true;
				details += " - Collection in progress";
			} else if (isJobComplete === true && canCollect === false) {
				state = "COMPLETED_AWAITING";
				isCollecting = false;
				details += " - Completed, awaiting reward collection";
			} else if (isJobComplete === false && canCollect === true) {
				state = "INCONSISTENT";
				isCollecting = false;
				details += " - Inconsistent state, needs investigation";
			}

			// Additional check using time left for validation
			if (timeLeft !== null && typeof timeLeft === 'number') {
				if (timeLeft > 0) {
					// Positive time = countdown active = collecting
					if (state !== "COLLECTING") {
						details += ` - WARNING: TimeLeft positive (${timeLeft}) but state is ${state}`;
						isCollecting = true; // Override based on positive timer
						state = "COLLECTING_BY_TIMER";
					}
				} else if (timeLeft < -1) {
					// Very negative time = long since completed
					details += ` - Completed ${Math.abs(timeLeft)}s ago`;
				}
			}

			return { state, isCollecting, details };
		} catch (error) {
			log("ERROR", `Error checking collection state: ${error}`);
			return { state: "ERROR", isCollecting: false, details: `Error: ${error}` };
		}
	}

	// Simplified function for backward compatibility
	function isCurrentlyCollecting(instance: Il2Cpp.Object): boolean {
		const stateInfo = getGoodyHutState(instance);
		return stateInfo.isCollecting;
	}
	
	// Enhanced function to automatically complete collection with retry logic
	function autoCompleteCollection(instance: Il2Cpp.Object): boolean {
		try {
			stats.totalAttempts++;
			
			log("INFO", "Attempting auto-completion of goody hut collection...");
			
			// Check if we can use buy-through
			if (!canUseBuyThrough(instance)) {
				log("WARN", "Cannot use buy-through on this instance - may require premium currency");
				stats.failedCompletions++;
				return false;
			}
			
			// Get current retry count
			const retryCount = activeGoodyHuts.get(instance) || 0;
			if (retryCount >= CONFIG.MAX_RETRIES) {
				log("WARN", `Max retries (${CONFIG.MAX_RETRIES}) reached for instance, skipping`);
				activeGoodyHuts.delete(instance);
				stats.failedCompletions++;
				return false;
			}
			
			// Call DoJobBuyThrough to instantly complete
			const result = safeMethodCall(instance, "DoJobBuyThrough");
			
			if (result !== null) {
				log("SUCCESS", "Successfully auto-completed goody hut collection!");
				activeGoodyHuts.delete(instance);
				stats.successfulCompletions++;
				return true;
			} else {
				log("WARN", `DoJobBuyThrough call failed (attempt ${retryCount + 1}/${CONFIG.MAX_RETRIES})`);
				activeGoodyHuts.set(instance, retryCount + 1);
				stats.failedCompletions++;
				return false;
			}
		} catch (error) {
			log("ERROR", `Error in auto-completion: ${error}`);
			stats.failedCompletions++;
			return false;
		}
	}
	
	// Function to get detailed instance information
	function getInstanceInfo(instance: Il2Cpp.Object): string {
		try {
			const health = safeMethodCall(instance, "GetHealth");
			const maxHealth = safeMethodCall(instance, "GetMaxHealth");
			const explorations = safeMethodCall(instance, "GetExplorations");
			const rewardType = safeMethodCall(instance, "GetRewardType");
			const rewardAmount = safeMethodCall(instance, "GetRewardAmount");
			
			return `Health: ${health}/${maxHealth}, Explorations: ${explorations}, Reward: ${rewardAmount} ${rewardType}`;
		} catch (error) {
			return "Info unavailable";
		}
	}
	
	// Debug function to investigate instance detection issues
	function debugInstanceDetection(): void {
		log("DEBUG", "=== Instance Detection Debug ===");

		try {
			// Try multiple approaches to find instances
			log("DEBUG", "Attempting Il2Cpp.gc.choose...");
			const instances = Il2Cpp.gc.choose(GoodyHutHelper);
			log("DEBUG", `Il2Cpp.gc.choose found ${instances.length} instances`);

			// Try to find EntityController instances (parent class)
			try {
				const EntityController = AssemblyCSharp.class("EntityController");
				const entityInstances = Il2Cpp.gc.choose(EntityController);
				log("DEBUG", `Found ${entityInstances.length} EntityController instances`);

				// Check if any have GoodyHutHelper components
				let goodyHutCount = 0;
				entityInstances.forEach((entity, index) => {
					try {
						const goodyHut = entity.field("m_goodyHut");
						if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
							goodyHutCount++;
							log("DEBUG", `EntityController ${index} has GoodyHutHelper component`);
						}
					} catch (e) {
						// Ignore - not all entities have goody hut components
					}
				});
				log("DEBUG", `Found ${goodyHutCount} entities with GoodyHutHelper components`);
			} catch (error) {
				log("DEBUG", `Error checking EntityController: ${error}`);
			}

			// Check if class methods are accessible
			log("DEBUG", "Checking GoodyHutHelper class methods...");
			const methods = GoodyHutHelper.methods;
			log("DEBUG", `GoodyHutHelper has ${methods.length} methods`);

			// List some key methods
			const keyMethods = ["StartCollect", "CanCollect", "IsJobComplete", "DoJobBuyThrough"];
			keyMethods.forEach(methodName => {
				try {
					const method = GoodyHutHelper.method(methodName);
					log("DEBUG", `Method ${methodName}: ${method ? 'Found' : 'Not found'}`);
				} catch (error) {
					log("DEBUG", `Method ${methodName}: Error - ${error}`);
				}
			});

		} catch (error) {
			log("ERROR", `Error in debugInstanceDetection: ${error}`);
		}

		log("DEBUG", "=== End Debug ===");
	}

	// Enhanced scanning function with detailed state analysis
	function scanAndAutoComplete(): void {
		try {
			// Use Il2Cpp.gc.choose to find all GoodyHutHelper instances
			const instances = Il2Cpp.gc.choose(GoodyHutHelper);

			if (instances.length === 0) {
				// Try alternative detection method via EntityController
				try {
					const EntityController = AssemblyCSharp.class("EntityController");
					const entityInstances = Il2Cpp.gc.choose(EntityController);

					if (entityInstances.length > 0) {
						log("DEBUG", `No direct GoodyHutHelper instances, but found ${entityInstances.length} EntityController instances`);

						// Check entities for goody hut components
						entityInstances.forEach((entity, index) => {
							try {
								const goodyHut = entity.field("m_goodyHut");
								if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
									const goodyHutInstance = goodyHut.value as Il2Cpp.Object;
									log("DEBUG", `Found GoodyHutHelper via EntityController ${index}`);

									// Get detailed state information
									const stateInfo = getGoodyHutState(goodyHutInstance);
									log("DEBUG", `EntityController ${index} state: ${stateInfo.state} - ${stateInfo.details}`);

									// Check if this instance is collecting
									if (stateInfo.isCollecting) {
										const info = getInstanceInfo(goodyHutInstance);
										log("INFO", `EntityController ${index} goody hut collecting - ${info}`);

										if (CONFIG.AUTO_COMPLETE_ENABLED) {
											autoCompleteCollection(goodyHutInstance);
										}
									}
								}
							} catch (e) {
								// Ignore - not all entities have goody hut components
							}
						});
					} else {
						if (CONFIG.VERBOSE_LOGGING) {
							log("DEBUG", "No GoodyHutHelper or EntityController instances found - game may not be fully loaded");
						}
					}
				} catch (error) {
					if (CONFIG.VERBOSE_LOGGING) {
						log("DEBUG", `Alternative detection failed: ${error}`);
					}
				}
				return;
			}

			log("INFO", `Scanning ${instances.length} GoodyHutHelper instances...`);

			let collectingCount = 0;
			let stateBreakdown = {
				IDLE_READY: 0,
				COLLECTING: 0,
				COMPLETED_AWAITING: 0,
				INCONSISTENT: 0,
				ERROR: 0,
				OTHER: 0
			};

			instances.forEach((instance, index) => {
				try {
					// Get detailed state information
					const stateInfo = getGoodyHutState(instance);

					// Count states
					if (stateBreakdown.hasOwnProperty(stateInfo.state)) {
						(stateBreakdown as any)[stateInfo.state]++;
					} else {
						stateBreakdown.OTHER++;
					}

					// Log detailed state for each instance
					if (CONFIG.VERBOSE_LOGGING) {
						const info = getInstanceInfo(instance);
						log("DEBUG", `Instance ${index}: ${stateInfo.state} - ${stateInfo.details} | ${info}`);
					}

					// Check if instance is currently collecting
					if (stateInfo.isCollecting) {
						collectingCount++;
						const info = getInstanceInfo(instance);
						log("INFO", `Instance ${index} COLLECTING - ${info}`);
						log("INFO", `Collection details: ${stateInfo.details}`);

						if (CONFIG.AUTO_COMPLETE_ENABLED) {
							autoCompleteCollection(instance);
						}
					}
				} catch (error) {
					log("ERROR", `Error processing instance ${index}: ${error}`);
					stateBreakdown.ERROR++;
				}
			});

			// Log state breakdown
			log("INFO", `State breakdown: IDLE=${stateBreakdown.IDLE_READY}, COLLECTING=${stateBreakdown.COLLECTING}, COMPLETED=${stateBreakdown.COMPLETED_AWAITING}, INCONSISTENT=${stateBreakdown.INCONSISTENT}, ERROR=${stateBreakdown.ERROR}, OTHER=${stateBreakdown.OTHER}`);

			if (collectingCount > 0) {
				log("SUCCESS", `Found ${collectingCount} collecting goody huts - attempting auto-completion`);
			} else {
				log("INFO", `Scanned ${instances.length} instances, none currently collecting`);
			}
		} catch (error) {
			log("ERROR", `Error in scanAndAutoComplete: ${error}`);
		}
	}
	
	// Function to print statistics
	function printStats(): void {
		const runtime = Math.floor((Date.now() - stats.startTime) / 1000);
		const successRate = stats.totalAttempts > 0 ? 
			Math.round((stats.successfulCompletions / stats.totalAttempts) * 100) : 0;
		
		log("STATS", `Runtime: ${runtime}s | Attempts: ${stats.totalAttempts} | Success: ${stats.successfulCompletions} | Failed: ${stats.failedCompletions} | Rate: ${successRate}%`);
	}
	
	// Hook the StartCollect method to automatically trigger completion
	try {
		const startCollectMethod = GoodyHutHelper.method("StartCollect");
		
		if (startCollectMethod) {
			log("INFO", "Hooking StartCollect method...");
			
			startCollectMethod.implementation = function() {
				log("INFO", "StartCollect called - triggering original method first");
				
				// Store reference to this instance
				const thisInstance = this as Il2Cpp.Object;
				const info = getInstanceInfo(thisInstance);
				log("INFO", `Starting collection on instance: ${info}`);
				
				// Call the original method
				const result = thisInstance.method("StartCollect").invoke();
				
				// Schedule auto-completion after a short delay
				if (CONFIG.AUTO_COMPLETE_ENABLED) {
					setTimeout(() => {
						try {
							log("INFO", "Attempting auto-completion after StartCollect...");
							if (autoCompleteCollection(thisInstance)) {
								log("SUCCESS", "Auto-completed collection immediately after start!");
							}
						} catch (error) {
							log("ERROR", `Error in delayed auto-completion: ${error}`);
						}
					}, CONFIG.COMPLETION_DELAY);
				}
				
				return result;
			};
			
			log("SUCCESS", "StartCollect hook installed successfully!");
		} else {
			log("WARN", "StartCollect method not found, using periodic scanning only");
		}
	} catch (error) {
		log("ERROR", `Error hooking StartCollect: ${error}`);
		log("INFO", "Falling back to periodic scanning method");
	}
	
	// Periodic scanning as backup method
	log("INFO", `Starting periodic scan every ${CONFIG.SCAN_INTERVAL}ms...`);
	setInterval(() => {
		scanAndAutoComplete();
	}, CONFIG.SCAN_INTERVAL);

	// Statistics reporting
	setInterval(() => {
		printStats();
	}, 30000); // Every 30 seconds

	// Initial debug and scan with longer delay to allow game loading
	setTimeout(() => {
		log("INFO", "Performing initial debug check...");
		debugInstanceDetection();
	}, 3000); // 3 second delay

	setTimeout(() => {
		log("INFO", "Performing initial scan...");
		scanAndAutoComplete();
	}, 5000); // 5 second delay
	
	// Manual testing functions
	function testAutoComplete(instanceIndex: number = 0): void {
		try {
			const instances = Il2Cpp.gc.choose(GoodyHutHelper);
			if (instances.length === 0) {
				log("ERROR", "No GoodyHutHelper instances found for testing");
				return;
			}

			if (instanceIndex >= instances.length) {
				log("ERROR", `Instance index ${instanceIndex} out of range. Found ${instances.length} instances.`);
				return;
			}

			const instance = instances[instanceIndex];
			const stateInfo = getGoodyHutState(instance);
			const info = getInstanceInfo(instance);

			log("INFO", `=== MANUAL TEST on Instance ${instanceIndex} ===`);
			log("INFO", `State: ${stateInfo.state}`);
			log("INFO", `Details: ${stateInfo.details}`);
			log("INFO", `Info: ${info}`);

			log("INFO", "Attempting manual DoJobBuyThrough...");
			const result = autoCompleteCollection(instance);

			if (result) {
				log("SUCCESS", "Manual test completed successfully!");
			} else {
				log("WARN", "Manual test failed - check logs above for details");
			}
		} catch (error) {
			log("ERROR", `Error in manual test: ${error}`);
		}
	}

	function forceCompleteAll(): void {
		try {
			const instances = Il2Cpp.gc.choose(GoodyHutHelper);
			log("INFO", `=== FORCE COMPLETE ALL ${instances.length} INSTANCES ===`);

			let attempted = 0;
			let successful = 0;

			instances.forEach((instance, index) => {
				try {
					const stateInfo = getGoodyHutState(instance);
					log("INFO", `Force completing instance ${index}: ${stateInfo.state}`);

					attempted++;
					if (autoCompleteCollection(instance)) {
						successful++;
					}
				} catch (error) {
					log("ERROR", `Error force completing instance ${index}: ${error}`);
				}
			});

			log("INFO", `Force completion results: ${successful}/${attempted} successful`);
		} catch (error) {
			log("ERROR", `Error in forceCompleteAll: ${error}`);
		}
	}

	function showAllStates(): void {
		try {
			const instances = Il2Cpp.gc.choose(GoodyHutHelper);
			log("INFO", `=== ALL INSTANCE STATES (${instances.length} instances) ===`);

			instances.forEach((instance, index) => {
				try {
					const stateInfo = getGoodyHutState(instance);
					const info = getInstanceInfo(instance);
					log("INFO", `[${index}] ${stateInfo.state}: ${stateInfo.details} | ${info}`);
				} catch (error) {
					log("ERROR", `Error getting state for instance ${index}: ${error}`);
				}
			});
		} catch (error) {
			log("ERROR", `Error in showAllStates: ${error}`);
		}
	}

	// Configuration commands via console - Use Frida's global scope
	const goodyHutConfig = {
		enable: () => { CONFIG.AUTO_COMPLETE_ENABLED = true; log("INFO", "Auto-completion enabled"); },
		disable: () => { CONFIG.AUTO_COMPLETE_ENABLED = false; log("INFO", "Auto-completion disabled"); },
		verbose: (enabled: boolean) => { CONFIG.VERBOSE_LOGGING = enabled; log("INFO", `Verbose logging ${enabled ? 'enabled' : 'disabled'}`); },
		stats: () => printStats(),
		scan: () => scanAndAutoComplete(),
		debug: () => debugInstanceDetection(),
		test: (index: number = 0) => testAutoComplete(index),
		forceAll: () => forceCompleteAll(),
		states: () => showAllStates(),
		help: () => {
			log("INFO", "=== GoodyHut Config Commands ===");
			log("INFO", "goodyHutConfig.enable() - Enable auto-completion");
			log("INFO", "goodyHutConfig.disable() - Disable auto-completion");
			log("INFO", "goodyHutConfig.verbose(true/false) - Toggle verbose logging");
			log("INFO", "goodyHutConfig.stats() - Show statistics");
			log("INFO", "goodyHutConfig.scan() - Manual scan");
			log("INFO", "goodyHutConfig.debug() - Debug instance detection");
			log("INFO", "goodyHutConfig.test(index) - Test auto-complete on specific instance");
			log("INFO", "goodyHutConfig.forceAll() - Force complete all instances");
			log("INFO", "goodyHutConfig.states() - Show all instance states");
		}
	};

	// Make it available in Frida's global scope
	(globalThis as any).goodyHutConfig = goodyHutConfig;

	// Also make the class available for console access
	(globalThis as any).GoodyHutHelper = GoodyHutHelper;
	
	// Function to check if game is fully loaded
	function checkGameLoadStatus(): void {
		log("INFO", "Checking game load status...");

		try {
			// Check for common game classes that indicate the game is loaded
			const testClasses = [
				"GameManager", "SimulationManager", "EntityManager",
				"BuildingManager", "ResourceManager", "UIManager"
			];

			let foundClasses = 0;
			testClasses.forEach(className => {
				try {
					const testClass = AssemblyCSharp.class(className);
					if (testClass) {
						foundClasses++;
						log("DEBUG", `Found class: ${className}`);
					}
				} catch (e) {
					log("DEBUG", `Class not found: ${className}`);
				}
			});

			log("INFO", `Game load status: ${foundClasses}/${testClasses.length} core classes found`);

			if (foundClasses >= 2) {
				log("SUCCESS", "Game appears to be loaded - core classes accessible");
			} else {
				log("WARN", "Game may not be fully loaded - few core classes found");
			}
		} catch (error) {
			log("ERROR", `Error checking game load status: ${error}`);
		}
	}

	// Check game load status before starting
	setTimeout(() => {
		checkGameLoadStatus();
	}, 2000);

	log("SUCCESS", "Enhanced GoodyHutHelper auto-completion script fully initialized!");
	log("INFO", "=== Available Commands ===");
	log("INFO", "goodyHutConfig.help() - Show all available commands");
	log("INFO", "goodyHutConfig.states() - Show current state of all instances");
	log("INFO", "goodyHutConfig.test(0) - Test auto-complete on instance 0");
	log("INFO", "goodyHutConfig.forceAll() - Force complete all instances");
	log("INFO", "goodyHutConfig.verbose(true) - Enable detailed logging");
});
