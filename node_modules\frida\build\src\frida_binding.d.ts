export interface FridaBinding {
    commitConstructors(): void;
    DeviceManager: typeof DeviceManager;
    Device: typeof Device;
    _Device: typeof _Device;
    RemoteDeviceOptions: RemoteDeviceOptions;
    Application: typeof Application;
    Process: typeof Process;
    ProcessMatchOptions: ProcessMatchOptions;
    RawSpawnOptions: RawSpawnOptions;
    Spawn: typeof Spawn;
    Child: typeof Child;
    Crash: typeof Crash;
    Bus: typeof Bus;
    _Bus: typeof _Bus;
    Service: typeof Service;
    Session: typeof Session;
    Script: typeof Script;
    _Script: typeof _Script;
    PortalMembership: typeof PortalMembership;
    PackageManager: typeof PackageManager;
    Package: typeof Package;
    PackageSearchOptions: PackageSearchOptions;
    PackageSearchResult: typeof PackageSearchResult;
    PackageInstallOptions: PackageInstallOptions;
    PackageInstallResult: typeof PackageInstallResult;
    ControlService: typeof ControlService;
    ControlServiceOptions: ControlServiceOptions;
    PortalService: typeof PortalService;
    _PortalService: typeof _PortalService;
    FileMonitor: typeof FileMonitor;
    Compiler: typeof Compiler;
    CompilerOptions: CompilerOptions;
    BuildOptions: BuildOptions;
    WatchOptions: WatchOptions;
    StaticAuthenticationService: typeof StaticAuthenticationService;
    FrontmostQueryOptions: FrontmostQueryOptions;
    ApplicationQueryOptions: ApplicationQueryOptions;
    ProcessQueryOptions: ProcessQueryOptions;
    SessionOptions: SessionOptions;
    ScriptOptions: ScriptOptions;
    SnapshotOptions: SnapshotOptions;
    PortalOptions: PortalOptions;
    PeerOptions: PeerOptions;
    Relay: typeof Relay;
    _Relay: typeof _Relay;
    EndpointParameters: typeof EndpointParameters;
    _EndpointParameters: typeof _EndpointParameters;
    AbstractAuthenticationService: typeof AbstractAuthenticationService;
    BaseObject: typeof BaseObject;
    Cancellable: typeof Cancellable;
    _Cancellable: typeof _Cancellable;
    IOStream: typeof IOStream;
    InputStream: typeof InputStream;
    OutputStream: typeof OutputStream;
    InetSocketAddress: typeof InetSocketAddress;
    InetAddress: typeof InetAddress;
    UnixSocketAddress: typeof UnixSocketAddress;
    BaseSocketAddress: typeof BaseSocketAddress;
    SocketAddressEnumerator: typeof SocketAddressEnumerator;
    Runtime: typeof Runtime;
    DeviceType: typeof DeviceType;
    PackageInstallPhase: typeof PackageInstallPhase;
    PackageRole: typeof PackageRole;
    OutputFormat: typeof OutputFormat;
    BundleFormat: typeof BundleFormat;
    TypeCheckMode: typeof TypeCheckMode;
    SourceMaps: typeof SourceMaps;
    JsCompression: typeof JsCompression;
    GadgetBreakpointAction: typeof GadgetBreakpointAction;
    Realm: typeof Realm;
    SessionDetachReason: typeof SessionDetachReason;
    Scope: typeof Scope;
    Stdio: typeof Stdio;
    ChildOrigin: typeof ChildOrigin;
    SnapshotTransport: typeof SnapshotTransport;
    ScriptRuntime: typeof ScriptRuntime;
    RelayKind: typeof RelayKind;
    FileMonitorEvent: typeof FileMonitorEvent;
    SocketFamily: typeof SocketFamily;
    UnixSocketAddressType: typeof UnixSocketAddressType;
    MessageType: typeof MessageType;
    LogLevel: typeof LogLevel;
}

export class DeviceManager extends BaseObject {
    constructor();
    close(cancellable?: Cancellable | null): Promise<void>;
    getDeviceById(id: string, timeout: number, cancellable?: Cancellable | null): Promise<Device>;
    getDeviceByType(type: DeviceType, timeout: number, cancellable?: Cancellable | null): Promise<Device>;
    findDeviceById(id: string, timeout: number, cancellable?: Cancellable | null): Promise<Device | null>;
    findDeviceByType(type: DeviceType, timeout: number, cancellable?: Cancellable | null): Promise<Device | null>;
    enumerateDevices(cancellable?: Cancellable | null): Promise<Device[]>;
    addRemoteDevice(address: string, options?: RemoteDeviceOptions, cancellable?: Cancellable | null): Promise<Device>;
    removeRemoteDevice(address: string, cancellable?: Cancellable | null): Promise<void>;
    readonly added: Signal<DeviceAddedHandler>;
    readonly removed: Signal<DeviceRemovedHandler>;
    readonly changed: Signal<DeviceChangedHandler>;
}

export type DeviceAddedHandler = (device: Device) => void;
export type DeviceRemovedHandler = (device: Device) => void;
export type DeviceChangedHandler = () => void;

export class Device extends _Device {
    getProcess(name: string, options?: ProcessMatchOptions, cancellable?: Cancellable | null): Promise<Process>;
    querySystemParameters(cancellable?: Cancellable | null): Promise<SystemParameters>;
    spawn(programOrArgv: string | string[], opts?: SpawnOptions, cancellable?: Cancellable | null): Promise<ProcessID>;
    input(target: TargetProcess, data: Buffer, cancellable?: Cancellable | null): Promise<void>;
    resume(target: TargetProcess, cancellable?: Cancellable | null): Promise<void>;
    kill(target: TargetProcess, cancellable?: Cancellable | null): Promise<void>;
    attach(target: TargetProcess, options?: SessionOptions, cancellable?: Cancellable | null): Promise<Session>;
    injectLibraryFile(target: TargetProcess, path: string, entrypoint: string, data: string, cancellable?: Cancellable | null): Promise<InjecteeID>;
    injectLibraryBlob(target: TargetProcess, blob: Buffer, entrypoint: string, data: string, cancellable?: Cancellable | null): Promise<InjecteeID>;
    openChannel(address: string, cancellable?: Cancellable | null): Promise<import("stream").Duplex>;
    readonly output: Signal<OutputHandler>;
    readonly uninjected: Signal<UninjectedHandler>;
}

export type OutputHandler = (pid: ProcessID, fd: FileDescriptor, data: Buffer) => void;
export type UninjectedHandler = (id: InjecteeID) => void;

export class _Device extends BaseObject {
    isLost(): boolean;
    protected _querySystemParameters(cancellable?: Cancellable | null): Promise<VariantDict>;
    getFrontmostApplication(options?: FrontmostQueryOptions, cancellable?: Cancellable | null): Promise<Application | null>;
    enumerateApplications(options?: ApplicationQueryOptions, cancellable?: Cancellable | null): Promise<Application[]>;
    getProcessByPid(pid: number, options?: ProcessMatchOptions, cancellable?: Cancellable | null): Promise<Process>;
    getProcessByName(name: string, options?: ProcessMatchOptions, cancellable?: Cancellable | null): Promise<Process>;
    findProcessByPid(pid: number, options?: ProcessMatchOptions, cancellable?: Cancellable | null): Promise<Process | null>;
    findProcessByName(name: string, options?: ProcessMatchOptions, cancellable?: Cancellable | null): Promise<Process | null>;
    enumerateProcesses(options?: ProcessQueryOptions, cancellable?: Cancellable | null): Promise<Process[]>;
    enableSpawnGating(cancellable?: Cancellable | null): Promise<void>;
    disableSpawnGating(cancellable?: Cancellable | null): Promise<void>;
    enumeratePendingSpawn(cancellable?: Cancellable | null): Promise<Spawn[]>;
    enumeratePendingChildren(cancellable?: Cancellable | null): Promise<Child[]>;
    protected _spawn(program: string, options?: RawSpawnOptions, cancellable?: Cancellable | null): Promise<number>;
    protected _input(pid: number, data: Buffer, cancellable?: Cancellable | null): Promise<void>;
    protected _resume(pid: number, cancellable?: Cancellable | null): Promise<void>;
    protected _kill(pid: number, cancellable?: Cancellable | null): Promise<void>;
    protected _attach(pid: number, options?: SessionOptions, cancellable?: Cancellable | null): Promise<Session>;
    protected _injectLibraryFile(pid: number, path: string, entrypoint: string, data: string, cancellable?: Cancellable | null): Promise<number>;
    protected _injectLibraryBlob(pid: number, blob: Buffer, entrypoint: string, data: string, cancellable?: Cancellable | null): Promise<number>;
    protected _openChannel(address: string, cancellable?: Cancellable | null): Promise<IOStream>;
    openService(address: string, cancellable?: Cancellable | null): Promise<Service>;
    unpair(cancellable?: Cancellable | null): Promise<void>;
    readonly id: string;
    readonly name: string;
    icon: any;
    readonly type: DeviceType;
    readonly bus: Bus;
    readonly spawnAdded: Signal<SpawnAddedHandler>;
    readonly spawnRemoved: Signal<SpawnRemovedHandler>;
    readonly childAdded: Signal<ChildAddedHandler>;
    readonly childRemoved: Signal<ChildRemovedHandler>;
    readonly processCrashed: Signal<ProcessCrashedHandler>;
    protected readonly _output: Signal<_OutputHandler>;
    protected readonly _uninjected: Signal<_UninjectedHandler>;
    readonly lost: Signal<DeviceLostHandler>;
}

export type SpawnAddedHandler = (spawn: Spawn) => void;
export type SpawnRemovedHandler = (spawn: Spawn) => void;
export type ChildAddedHandler = (child: Child) => void;
export type ChildRemovedHandler = (child: Child) => void;
export type ProcessCrashedHandler = (crash: Crash) => void;
export type _OutputHandler = (pid: number, fd: number, data: Buffer) => void;
export type _UninjectedHandler = (id: number) => void;
export type DeviceLostHandler = () => void;

export interface RemoteDeviceOptions {
    certificate?: string;
    origin?: string;
    token?: string;
    keepaliveInterval?: number;
}

export class Application extends BaseObject {
    identifier: string;
    name: string;
    pid: number;
    parameters: VariantDict;
}

export class Process extends BaseObject {
    pid: number;
    name: string;
    parameters: VariantDict;
}

export interface ProcessMatchOptions {
    timeout?: number;
    scope?: Scope;
}

export interface RawSpawnOptions {
    argv?: string[];
    envp?: string[];
    env?: string[];
    cwd?: string;
    stdio?: Stdio;
    aux?: VariantDict;
}

export class Spawn extends BaseObject {
    pid: number;
    identifier: string;
}

export class Child extends BaseObject {
    pid: number;
    parentPid: number;
    origin: ChildOrigin;
    identifier: string;
    path: string;
    argv: string[];
    envp: string[];
}

export class Crash extends BaseObject {
    pid: number;
    processName: string;
    summary: string;
    report: string;
    parameters: VariantDict;
}

export class Bus extends _Bus {
    post(message: any, data?: Buffer | null): void;
    readonly message: Signal<BusMessageHandler>;
}

export type BusMessageHandler = (message: any, data: Buffer | null) => void;

export class _Bus extends BaseObject {
    isDetached(): boolean;
    attach(cancellable?: Cancellable | null): Promise<void>;
    protected _post(json: string, data?: Buffer | null): void;
    readonly detached: Signal<BusDetachedHandler>;
    protected readonly _message: Signal<_BusMessageHandler>;
}

export type BusDetachedHandler = () => void;
export type _BusMessageHandler = (json: string, data: Buffer | null) => void;

export class Service extends BaseObject {
    isClosed(): boolean;
    activate(cancellable?: Cancellable | null): Promise<void>;
    cancel(cancellable?: Cancellable | null): Promise<void>;
    request(parameters: any, cancellable?: Cancellable | null): Promise<any>;
    readonly close: Signal<ServiceCloseHandler>;
    readonly message: Signal<ServiceMessageHandler>;
}

export type ServiceCloseHandler = () => void;
export type ServiceMessageHandler = (message: any) => void;

export class Session extends BaseObject {
    isDetached(): boolean;
    detach(cancellable?: Cancellable | null): Promise<void>;
    resume(cancellable?: Cancellable | null): Promise<void>;
    enableChildGating(cancellable?: Cancellable | null): Promise<void>;
    disableChildGating(cancellable?: Cancellable | null): Promise<void>;
    createScript(source: string, options?: ScriptOptions, cancellable?: Cancellable | null): Promise<Script>;
    createScriptFromBytes(bytes: Buffer, options?: ScriptOptions, cancellable?: Cancellable | null): Promise<Script>;
    compileScript(source: string, options?: ScriptOptions, cancellable?: Cancellable | null): Promise<Buffer>;
    snapshotScript(embedScript: string, options?: SnapshotOptions, cancellable?: Cancellable | null): Promise<Buffer>;
    setupPeerConnection(options?: PeerOptions, cancellable?: Cancellable | null): Promise<void>;
    joinPortal(address: string, options?: PortalOptions, cancellable?: Cancellable | null): Promise<PortalMembership>;
    pid: number;
    persistTimeout: number;
    readonly detached: Signal<SessionDetachedHandler>;
}

export type SessionDetachedHandler = (reason: SessionDetachReason, crash: Crash | null) => void;

export class Script extends _Script {
    logHandler: ScriptLogHandler;
    readonly isDestroyed: boolean;
    readonly exports: ScriptExports;
    readonly defaultLogHandler: ScriptLogHandler;
    post(message: any, data?: Buffer | null): void;
    enableDebugger(options?: EnableDebuggerOptions, cancellable?: Cancellable | null): Promise<void>;
    readonly message: Signal<ScriptMessageHandler>;
}

export type ScriptMessageHandler = (message: Message, data: Buffer | null) => void;

export class _Script extends BaseObject {
    protected _isDestroyed(): boolean;
    load(cancellable?: Cancellable | null): Promise<void>;
    unload(cancellable?: Cancellable | null): Promise<void>;
    eternalize(cancellable?: Cancellable | null): Promise<void>;
    protected _post(json: string, data?: Buffer | null): void;
    protected _enableDebugger(port: number, cancellable?: Cancellable | null): Promise<void>;
    disableDebugger(cancellable?: Cancellable | null): Promise<void>;
    readonly destroyed: Signal<ScriptDestroyedHandler>;
    protected readonly _message: Signal<_ScriptMessageHandler>;
}

export type ScriptDestroyedHandler = () => void;
export type _ScriptMessageHandler = (json: string, data: Buffer | null) => void;

export class PortalMembership extends BaseObject {
    terminate(cancellable?: Cancellable | null): Promise<void>;
}

export class PackageManager extends BaseObject {
    constructor();
    search(query: string, options?: PackageSearchOptions, cancellable?: Cancellable | null): Promise<PackageSearchResult>;
    install(options?: PackageInstallOptions, cancellable?: Cancellable | null): Promise<PackageInstallResult>;
    registry: string;
    readonly installProgress: Signal<PackageManagerInstallProgressHandler>;
}

export type PackageManagerInstallProgressHandler = (phase: PackageInstallPhase, fraction: number, details: string | null) => void;

export class Package extends BaseObject {
    name: string;
    version: string;
    description: string;
    url: string;
}

export interface PackageSearchOptions {
    offset?: number;
    limit?: number;
}

export class PackageSearchResult extends BaseObject {
    packages: Package[];
    total: number;
}

export interface PackageInstallOptions {
    specs?: string[];
    omits?: PackageRole[];
    projectRoot?: string;
    role?: PackageRole;
}

export class PackageInstallResult extends BaseObject {
    packages: Package[];
}

export class ControlService extends BaseObject {
    constructor(endpointParams: EndpointParameters, options?: ControlServiceOptions);
    start(cancellable?: Cancellable | null): Promise<void>;
    stop(cancellable?: Cancellable | null): Promise<void>;
}

export interface ControlServiceOptions {
    sysroot?: string;
    enablePreload?: boolean;
    reportCrashes?: boolean;
}

export class PortalService extends _PortalService {
    constructor(options?: PortalServiceOptions);
    post(connectionId: PortalConnectionId, message: any, data?: Buffer | null): void;
    narrowcast(tag: string, message: any, data?: Buffer | null): void;
    broadcast(message: any, data?: Buffer | null): void;
    readonly nodeConnected: Signal<PortalNodeConnectedHandler>;
    readonly nodeJoined: Signal<PortalNodeJoinedHandler>;
    readonly nodeLeft: Signal<PortalNodeLeftHandler>;
    readonly nodeDisconnected: Signal<PortalNodeDisconnectedHandler>;
    readonly controllerConnected: Signal<PortalControllerConnectedHandler>;
    readonly controllerDisconnected: Signal<PortalControllerDisconnectedHandler>;
    readonly authenticated: Signal<PortalAuthenticatedHandler>;
    readonly subscribe: Signal<PortalSubscribeHandler>;
    readonly message: Signal<PortalMessageHandler>;
}

export type PortalNodeConnectedHandler = (connectionId: PortalConnectionId, remoteAddress: SocketAddress) => void;
export type PortalNodeJoinedHandler = (connectionId: PortalConnectionId, application: Application) => void;
export type PortalNodeLeftHandler = (connectionId: PortalConnectionId, application: Application) => void;
export type PortalNodeDisconnectedHandler = (connectionId: PortalConnectionId, remoteAddress: SocketAddress) => void;
export type PortalControllerConnectedHandler = (connectionId: PortalConnectionId, remoteAddress: SocketAddress) => void;
export type PortalControllerDisconnectedHandler = (connectionId: PortalConnectionId, remoteAddress: SocketAddress) => void;
export type PortalAuthenticatedHandler = (connectionId: PortalConnectionId, sessionInfo: AuthenticatedSessionInfo) => void;
export type PortalSubscribeHandler = (connectionId: PortalConnectionId) => void;
export type PortalMessageHandler = (connectionId: PortalConnectionId, message: any, data: Buffer | null) => void;

export class _PortalService extends BaseObject {
    constructor(clusterParams: EndpointParameters, controlParams?: EndpointParameters | null);
    start(cancellable?: Cancellable | null): Promise<void>;
    stop(cancellable?: Cancellable | null): Promise<void>;
    kick(connectionId: number): void;
    protected _post(connectionId: number, json: string, data?: Buffer | null): void;
    protected _narrowcast(tag: string, json: string, data?: Buffer | null): void;
    protected _broadcast(json: string, data?: Buffer | null): void;
    enumerateTags(connectionId: number): string[] | null;
    tag(connectionId: number, tag: string): void;
    untag(connectionId: number, tag: string): void;
    readonly device: Device;
    clusterParams: EndpointParameters;
    controlParams: EndpointParameters;
    protected readonly _nodeConnected: Signal<_PortalNodeConnectedHandler>;
    protected readonly _nodeJoined: Signal<_PortalNodeJoinedHandler>;
    protected readonly _nodeLeft: Signal<_PortalNodeLeftHandler>;
    protected readonly _nodeDisconnected: Signal<_PortalNodeDisconnectedHandler>;
    protected readonly _controllerConnected: Signal<_PortalControllerConnectedHandler>;
    protected readonly _controllerDisconnected: Signal<_PortalControllerDisconnectedHandler>;
    protected readonly _authenticated: Signal<_PortalAuthenticatedHandler>;
    protected readonly _subscribe: Signal<_PortalSubscribeHandler>;
    protected readonly _message: Signal<_PortalMessageHandler>;
}

export type _PortalNodeConnectedHandler = (connectionId: number, remoteAddress: BaseSocketAddress) => void;
export type _PortalNodeJoinedHandler = (connectionId: number, application: Application) => void;
export type _PortalNodeLeftHandler = (connectionId: number, application: Application) => void;
export type _PortalNodeDisconnectedHandler = (connectionId: number, remoteAddress: BaseSocketAddress) => void;
export type _PortalControllerConnectedHandler = (connectionId: number, remoteAddress: BaseSocketAddress) => void;
export type _PortalControllerDisconnectedHandler = (connectionId: number, remoteAddress: BaseSocketAddress) => void;
export type _PortalAuthenticatedHandler = (connectionId: number, sessionInfo: string) => void;
export type _PortalSubscribeHandler = (connectionId: number) => void;
export type _PortalMessageHandler = (connectionId: number, json: string, data: Buffer | null) => void;

export class FileMonitor extends BaseObject {
    constructor(path: string);
    enable(cancellable?: Cancellable | null): Promise<void>;
    disable(cancellable?: Cancellable | null): Promise<void>;
    path: string;
    readonly change: Signal<FileMonitorChangeHandler>;
}

export type FileMonitorChangeHandler = (filePath: string, otherFilePath: string | null, event: FileMonitorEvent) => void;

export class Compiler extends BaseObject {
    constructor(manager?: DeviceManager | null);
    build(entrypoint: string, options?: BuildOptions, cancellable?: Cancellable | null): Promise<string>;
    watch(entrypoint: string, options?: WatchOptions, cancellable?: Cancellable | null): Promise<void>;
    readonly starting: Signal<CompilerStartingHandler>;
    readonly finished: Signal<CompilerFinishedHandler>;
    readonly output: Signal<CompilerOutputHandler>;
    readonly diagnostics: Signal<CompilerDiagnosticsHandler>;
}

export type CompilerStartingHandler = () => void;
export type CompilerFinishedHandler = () => void;
export type CompilerOutputHandler = (bundle: string) => void;
export type CompilerDiagnosticsHandler = (diagnostics: any) => void;

export interface CompilerOptions {
    projectRoot?: string;
    outputFormat?: OutputFormat;
    bundleFormat?: BundleFormat;
    typeCheck?: TypeCheckMode;
    sourceMaps?: SourceMaps;
    compression?: JsCompression;
}

export interface BuildOptions {
}

export interface WatchOptions {
}

export class StaticAuthenticationService extends BaseObject implements AuthenticationService {
    constructor(token: string);
    tokenHash: string;
    authenticate(token: string, cancellable?: Cancellable | null): Promise<string>;
}

export interface FrontmostQueryOptions {
    scope?: Scope;
}

export interface ApplicationQueryOptions {
    identifiers?: string[];
    scope?: Scope;
}

export interface ProcessQueryOptions {
    pids?: number[];
    scope?: Scope;
}

export interface SessionOptions {
    realm?: Realm;
    persistTimeout?: number;
    emulatedAgentPath?: string;
}

export interface ScriptOptions {
    name?: string;
    snapshot?: Buffer;
    snapshotTransport?: SnapshotTransport;
    runtime?: ScriptRuntime;
}

export interface SnapshotOptions {
    warmupScript?: string;
    runtime?: ScriptRuntime;
}

export interface PortalOptions {
    certificate?: string;
    token?: string;
    acl?: string[];
}

export interface PeerOptions {
    relays?: Relay[];
    stunServer?: string;
}

export class Relay extends _Relay {
    constructor(properties: RelayProperties);
}

export class _Relay extends BaseObject {
    constructor(address: string, username: string, password: string, kind: RelayKind);
    address: string;
    username: string;
    password: string;
    kind: RelayKind;
}

export class EndpointParameters extends _EndpointParameters {
    constructor(params?: EndpointParametersSubset);
}

export class _EndpointParameters extends BaseObject {
    constructor(address: string | null, port: number, certificate?: string | null, origin?: string | null, authService?: AuthenticationService | null, assetRoot?: string | null);
    address: string;
    port: number;
    certificate: string;
    origin: string;
    authService: AuthenticationService;
    assetRoot: string;
}

export interface AuthenticationService extends BaseObject {
    authenticate(token: string, cancellable?: Cancellable | null): Promise<string>;
}

export abstract class AbstractAuthenticationService extends BaseObject implements AuthenticationService {
    authenticate(token: string, cancellable: Cancellable | null): Promise<string>;
}

export class BaseObject {
    notify(propertyName: string): void;
}

export class Cancellable extends _Cancellable {
    readonly isCancelled: boolean;
}

export class _Cancellable extends BaseObject {
    constructor();
    cancel(): void;
    disconnect(handlerId: number): void;
    getFd(): number;
    protected _isCancelled(): boolean;
    popCurrent(): void;
    pushCurrent(): void;
    reset(): void;
    throwIfCancelled(): boolean;
    readonly cancelled: Signal<CancelledHandler>;
}

export type CancelledHandler = () => void;

export class IOStream extends BaseObject {
    close(ioPriority: number, cancellable?: Cancellable | null): Promise<boolean>;
    readonly closed: boolean;
    readonly inputStream: InputStream;
    readonly outputStream: OutputStream;
}

export class InputStream extends BaseObject {
    close(ioPriority: number, cancellable?: Cancellable | null): Promise<boolean>;
    read(count: number, ioPriority: number, cancellable?: Cancellable | null): Promise<Buffer>;
    skip(count: number, ioPriority: number, cancellable?: Cancellable | null): Promise<number>;
}

export class OutputStream extends BaseObject {
    close(ioPriority: number, cancellable?: Cancellable | null): Promise<boolean>;
    flush(ioPriority: number, cancellable?: Cancellable | null): Promise<boolean>;
    write(bytes: Buffer, ioPriority: number, cancellable?: Cancellable | null): Promise<number>;
}

export class InetSocketAddress extends BaseSocketAddress implements SocketConnectable {
    constructor(address: InetAddress, port: number);
    address: InetAddress;
    flowinfo: number;
    port: number;
    scopeId: number;
    enumerate(): SocketAddressEnumerator;
    proxyEnumerate(): SocketAddressEnumerator;
    toString(): string;
}

export class InetAddress extends BaseObject {
    constructor(family: SocketFamily);
    equal(otherAddress: InetAddress): boolean;
    getNativeSize(): number;
    toString(): string;
    family: SocketFamily;
    readonly isAny: boolean;
    readonly isLinkLocal: boolean;
    readonly isLoopback: boolean;
    readonly isMcGlobal: boolean;
    readonly isMcLinkLocal: boolean;
    readonly isMcNodeLocal: boolean;
    readonly isMcOrgLocal: boolean;
    readonly isMcSiteLocal: boolean;
    readonly isMulticast: boolean;
    readonly isSiteLocal: boolean;
}

export class UnixSocketAddress extends BaseSocketAddress implements SocketConnectable {
    constructor(path: string);
    addressType: UnixSocketAddressType;
    path: Buffer;
    enumerate(): SocketAddressEnumerator;
    proxyEnumerate(): SocketAddressEnumerator;
    toString(): string;
}

export class BaseSocketAddress extends BaseObject implements SocketConnectable {
    getNativeSize(): number;
    readonly family: SocketFamily;
    enumerate(): SocketAddressEnumerator;
    proxyEnumerate(): SocketAddressEnumerator;
    toString(): string;
}

export class SocketAddressEnumerator extends BaseObject {
    next(cancellable?: Cancellable | null): BaseSocketAddress | null;
    nextAsync(cancellable?: Cancellable | null): Promise<BaseSocketAddress | null>;
}

export interface SocketConnectable {
    enumerate(): SocketAddressEnumerator;
    proxyEnumerate(): SocketAddressEnumerator;
    toString(): string;
}

export enum Runtime {
    Glib = "glib",
    Other = "other"
}

export enum DeviceType {
    Local = "local",
    Remote = "remote",
    Usb = "usb"
}

export enum PackageInstallPhase {
    Initializing = "initializing",
    PreparingDependencies = "preparing-dependencies",
    ResolvingPackage = "resolving-package",
    FetchingResource = "fetching-resource",
    PackageAlreadyInstalled = "package-already-installed",
    DownloadingPackage = "downloading-package",
    PackageInstalled = "package-installed",
    ResolvingAndInstallingAll = "resolving-and-installing-all",
    Complete = "complete"
}

export enum PackageRole {
    Runtime = "runtime",
    Development = "development",
    Optional = "optional",
    Peer = "peer"
}

export enum OutputFormat {
    Unescaped = "unescaped",
    HexBytes = "hex-bytes",
    CString = "c-string"
}

export enum BundleFormat {
    Esm = "esm",
    Iife = "iife"
}

export enum TypeCheckMode {
    Full = "full",
    None = "none"
}

export enum SourceMaps {
    Included = "included",
    Omitted = "omitted"
}

export enum JsCompression {
    None = "none",
    Terser = "terser"
}

export enum GadgetBreakpointAction {
    InvokeReturn = "invoke-return",
    Resume = "resume",
    Detach = "detach",
    PagePlan = "page-plan"
}

export enum Realm {
    Native = "native",
    Emulated = "emulated"
}

export enum SessionDetachReason {
    ApplicationRequested = "application-requested",
    ProcessReplaced = "process-replaced",
    ProcessTerminated = "process-terminated",
    ConnectionTerminated = "connection-terminated",
    DeviceLost = "device-lost"
}

export enum Scope {
    Minimal = "minimal",
    Metadata = "metadata",
    Full = "full"
}

export enum Stdio {
    Inherit = "inherit",
    Pipe = "pipe"
}

export enum ChildOrigin {
    Fork = "fork",
    Exec = "exec",
    Spawn = "spawn"
}

export enum SnapshotTransport {
    Inline = "inline",
    SharedMemory = "shared-memory"
}

export enum ScriptRuntime {
    Default = "default",
    QJS = "qjs",
    V8 = "v8"
}

export enum RelayKind {
    TurnUDP = "turn-udp",
    TurnTCP = "turn-tcp",
    TurnTLS = "turn-tls"
}

export enum FileMonitorEvent {
    Changed = "changed",
    ChangesDoneHint = "changes-done-hint",
    Deleted = "deleted",
    Created = "created",
    AttributeChanged = "attribute-changed",
    PreUnmount = "pre-unmount",
    Unmounted = "unmounted",
    Moved = "moved",
    Renamed = "renamed",
    MovedIn = "moved-in",
    MovedOut = "moved-out"
}

export enum SocketFamily {
    Invalid = "invalid",
    Unix = "unix",
    Ipv4 = "ipv4",
    Ipv6 = "ipv6"
}

export enum UnixSocketAddressType {
    Invalid = "invalid",
    Anonymous = "anonymous",
    Path = "path",
    Abstract = "abstract",
    AbstractPadded = "abstract-padded"
}

export type TargetProcess = ProcessID | ProcessName;

export type ProcessID = number;

export type InjecteeID = number;

export type FileDescriptor = number;

export type ProcessName = string;

export interface SystemParameters {
    /**
     * Operating System details.
     */
    os: {
        /**
         * ID, e.g.: windows, macos, linux, ios, android, qnx, fedora, ubuntu, etc.
         */
        id: string;

        /**
         * Human-readable name, e.g. `"macOS"`.
         */
        name: string;

        /**
         * Human-readable version string, e.g. `"11.2.2"`.
         */
        version?: string;

        /**
         * Build version, e.g. `"21B91"`.
         */
        build?: string;
    }

    /**
     * Platform, same as `Process.platform` in GumJS.
     */
    platform: "windows" | "darwin" | "linux" | "freebsd" | "qnx";

    /**
     * Architecture, same as `Process.arch` in GumJS.
     */
    arch: "ia32" | "x64" | "arm" | "arm64" | "mips";

    /**
     * Hardware details.
     */
    hardware?: {
        /**
         * Product type, e.g. `"iPad6,3"`.
         */
        product?: string;

        /**
         * Hardware platform, e.g. `"t8010"`.
         */
        platform?: string;

        /**
         * Hardware model, e.g. `"J71bAP"`.
         */
        model?: string;
    }

    /**
     * Level of access.
     */
    access: "full" | "jailed";

    /**
     * System name, e.g. `"Ole André’s iPhone"`.
     */
    name?: string;

    /**
     * iOS UDID (Unique Device ID).
     */
    udid?: string;

    /**
     * Details about cellular and networking interfaces.
     */
    interfaces?: SystemInterface[];

    /**
     * Android API level, e.g.: `30`.
     */
    apiLevel?: number;

    [name: string]: any;
}

export type SystemInterface = NetworkInterface | CellularInterface;

export interface NetworkInterface {
    type: "ethernet" | "wifi" | "bluetooth";

    /**
     * MAC address, e.g.: `"aa:bb:cc:dd:ee:ff"`.
     */
    address: string;
}

export interface CellularInterface {
    type: "cellular";

    /**
     * Phone number, e.g. `"+47 123 45 678"`.
     */
    phoneNumber: string;
}

export interface SpawnOptions {
    argv?: string[];
    envp?: { [name: string]: string };
    env?: { [name: string]: string };
    cwd?: string;
    stdio?: Stdio;

    [name: string]: any;
}

export interface RelayProperties {
    address: string;
    username: string;
    password: string;
    kind: RelayKind;
}

export type Message = SendMessage | ErrorMessage;

export enum MessageType {
    Send = "send",
    Error = "error"
}

export interface SendMessage {
    type: MessageType.Send;
    payload: any;
}

export interface ErrorMessage {
    type: MessageType.Error;
    description: string;
    stack?: string;
    fileName?: string;
    lineNumber?: number;
    columnNumber?: number;
}

export type ScriptLogHandler = (level: LogLevel, text: string) => void;

export interface ScriptExports {
    [name: string]: (...args: any[]) => Promise<any>;
}

export enum LogLevel {
    Info = "info",
    Warning = "warning",
    Error = "error",
}

export interface EnableDebuggerOptions {
    port?: number;
}

export interface PortalServiceOptions {
    clusterParams?: EndpointParameters;
    controlParams?: EndpointParameters;
}

export type PortalConnectionId = number;

export type PortalConnectionTag = string;

export interface EndpointParametersSubset {
    address?: string;
    port?: number;
    certificate?: string;
    origin?: string;
    authentication?: AuthenticationScheme;
    assetRoot?: string;
}

export type AuthenticationScheme = TokenAuthenticationScheme | CallbackAuthenticationScheme;

export interface TokenAuthenticationScheme {
    scheme: "token";
    token: string;
}

export interface CallbackAuthenticationScheme {
    scheme: "callback";
    callback: AuthenticationCallback;
}

export type AuthenticationCallback = (token: string) => Promise<AuthenticatedSessionInfo>;

export interface AuthenticatedSessionInfo {
    [key: string]: any;
}

export type SocketAddress = IPV4SocketAddress | IPV6SocketAddress | AnonymousUnixSocketAddress | PathUnixSocketAddress | AbstractUnixSocketAddress;

export interface IPV4SocketAddress {
    family: "ipv4";
    address: string;
    port: number;
}

export interface IPV6SocketAddress {
    family: "ipv6";
    address: string;
    port: number;
    flowlabel: number;
    scopeid: number;
}

export interface AnonymousUnixSocketAddress {
    family: "unix:anonymous";
}

export interface PathUnixSocketAddress {
    family: "unix:path";
    path: string;
}

export interface AbstractUnixSocketAddress {
    family: "unix:abstract";
    path: Buffer;
}

export type Variant = VariantValue | [type: symbol, value: VariantValue];

export type VariantValue = | boolean
| number
| string
| Buffer
| Variant[]
| VariantDict;

export interface VariantDict {
    [key: string]: Variant;
}

export class Signal<H extends SignalHandler> {
    connect(handler: H): void;
    disconnect(handler: H): void;
}

export type SignalHandler = (...args: any[]) => void;
