# GoodyHut Auto-Complete Script Troubleshooting Guide

## Fixed Issues in Latest Version

### ✅ Issue 1: JavaScript Runtime Error (FIXED)
**Problem**: `ReferenceError: global is not defined`
**Solution**: Replaced `global` with `globalThis` which is supported in Frida's JavaScript runtime.

### ✅ Issue 2: Instance Detection Problem (IMPROVED)
**Problem**: "No GoodyHutHelper instances found" even when goody huts exist
**Solution**: Added multiple detection methods and improved debugging.

## Current Debugging Features

### 1. Enhanced Instance Detection
The script now uses multiple approaches to find goody hut instances:

1. **Direct Detection**: `Il2Cpp.gc.choose(GoodyHutHelper)`
2. **Indirect Detection**: Via `EntityController` instances with `m_goodyHut` field
3. **Game Load Verification**: Checks if core game classes are loaded

### 2. Debug Commands
Use these commands in the Frida console:

```javascript
// Run comprehensive debug check
goodyHutConfig.debug()

// Manual scan for instances
goodyHutConfig.scan()

// Enable verbose logging
goodyHutConfig.verbose(true)

// Show current statistics
goodyHutConfig.stats()
```

### 3. Improved Timing
- **Game Load Check**: 2 seconds after script start
- **Initial Debug**: 3 seconds after script start  
- **Initial Scan**: 5 seconds after script start
- **Periodic Scans**: Every 2 seconds

## Debugging Steps

### Step 1: Verify Script Loading
Look for these messages in console:
```
[timestamp] SUCCESS Enhanced GoodyHutHelper Auto-Complete Script Loaded
[timestamp] INFO GoodyHutHelper class found: 0x...
[timestamp] SUCCESS StartCollect hook installed successfully!
```

### Step 2: Check Game Load Status
After 2 seconds, you should see:
```
[timestamp] INFO Checking game load status...
[timestamp] SUCCESS Game appears to be loaded - core classes accessible
```

If you see warnings about few classes found, the game may not be fully loaded.

### Step 3: Run Debug Check
Execute in Frida console:
```javascript
goodyHutConfig.debug()
```

Expected output:
```
[timestamp] DEBUG === Instance Detection Debug ===
[timestamp] DEBUG Attempting Il2Cpp.gc.choose...
[timestamp] DEBUG Il2Cpp.gc.choose found X instances
[timestamp] DEBUG Found Y EntityController instances
[timestamp] DEBUG Found Z entities with GoodyHutHelper components
```

### Step 4: Verify Goody Huts Exist in Game
1. **In-Game Check**: Ensure you have goody huts visible on your base
2. **Start Collection**: Try starting a collection manually
3. **Check Hook**: Look for "StartCollect called" message

## Common Scenarios and Solutions

### Scenario 1: No Instances Found
**Possible Causes**:
- Game not fully loaded
- No goody huts spawned in current base
- Goody huts already collected/destroyed

**Solutions**:
1. Wait longer for game to load (try after 10+ seconds)
2. Switch to a base with goody huts
3. Wait for goody huts to respawn
4. Use `goodyHutConfig.debug()` to investigate

### Scenario 2: Instances Found But Not Collecting
**Expected Output**:
```
[timestamp] INFO Scanning X GoodyHutHelper instances...
[timestamp] DEBUG Scanned X instances, none currently collecting
```

**Solutions**:
1. Start a goody hut collection manually
2. Check if collections complete too quickly to detect
3. Enable verbose logging: `goodyHutConfig.verbose(true)`

### Scenario 3: Hook Not Triggering
**Symptoms**: No "StartCollect called" message when starting collection

**Solutions**:
1. Verify method exists: Check debug output for "Method StartCollect: Found"
2. Game version compatibility: Method signature may have changed
3. Try alternative method names in debug output

### Scenario 4: DoJobBuyThrough Fails
**Expected Messages**:
```
[timestamp] WARN Cannot use buy-through on this instance
[timestamp] WARN DoJobBuyThrough call failed
```

**Causes**:
- Insufficient premium currency
- Goody hut doesn't support instant completion
- Collection already completed

## Advanced Debugging

### Enable Maximum Logging
```javascript
goodyHutConfig.verbose(true)
```

### Check Method Availability
The debug function will show:
```
Method StartCollect: Found/Not found
Method CanCollect: Found/Not found  
Method IsJobComplete: Found/Not found
Method DoJobBuyThrough: Found/Not found
```

### Monitor EntityController Approach
If direct detection fails, the script tries finding goody huts via EntityController:
```
[timestamp] DEBUG No direct GoodyHutHelper instances, but found X EntityController instances
[timestamp] DEBUG Found GoodyHutHelper via EntityController Y
```

## Game State Requirements

### For Instance Detection to Work:
1. ✅ Game fully loaded (core classes accessible)
2. ✅ Base with goody huts loaded
3. ✅ Goody huts not destroyed/collected
4. ✅ IL2CPP bridge functioning

### For Auto-Completion to Work:
1. ✅ Instance detection working
2. ✅ Goody hut collection started
3. ✅ Premium currency available (game requirement)
4. ✅ Goody hut supports instant completion

## Performance Monitoring

### Statistics Output (every 30 seconds):
```
[timestamp] STATS Runtime: 45s | Attempts: 3 | Success: 3 | Failed: 0 | Rate: 100%
```

### Key Metrics:
- **Attempts**: Total auto-completion attempts
- **Success**: Successful completions
- **Failed**: Failed attempts
- **Rate**: Success percentage

## When to Contact for Help

If after following all debugging steps you still see:
1. No instances found despite goody huts being visible
2. Methods not found in debug output
3. Consistent 0% success rate
4. Script crashes or stops responding

Provide this information:
- Full console output from script start
- Output from `goodyHutConfig.debug()`
- Game version/build number
- Device/platform information
- Screenshots of in-game goody huts

## Quick Test Procedure

1. **Load Script**: `npm run spawn-enhanced`
2. **Wait 10 seconds**: Let game fully load
3. **Run Debug**: `goodyHutConfig.debug()`
4. **Start Collection**: Manually start goody hut collection
5. **Check Results**: Look for auto-completion messages

Expected successful flow:
```
[timestamp] INFO StartCollect called - triggering original method first
[timestamp] INFO Attempting auto-completion after StartCollect...
[timestamp] SUCCESS Successfully auto-completed goody hut collection!
```
