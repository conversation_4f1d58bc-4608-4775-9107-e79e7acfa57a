import type { DeviceManager as _<PERSON>ce<PERSON>anager, Device<PERSON>ddedHandler as _DeviceAddedHandler, <PERSON><PERSON><PERSON><PERSON>ovedHandler as _DeviceRemovedHandler, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>andler as _Device<PERSON>hang<PERSON>Handler, <PERSON>ce as _<PERSON>ce, SpawnAddedHandler as _SpawnAddedHandler, SpawnRemovedHandler as _SpawnRemovedHandler, ChildAdded<PERSON>and<PERSON> as _ChildAddedHandler, ChildRemovedHand<PERSON> as _ChildRemovedHandler, ProcessCrashedHandler as _ProcessCrashedHandler, OutputHandler as _OutputHandler, UninjectedHandler as _UninjectedHandler, <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> as _DeviceLostHandler, RemoteDeviceOptions as _RemoteDeviceOptions, Application as _Application, Process as _Process, ProcessMatchOptions as _ProcessMatchOptions, RawSpawnOptions as _RawSpawnOptions, Spawn as _Spawn, Child as _Child, Crash as _Crash, Bus as _Bus, BusDetachedHandler as _BusDetachedHandler, BusMessageHandler as _BusMessageHandler, Service as _Service, ServiceCloseHandler as _ServiceCloseHandler, ServiceMessageHandler as _ServiceMessageHandler, Session as _Session, SessionDetachedHandler as _SessionDetachedHandler, Script as _Script, ScriptDestroyedHandler as _ScriptDestroyedHandler, ScriptMessageHandler as _ScriptMessageHandler, PortalMembership as _PortalMembership, PackageManager as _PackageManager, PackageManagerInstallProgressHandler as _PackageManagerInstallProgressHandler, Package as _Package, PackageSearchOptions as _PackageSearchOptions, PackageSearchResult as _PackageSearchResult, PackageInstallOptions as _PackageInstallOptions, PackageInstallResult as _PackageInstallResult, ControlService as _ControlService, ControlServiceOptions as _ControlServiceOptions, PortalService as _PortalService, PortalNodeConnectedHandler as _PortalNodeConnectedHandler, PortalNodeJoinedHandler as _PortalNodeJoinedHandler, PortalNodeLeftHandler as _PortalNodeLeftHandler, PortalNodeDisconnectedHandler as _PortalNodeDisconnectedHandler, PortalControllerConnectedHandler as _PortalControllerConnectedHandler, PortalControllerDisconnectedHandler as _PortalControllerDisconnectedHandler, PortalAuthenticatedHandler as _PortalAuthenticatedHandler, PortalSubscribeHandler as _PortalSubscribeHandler, PortalMessageHandler as _PortalMessageHandler, FileMonitor as _FileMonitor, FileMonitorChangeHandler as _FileMonitorChangeHandler, Compiler as _Compiler, CompilerStartingHandler as _CompilerStartingHandler, CompilerFinishedHandler as _CompilerFinishedHandler, CompilerOutputHandler as _CompilerOutputHandler, CompilerDiagnosticsHandler as _CompilerDiagnosticsHandler, CompilerOptions as _CompilerOptions, BuildOptions as _BuildOptions, WatchOptions as _WatchOptions, StaticAuthenticationService as _StaticAuthenticationService, FrontmostQueryOptions as _FrontmostQueryOptions, ApplicationQueryOptions as _ApplicationQueryOptions, ProcessQueryOptions as _ProcessQueryOptions, SessionOptions as _SessionOptions, ScriptOptions as _ScriptOptions, SnapshotOptions as _SnapshotOptions, PortalOptions as _PortalOptions, PeerOptions as _PeerOptions, Relay as _Relay, EndpointParameters as _EndpointParameters, AuthenticationService as _AuthenticationService, AbstractAuthenticationService as _AbstractAuthenticationService, BaseObject as _BaseObject, Cancellable as _Cancellable, CancelledHandler as _CancelledHandler, IOStream as _IOStream, InputStream as _InputStream, OutputStream as _OutputStream, InetSocketAddress as _InetSocketAddress, InetAddress as _InetAddress, UnixSocketAddress as _UnixSocketAddress, BaseSocketAddress as _BaseSocketAddress, SocketAddressEnumerator as _SocketAddressEnumerator, SocketConnectable as _SocketConnectable, Runtime as _Runtime, DeviceType as _DeviceType, PackageInstallPhase as _PackageInstallPhase, PackageRole as _PackageRole, OutputFormat as _OutputFormat, BundleFormat as _BundleFormat, TypeCheckMode as _TypeCheckMode, SourceMaps as _SourceMaps, JsCompression as _JsCompression, GadgetBreakpointAction as _GadgetBreakpointAction, Realm as _Realm, SessionDetachReason as _SessionDetachReason, Scope as _Scope, Stdio as _Stdio, ChildOrigin as _ChildOrigin, SnapshotTransport as _SnapshotTransport, ScriptRuntime as _ScriptRuntime, RelayKind as _RelayKind, FileMonitorEvent as _FileMonitorEvent, SocketFamily as _SocketFamily, UnixSocketAddressType as _UnixSocketAddressType, TargetProcess as _TargetProcess, ProcessID as _ProcessID, InjecteeID as _InjecteeID, FileDescriptor as _FileDescriptor, ProcessName as _ProcessName, SystemParameters as _SystemParameters, SystemInterface as _SystemInterface, NetworkInterface as _NetworkInterface, CellularInterface as _CellularInterface, SpawnOptions as _SpawnOptions, RelayProperties as _RelayProperties, Message as _Message, MessageType as _MessageType, SendMessage as _SendMessage, ErrorMessage as _ErrorMessage, ScriptLogHandler as _ScriptLogHandler, ScriptExports as _ScriptExports, LogLevel as _LogLevel, EnableDebuggerOptions as _EnableDebuggerOptions, PortalServiceOptions as _PortalServiceOptions, PortalConnectionId as _PortalConnectionId, PortalConnectionTag as _PortalConnectionTag, EndpointParametersSubset as _EndpointParametersSubset, AuthenticationScheme as _AuthenticationScheme, TokenAuthenticationScheme as _TokenAuthenticationScheme, CallbackAuthenticationScheme as _CallbackAuthenticationScheme, AuthenticationCallback as _AuthenticationCallback, AuthenticatedSessionInfo as _AuthenticatedSessionInfo, SocketAddress as _SocketAddress, IPV4SocketAddress as _IPV4SocketAddress, IPV6SocketAddress as _IPV6SocketAddress, AnonymousUnixSocketAddress as _AnonymousUnixSocketAddress, PathUnixSocketAddress as _PathUnixSocketAddress, AbstractUnixSocketAddress as _AbstractUnixSocketAddress, Variant as _Variant, VariantValue as _VariantValue, VariantDict as _VariantDict } from "./frida_binding.d.ts";
export declare const MessageType: typeof _MessageType;
export declare const LogLevel: typeof _LogLevel;
export declare const DeviceManager: typeof _DeviceManager, Device: typeof _Device, RemoteDeviceOptions: _RemoteDeviceOptions, Application: typeof _Application, Process: typeof _Process, ProcessMatchOptions: _ProcessMatchOptions, RawSpawnOptions: _RawSpawnOptions, Spawn: typeof _Spawn, Child: typeof _Child, Crash: typeof _Crash, Bus: typeof _Bus, Service: typeof _Service, Session: typeof _Session, Script: typeof _Script, PortalMembership: typeof _PortalMembership, PackageManager: typeof _PackageManager, Package: typeof _Package, PackageSearchOptions: _PackageSearchOptions, PackageSearchResult: typeof _PackageSearchResult, PackageInstallOptions: _PackageInstallOptions, PackageInstallResult: typeof _PackageInstallResult, ControlService: typeof _ControlService, ControlServiceOptions: _ControlServiceOptions, PortalService: typeof _PortalService, FileMonitor: typeof _FileMonitor, Compiler: typeof _Compiler, CompilerOptions: _CompilerOptions, BuildOptions: _BuildOptions, WatchOptions: _WatchOptions, StaticAuthenticationService: typeof _StaticAuthenticationService, FrontmostQueryOptions: _FrontmostQueryOptions, ApplicationQueryOptions: _ApplicationQueryOptions, ProcessQueryOptions: _ProcessQueryOptions, SessionOptions: _SessionOptions, ScriptOptions: _ScriptOptions, SnapshotOptions: _SnapshotOptions, PortalOptions: _PortalOptions, PeerOptions: _PeerOptions, Relay: typeof _Relay, EndpointParameters: typeof _EndpointParameters, AbstractAuthenticationService: typeof _AbstractAuthenticationService, BaseObject: typeof _BaseObject, Cancellable: typeof _Cancellable, IOStream: typeof _IOStream, InputStream: typeof _InputStream, OutputStream: typeof _OutputStream, InetSocketAddress: typeof _InetSocketAddress, InetAddress: typeof _InetAddress, UnixSocketAddress: typeof _UnixSocketAddress, BaseSocketAddress: typeof _BaseSocketAddress, SocketAddressEnumerator: typeof _SocketAddressEnumerator, Runtime: typeof _Runtime, DeviceType: typeof _DeviceType, PackageInstallPhase: typeof _PackageInstallPhase, PackageRole: typeof _PackageRole, OutputFormat: typeof _OutputFormat, BundleFormat: typeof _BundleFormat, TypeCheckMode: typeof _TypeCheckMode, SourceMaps: typeof _SourceMaps, JsCompression: typeof _JsCompression, GadgetBreakpointAction: typeof _GadgetBreakpointAction, Realm: typeof _Realm, SessionDetachReason: typeof _SessionDetachReason, Scope: typeof _Scope, Stdio: typeof _Stdio, ChildOrigin: typeof _ChildOrigin, SnapshotTransport: typeof _SnapshotTransport, ScriptRuntime: typeof _ScriptRuntime, RelayKind: typeof _RelayKind, FileMonitorEvent: typeof _FileMonitorEvent, SocketFamily: typeof _SocketFamily, UnixSocketAddressType: typeof _UnixSocketAddressType;
declare const frida: {
    readonly DeviceManager: typeof _DeviceManager;
    readonly Device: typeof _Device;
    readonly RemoteDeviceOptions: _RemoteDeviceOptions;
    readonly Application: typeof _Application;
    readonly Process: typeof _Process;
    readonly ProcessMatchOptions: _ProcessMatchOptions;
    readonly RawSpawnOptions: _RawSpawnOptions;
    readonly Spawn: typeof _Spawn;
    readonly Child: typeof _Child;
    readonly Crash: typeof _Crash;
    readonly Bus: typeof _Bus;
    readonly Service: typeof _Service;
    readonly Session: typeof _Session;
    readonly Script: typeof _Script;
    readonly PortalMembership: typeof _PortalMembership;
    readonly PackageManager: typeof _PackageManager;
    readonly Package: typeof _Package;
    readonly PackageSearchOptions: _PackageSearchOptions;
    readonly PackageSearchResult: typeof _PackageSearchResult;
    readonly PackageInstallOptions: _PackageInstallOptions;
    readonly PackageInstallResult: typeof _PackageInstallResult;
    readonly ControlService: typeof _ControlService;
    readonly ControlServiceOptions: _ControlServiceOptions;
    readonly PortalService: typeof _PortalService;
    readonly FileMonitor: typeof _FileMonitor;
    readonly Compiler: typeof _Compiler;
    readonly CompilerOptions: _CompilerOptions;
    readonly BuildOptions: _BuildOptions;
    readonly WatchOptions: _WatchOptions;
    readonly StaticAuthenticationService: typeof _StaticAuthenticationService;
    readonly FrontmostQueryOptions: _FrontmostQueryOptions;
    readonly ApplicationQueryOptions: _ApplicationQueryOptions;
    readonly ProcessQueryOptions: _ProcessQueryOptions;
    readonly SessionOptions: _SessionOptions;
    readonly ScriptOptions: _ScriptOptions;
    readonly SnapshotOptions: _SnapshotOptions;
    readonly PortalOptions: _PortalOptions;
    readonly PeerOptions: _PeerOptions;
    readonly Relay: typeof _Relay;
    readonly EndpointParameters: typeof _EndpointParameters;
    readonly AbstractAuthenticationService: typeof _AbstractAuthenticationService;
    readonly BaseObject: typeof _BaseObject;
    readonly Cancellable: typeof _Cancellable;
    readonly IOStream: typeof _IOStream;
    readonly InputStream: typeof _InputStream;
    readonly OutputStream: typeof _OutputStream;
    readonly InetSocketAddress: typeof _InetSocketAddress;
    readonly InetAddress: typeof _InetAddress;
    readonly UnixSocketAddress: typeof _UnixSocketAddress;
    readonly BaseSocketAddress: typeof _BaseSocketAddress;
    readonly SocketAddressEnumerator: typeof _SocketAddressEnumerator;
    readonly Runtime: typeof _Runtime;
    readonly DeviceType: typeof _DeviceType;
    readonly PackageInstallPhase: typeof _PackageInstallPhase;
    readonly PackageRole: typeof _PackageRole;
    readonly OutputFormat: typeof _OutputFormat;
    readonly BundleFormat: typeof _BundleFormat;
    readonly TypeCheckMode: typeof _TypeCheckMode;
    readonly SourceMaps: typeof _SourceMaps;
    readonly JsCompression: typeof _JsCompression;
    readonly GadgetBreakpointAction: typeof _GadgetBreakpointAction;
    readonly Realm: typeof _Realm;
    readonly SessionDetachReason: typeof _SessionDetachReason;
    readonly Scope: typeof _Scope;
    readonly Stdio: typeof _Stdio;
    readonly ChildOrigin: typeof _ChildOrigin;
    readonly SnapshotTransport: typeof _SnapshotTransport;
    readonly ScriptRuntime: typeof _ScriptRuntime;
    readonly RelayKind: typeof _RelayKind;
    readonly FileMonitorEvent: typeof _FileMonitorEvent;
    readonly SocketFamily: typeof _SocketFamily;
    readonly UnixSocketAddressType: typeof _UnixSocketAddressType;
    readonly MessageType: typeof _MessageType;
    readonly LogLevel: typeof _LogLevel;
    readonly querySystemParameters: typeof querySystemParameters;
    readonly spawn: typeof spawn;
    readonly resume: typeof resume;
    readonly kill: typeof kill;
    readonly attach: typeof attach;
    readonly injectLibraryFile: typeof injectLibraryFile;
    readonly injectLibraryBlob: typeof injectLibraryBlob;
    readonly enumerateDevices: typeof enumerateDevices;
    readonly getDeviceManager: typeof getDeviceManager;
    readonly getLocalDevice: typeof getLocalDevice;
    readonly getRemoteDevice: typeof getRemoteDevice;
    readonly getUsbDevice: typeof getUsbDevice;
    readonly getDevice: typeof getDevice;
};
export default frida;
declare namespace frida {
    type DeviceManager = _DeviceManager;
    type DeviceAddedHandler = _DeviceAddedHandler;
    type DeviceRemovedHandler = _DeviceRemovedHandler;
    type DeviceChangedHandler = _DeviceChangedHandler;
    type Device = _Device;
    type SpawnAddedHandler = _SpawnAddedHandler;
    type SpawnRemovedHandler = _SpawnRemovedHandler;
    type ChildAddedHandler = _ChildAddedHandler;
    type ChildRemovedHandler = _ChildRemovedHandler;
    type ProcessCrashedHandler = _ProcessCrashedHandler;
    type OutputHandler = _OutputHandler;
    type UninjectedHandler = _UninjectedHandler;
    type DeviceLostHandler = _DeviceLostHandler;
    type RemoteDeviceOptions = _RemoteDeviceOptions;
    type Application = _Application;
    type Process = _Process;
    type ProcessMatchOptions = _ProcessMatchOptions;
    type RawSpawnOptions = _RawSpawnOptions;
    type Spawn = _Spawn;
    type Child = _Child;
    type Crash = _Crash;
    type Bus = _Bus;
    type BusDetachedHandler = _BusDetachedHandler;
    type BusMessageHandler = _BusMessageHandler;
    type Service = _Service;
    type ServiceCloseHandler = _ServiceCloseHandler;
    type ServiceMessageHandler = _ServiceMessageHandler;
    type Session = _Session;
    type SessionDetachedHandler = _SessionDetachedHandler;
    type Script = _Script;
    type ScriptDestroyedHandler = _ScriptDestroyedHandler;
    type ScriptMessageHandler = _ScriptMessageHandler;
    type PortalMembership = _PortalMembership;
    type PackageManager = _PackageManager;
    type PackageManagerInstallProgressHandler = _PackageManagerInstallProgressHandler;
    type Package = _Package;
    type PackageSearchOptions = _PackageSearchOptions;
    type PackageSearchResult = _PackageSearchResult;
    type PackageInstallOptions = _PackageInstallOptions;
    type PackageInstallResult = _PackageInstallResult;
    type ControlService = _ControlService;
    type ControlServiceOptions = _ControlServiceOptions;
    type PortalService = _PortalService;
    type PortalNodeConnectedHandler = _PortalNodeConnectedHandler;
    type PortalNodeJoinedHandler = _PortalNodeJoinedHandler;
    type PortalNodeLeftHandler = _PortalNodeLeftHandler;
    type PortalNodeDisconnectedHandler = _PortalNodeDisconnectedHandler;
    type PortalControllerConnectedHandler = _PortalControllerConnectedHandler;
    type PortalControllerDisconnectedHandler = _PortalControllerDisconnectedHandler;
    type PortalAuthenticatedHandler = _PortalAuthenticatedHandler;
    type PortalSubscribeHandler = _PortalSubscribeHandler;
    type PortalMessageHandler = _PortalMessageHandler;
    type FileMonitor = _FileMonitor;
    type FileMonitorChangeHandler = _FileMonitorChangeHandler;
    type Compiler = _Compiler;
    type CompilerStartingHandler = _CompilerStartingHandler;
    type CompilerFinishedHandler = _CompilerFinishedHandler;
    type CompilerOutputHandler = _CompilerOutputHandler;
    type CompilerDiagnosticsHandler = _CompilerDiagnosticsHandler;
    type CompilerOptions = _CompilerOptions;
    type BuildOptions = _BuildOptions;
    type WatchOptions = _WatchOptions;
    type StaticAuthenticationService = _StaticAuthenticationService;
    type FrontmostQueryOptions = _FrontmostQueryOptions;
    type ApplicationQueryOptions = _ApplicationQueryOptions;
    type ProcessQueryOptions = _ProcessQueryOptions;
    type SessionOptions = _SessionOptions;
    type ScriptOptions = _ScriptOptions;
    type SnapshotOptions = _SnapshotOptions;
    type PortalOptions = _PortalOptions;
    type PeerOptions = _PeerOptions;
    type Relay = _Relay;
    type EndpointParameters = _EndpointParameters;
    type AuthenticationService = _AuthenticationService;
    type AbstractAuthenticationService = _AbstractAuthenticationService;
    type BaseObject = _BaseObject;
    type Cancellable = _Cancellable;
    type CancelledHandler = _CancelledHandler;
    type IOStream = _IOStream;
    type InputStream = _InputStream;
    type OutputStream = _OutputStream;
    type InetSocketAddress = _InetSocketAddress;
    type InetAddress = _InetAddress;
    type UnixSocketAddress = _UnixSocketAddress;
    type BaseSocketAddress = _BaseSocketAddress;
    type SocketAddressEnumerator = _SocketAddressEnumerator;
    type SocketConnectable = _SocketConnectable;
    type Runtime = _Runtime;
    type DeviceType = _DeviceType;
    type PackageInstallPhase = _PackageInstallPhase;
    type PackageRole = _PackageRole;
    type OutputFormat = _OutputFormat;
    type BundleFormat = _BundleFormat;
    type TypeCheckMode = _TypeCheckMode;
    type SourceMaps = _SourceMaps;
    type JsCompression = _JsCompression;
    type GadgetBreakpointAction = _GadgetBreakpointAction;
    type Realm = _Realm;
    type SessionDetachReason = _SessionDetachReason;
    type Scope = _Scope;
    type Stdio = _Stdio;
    type ChildOrigin = _ChildOrigin;
    type SnapshotTransport = _SnapshotTransport;
    type ScriptRuntime = _ScriptRuntime;
    type RelayKind = _RelayKind;
    type FileMonitorEvent = _FileMonitorEvent;
    type SocketFamily = _SocketFamily;
    type UnixSocketAddressType = _UnixSocketAddressType;
    type TargetProcess = _TargetProcess;
    type ProcessID = _ProcessID;
    type InjecteeID = _InjecteeID;
    type FileDescriptor = _FileDescriptor;
    type ProcessName = _ProcessName;
    type SystemParameters = _SystemParameters;
    type SystemInterface = _SystemInterface;
    type NetworkInterface = _NetworkInterface;
    type CellularInterface = _CellularInterface;
    type SpawnOptions = _SpawnOptions;
    type RelayProperties = _RelayProperties;
    type Message = _Message;
    type MessageType = _MessageType;
    type SendMessage = _SendMessage;
    type ErrorMessage = _ErrorMessage;
    type ScriptLogHandler = _ScriptLogHandler;
    type ScriptExports = _ScriptExports;
    type LogLevel = _LogLevel;
    type EnableDebuggerOptions = _EnableDebuggerOptions;
    type PortalServiceOptions = _PortalServiceOptions;
    type PortalConnectionId = _PortalConnectionId;
    type PortalConnectionTag = _PortalConnectionTag;
    type EndpointParametersSubset = _EndpointParametersSubset;
    type AuthenticationScheme = _AuthenticationScheme;
    type TokenAuthenticationScheme = _TokenAuthenticationScheme;
    type CallbackAuthenticationScheme = _CallbackAuthenticationScheme;
    type AuthenticationCallback = _AuthenticationCallback;
    type AuthenticatedSessionInfo = _AuthenticatedSessionInfo;
    type SocketAddress = _SocketAddress;
    type IPV4SocketAddress = _IPV4SocketAddress;
    type IPV6SocketAddress = _IPV6SocketAddress;
    type AnonymousUnixSocketAddress = _AnonymousUnixSocketAddress;
    type PathUnixSocketAddress = _PathUnixSocketAddress;
    type AbstractUnixSocketAddress = _AbstractUnixSocketAddress;
    type Variant = _Variant;
    type VariantValue = _VariantValue;
    type VariantDict = _VariantDict;
}
export type DeviceManager = _DeviceManager;
export type DeviceAddedHandler = _DeviceAddedHandler;
export type DeviceRemovedHandler = _DeviceRemovedHandler;
export type DeviceChangedHandler = _DeviceChangedHandler;
export type Device = _Device;
export type SpawnAddedHandler = _SpawnAddedHandler;
export type SpawnRemovedHandler = _SpawnRemovedHandler;
export type ChildAddedHandler = _ChildAddedHandler;
export type ChildRemovedHandler = _ChildRemovedHandler;
export type ProcessCrashedHandler = _ProcessCrashedHandler;
export type OutputHandler = _OutputHandler;
export type UninjectedHandler = _UninjectedHandler;
export type DeviceLostHandler = _DeviceLostHandler;
export type RemoteDeviceOptions = _RemoteDeviceOptions;
export type Application = _Application;
export type Process = _Process;
export type ProcessMatchOptions = _ProcessMatchOptions;
export type RawSpawnOptions = _RawSpawnOptions;
export type Spawn = _Spawn;
export type Child = _Child;
export type Crash = _Crash;
export type Bus = _Bus;
export type BusDetachedHandler = _BusDetachedHandler;
export type BusMessageHandler = _BusMessageHandler;
export type Service = _Service;
export type ServiceCloseHandler = _ServiceCloseHandler;
export type ServiceMessageHandler = _ServiceMessageHandler;
export type Session = _Session;
export type SessionDetachedHandler = _SessionDetachedHandler;
export type Script = _Script;
export type ScriptDestroyedHandler = _ScriptDestroyedHandler;
export type ScriptMessageHandler = _ScriptMessageHandler;
export type PortalMembership = _PortalMembership;
export type PackageManager = _PackageManager;
export type PackageManagerInstallProgressHandler = _PackageManagerInstallProgressHandler;
export type Package = _Package;
export type PackageSearchOptions = _PackageSearchOptions;
export type PackageSearchResult = _PackageSearchResult;
export type PackageInstallOptions = _PackageInstallOptions;
export type PackageInstallResult = _PackageInstallResult;
export type ControlService = _ControlService;
export type ControlServiceOptions = _ControlServiceOptions;
export type PortalService = _PortalService;
export type PortalNodeConnectedHandler = _PortalNodeConnectedHandler;
export type PortalNodeJoinedHandler = _PortalNodeJoinedHandler;
export type PortalNodeLeftHandler = _PortalNodeLeftHandler;
export type PortalNodeDisconnectedHandler = _PortalNodeDisconnectedHandler;
export type PortalControllerConnectedHandler = _PortalControllerConnectedHandler;
export type PortalControllerDisconnectedHandler = _PortalControllerDisconnectedHandler;
export type PortalAuthenticatedHandler = _PortalAuthenticatedHandler;
export type PortalSubscribeHandler = _PortalSubscribeHandler;
export type PortalMessageHandler = _PortalMessageHandler;
export type FileMonitor = _FileMonitor;
export type FileMonitorChangeHandler = _FileMonitorChangeHandler;
export type Compiler = _Compiler;
export type CompilerStartingHandler = _CompilerStartingHandler;
export type CompilerFinishedHandler = _CompilerFinishedHandler;
export type CompilerOutputHandler = _CompilerOutputHandler;
export type CompilerDiagnosticsHandler = _CompilerDiagnosticsHandler;
export type CompilerOptions = _CompilerOptions;
export type BuildOptions = _BuildOptions;
export type WatchOptions = _WatchOptions;
export type StaticAuthenticationService = _StaticAuthenticationService;
export type FrontmostQueryOptions = _FrontmostQueryOptions;
export type ApplicationQueryOptions = _ApplicationQueryOptions;
export type ProcessQueryOptions = _ProcessQueryOptions;
export type SessionOptions = _SessionOptions;
export type ScriptOptions = _ScriptOptions;
export type SnapshotOptions = _SnapshotOptions;
export type PortalOptions = _PortalOptions;
export type PeerOptions = _PeerOptions;
export type Relay = _Relay;
export type EndpointParameters = _EndpointParameters;
export type AuthenticationService = _AuthenticationService;
export type AbstractAuthenticationService = _AbstractAuthenticationService;
export type BaseObject = _BaseObject;
export type Cancellable = _Cancellable;
export type CancelledHandler = _CancelledHandler;
export type IOStream = _IOStream;
export type InputStream = _InputStream;
export type OutputStream = _OutputStream;
export type InetSocketAddress = _InetSocketAddress;
export type InetAddress = _InetAddress;
export type UnixSocketAddress = _UnixSocketAddress;
export type BaseSocketAddress = _BaseSocketAddress;
export type SocketAddressEnumerator = _SocketAddressEnumerator;
export type SocketConnectable = _SocketConnectable;
export type Runtime = _Runtime;
export type DeviceType = _DeviceType;
export type PackageInstallPhase = _PackageInstallPhase;
export type PackageRole = _PackageRole;
export type OutputFormat = _OutputFormat;
export type BundleFormat = _BundleFormat;
export type TypeCheckMode = _TypeCheckMode;
export type SourceMaps = _SourceMaps;
export type JsCompression = _JsCompression;
export type GadgetBreakpointAction = _GadgetBreakpointAction;
export type Realm = _Realm;
export type SessionDetachReason = _SessionDetachReason;
export type Scope = _Scope;
export type Stdio = _Stdio;
export type ChildOrigin = _ChildOrigin;
export type SnapshotTransport = _SnapshotTransport;
export type ScriptRuntime = _ScriptRuntime;
export type RelayKind = _RelayKind;
export type FileMonitorEvent = _FileMonitorEvent;
export type SocketFamily = _SocketFamily;
export type UnixSocketAddressType = _UnixSocketAddressType;
export type TargetProcess = _TargetProcess;
export type ProcessID = _ProcessID;
export type InjecteeID = _InjecteeID;
export type FileDescriptor = _FileDescriptor;
export type ProcessName = _ProcessName;
export type SystemParameters = _SystemParameters;
export type SystemInterface = _SystemInterface;
export type NetworkInterface = _NetworkInterface;
export type CellularInterface = _CellularInterface;
export type SpawnOptions = _SpawnOptions;
export type RelayProperties = _RelayProperties;
export type Message = _Message;
export type MessageType = _MessageType;
export type SendMessage = _SendMessage;
export type ErrorMessage = _ErrorMessage;
export type ScriptLogHandler = _ScriptLogHandler;
export type ScriptExports = _ScriptExports;
export type LogLevel = _LogLevel;
export type EnableDebuggerOptions = _EnableDebuggerOptions;
export type PortalServiceOptions = _PortalServiceOptions;
export type PortalConnectionId = _PortalConnectionId;
export type PortalConnectionTag = _PortalConnectionTag;
export type EndpointParametersSubset = _EndpointParametersSubset;
export type AuthenticationScheme = _AuthenticationScheme;
export type TokenAuthenticationScheme = _TokenAuthenticationScheme;
export type CallbackAuthenticationScheme = _CallbackAuthenticationScheme;
export type AuthenticationCallback = _AuthenticationCallback;
export type AuthenticatedSessionInfo = _AuthenticatedSessionInfo;
export type SocketAddress = _SocketAddress;
export type IPV4SocketAddress = _IPV4SocketAddress;
export type IPV6SocketAddress = _IPV6SocketAddress;
export type AnonymousUnixSocketAddress = _AnonymousUnixSocketAddress;
export type PathUnixSocketAddress = _PathUnixSocketAddress;
export type AbstractUnixSocketAddress = _AbstractUnixSocketAddress;
export type Variant = _Variant;
export type VariantValue = _VariantValue;
export type VariantDict = _VariantDict;
export declare function querySystemParameters(cancellable?: Cancellable | null): Promise<SystemParameters>;
export declare function spawn(program: string | string[], options?: SpawnOptions, cancellable?: Cancellable | null): Promise<number>;
export declare function resume(target: TargetProcess, cancellable?: Cancellable | null): Promise<void>;
export declare function kill(target: TargetProcess, cancellable?: Cancellable | null): Promise<void>;
export declare function attach(target: TargetProcess, options?: SessionOptions, cancellable?: Cancellable | null): Promise<Session>;
export declare function injectLibraryFile(target: TargetProcess, path: string, entrypoint: string, data: string, cancellable?: Cancellable | null): Promise<number>;
export declare function injectLibraryBlob(target: TargetProcess, blob: Buffer, entrypoint: string, data: string, cancellable?: Cancellable | null): Promise<number>;
export declare function enumerateDevices(cancellable?: Cancellable | null): Promise<Device[]>;
export declare function getDeviceManager(): DeviceManager;
export declare function getLocalDevice(cancellable?: Cancellable | null): Promise<Device>;
export declare function getRemoteDevice(cancellable?: Cancellable | null): Promise<Device>;
export declare function getUsbDevice(options?: GetDeviceOptions, cancellable?: Cancellable | null): Promise<Device>;
export declare function getDevice(id: string, options?: GetDeviceOptions, cancellable?: Cancellable | null): Promise<Device>;
export interface GetDeviceOptions {
    timeout?: number | null;
}
