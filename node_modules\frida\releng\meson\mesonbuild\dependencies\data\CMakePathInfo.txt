cmake_minimum_required(VERSION ${CMAKE_MAJOR_VERSION}.${CMAKE_MINOR_VERSION}.${CMAKE_PATCH_VERSION})

set(TMP_PATHS_LIST)
list(APPEND TMP_PATHS_LIST ${CMAKE_PREFIX_PATH})
list(APPEND TMP_PATHS_LIST ${CMAKE_FRAMEWORK_PATH})
list(APPEND TMP_PATHS_LIST ${CMAKE_APPBUNDLE_PATH})
list(APPEND TMP_PATHS_LIST $ENV{CMAKE_PREFIX_PATH})
list(APPEND TMP_PATHS_LIST $ENV{CMAKE_FRAMEWORK_PATH})
list(APPEND TMP_PATHS_LIST $ENV{CMAKE_APPBUNDLE_PATH})
list(APPEND TMP_PATHS_LIST ${CMAKE_SYSTEM_PREFIX_PATH})
list(APPEND TMP_PATHS_LIST ${CMAKE_SYSTEM_FRAMEWORK_PATH})
list(APPEND TMP_PATHS_LIST ${CMAKE_SYSTEM_APPBUNDLE_PATH})

set(LIB_ARCH_LIST)
if(CMAKE_LIBRARY_ARCHITECTURE_REGEX)
  file(GLOB implicit_dirs RELATIVE /lib /lib/*-linux-gnu* )
  foreach(dir ${implicit_dirs})
    if("${dir}" MATCHES "${CMAKE_LIBRARY_ARCHITECTURE_REGEX}")
      list(APPEND LIB_ARCH_LIST "${dir}")
    endif()
  endforeach()
endif()

# "Export" these variables:
set(MESON_ARCH_LIST ${LIB_ARCH_LIST})
set(MESON_PATHS_LIST ${TMP_PATHS_LIST})
set(MESON_CMAKE_ROOT ${CMAKE_ROOT})
set(MESON_CMAKE_SYSROOT ${CMAKE_SYSROOT})
set(MESON_FIND_ROOT_PATH ${CMAKE_FIND_ROOT_PATH})

message(STATUS ${TMP_PATHS_LIST})
