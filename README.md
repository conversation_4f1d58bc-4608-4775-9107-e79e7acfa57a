# Dominations GoodyHut Auto-Complete Script

A Frida script that automatically completes goody hut collections in Dominations using IL2CPP method hooking.

## Features

- **Automatic Collection Completion**: Instantly completes goody hut collections by calling `DoJobBuyThrough()`
- **Real-time Hooking**: Hooks the `StartCollect()` method to trigger auto-completion immediately
- **Periodic Scanning**: Backup scanning mechanism to catch any missed collections
- **Error Handling**: Comprehensive error handling for failed method calls
- **Detailed Logging**: Console output showing all auto-completion activities

## How It Works

1. **Method Hooking**: The script hooks into the `GoodyHutHelper.StartCollect()` method
2. **Instant Completion**: When a collection starts, it automatically calls `<PERSON>JobBuyThrough()` to bypass the timer
3. **Fallback Scanning**: Periodically scans for active collections as a backup mechanism
4. **State Detection**: Uses `IsJobComplete()` and `CanCollect()` to identify collecting goody huts

## Usage

### Prerequisites
- Frida installed on your system
- Android device with Dominations installed
- USB debugging enabled

### Running the Script

1. **Build the script**:
   ```bash
   npm run build
   ```

2. **Spawn the game with the script**:
   ```bash
   npm run spawn
   ```

3. **Or attach to running game**:
   ```bash
   frida -U -n com.nexonm.dominations.adk -l dist/agent.js
   ```

### Alternative Trace Commands

For debugging and analysis:
```bash
# Trace all GoodyHut related methods
npm run trace-goody

# Trace all collection methods
npm run trace-collect

# Trace all methods
npm run trace-all
```

## Script Behavior

### Automatic Completion Flow
1. Player starts a goody hut collection normally
2. Script detects the `StartCollect()` call
3. Original collection starts (brief moment)
4. Script calls `DoJobBuyThrough()` to instantly complete
5. Player receives rewards immediately

### Safety Features
- **Method Validation**: Checks if `DoJobBuyThrough()` is available before calling
- **State Verification**: Verifies collection state before attempting completion
- **Error Recovery**: Continues operation even if individual completions fail
- **Fallback Mechanism**: Periodic scanning ensures no collections are missed

## Console Output

The script provides detailed logging:
```
[+] GoodyHutHelper Auto-Complete Script Loaded
[+] GoodyHutHelper class found: 0x...
[+] Hooking StartCollect method...
[+] StartCollect hook installed successfully!
[*] StartCollect called - triggering original method first
[*] Attempting auto-completion after StartCollect...
[+] Auto-completed collection immediately after start!
```

## Troubleshooting

### Common Issues

1. **"GoodyHutHelper class not found"**
   - Ensure the game is fully loaded before running the script
   - Check that you're using the correct package name

2. **"DoJobBuyThrough call failed"**
   - The goody hut might not support instant completion
   - Premium currency might be required (game limitation)

3. **"Method not found"**
   - Game version might have changed method signatures
   - Try using trace commands to identify current method names

### Debug Mode
Enable additional logging by modifying the script to include more detailed method tracing.

## Important Notes

- This script is for educational purposes and game research
- Use responsibly and in accordance with game terms of service
- The script works by calling existing game methods, not by modifying game data
- Results depend on game's internal logic for premium completions

## File Structure

```
dominations/
├── src/
│   └── index.ts          # Main Frida script
├── dist/
│   └── agent.js          # Compiled JavaScript
├── package.json          # NPM configuration
└── README.md            # This file
```
